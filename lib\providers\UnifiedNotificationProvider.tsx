import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAuthStore } from '../stores/auth-store';
import { oneSignalService } from '../services/OneSignalService';
import { soundService } from '../services/SoundService';
import { ENV } from '../config/env';

interface NotificationContextType {
  isInitialized: boolean;
  hasPermission: boolean;
  playerId: string | null;
  initializeNotifications: () => Promise<void>;
  sendTestNotification: () => Promise<void>;
  testOrderNotification: () => Promise<void>;
  stopOrderAlert: () => Promise<void>;
  testBackgroundSound: () => Promise<boolean>;
  getNotificationStatus: () => Promise<any>;
  manuallyRegisterPlayer: () => Promise<{ success: boolean; playerId?: string; error?: string }>;
  manuallyCreateSubscription: () => Promise<{ success: boolean; playerId?: string; error?: string }>;
  verifyServerIntegration: () => Promise<{
    success: boolean;
    playerId?: string;
    userSession?: any;
    serverStoreSuccess?: boolean;
    serverRemoveSuccess?: boolean;
    error?: string;
  }>;
}

const NotificationContext = createContext<NotificationContextType>({
  isInitialized: false,
  hasPermission: false,
  playerId: null,
  initializeNotifications: async () => {},
  sendTestNotification: async () => {},
  testOrderNotification: async () => {},
  stopOrderAlert: async () => {},
  testBackgroundSound: async () => false,
  getNotificationStatus: async () => ({}),
  manuallyRegisterPlayer: async () => ({ success: false, error: 'Not initialized' }),
  manuallyCreateSubscription: async () => ({ success: false, error: 'Not initialized' }),
  verifyServerIntegration: async () => ({ success: false, error: 'Not initialized' }),
});

interface UnifiedNotificationProviderProps {
  children: ReactNode;
}

export const UnifiedNotificationProvider: React.FC<UnifiedNotificationProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  
  const { user } = useAuthStore();

  // Initialize OneSignal notification service
  const initializeNotifications = async (): Promise<void> => {
    try {
      console.log('🚀 Initializing OneSignal notification system...');

      if (!ENV.ONESIGNAL_APP_ID) {
        console.error('❌ CRITICAL: OneSignal App ID is missing. Check your environment variables (EXPO_PUBLIC_ONESIGNAL_APP_ID).');
        return;
      }

      if (!user) {
        console.warn('⚠️ No user session available for notification initialization');
        return;
      }

      const userSession = {
        access_token: user.access_token,
        restaurant_id: user.restaurant_id,
        branch_id: user.branch_ids?.[0]?.branch_id || '',
        user_id: user.user_id,
      };

      // Initialize OneSignal
      console.log('📱 Initializing OneSignal service...');
      await oneSignalService.initialize(ENV.ONESIGNAL_APP_ID, userSession);
      
      // Get player ID after initialization
      const initialPlayerId = await oneSignalService.getPlayerId();
      setPlayerId(initialPlayerId);
      
      // Check permissions
      const settings = await oneSignalService.getNotificationSettings();
      setHasPermission(settings?.hasPermission || false);

      setIsInitialized(true);
      console.log('✅ OneSignal notification system initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize OneSignal notification system:', error);
      setIsInitialized(false);
    }
  };

  // Send test notification
  const sendTestNotification = async (): Promise<void> => {
    try {
      await oneSignalService.sendTestNotification();
    } catch (error) {
      console.error('❌ Error sending test notification:', error);
    }
  };

  // Test order notification locally
  const testOrderNotification = async (): Promise<void> => {
    try {
      await oneSignalService.testOrderNotification();
    } catch (error) {
      console.error('❌ Error testing order notification:', error);
    }
  };

  // Stop order alert sound
  const stopOrderAlert = async (): Promise<void> => {
    try {
      await soundService.stopAlert();
    } catch (error) {
      console.error('❌ Error stopping order alert:', error);
    }
  };

  // Test background sound
  const testBackgroundSound = async (): Promise<boolean> => {
    try {
      return await soundService.testBackgroundSound();
    } catch (error) {
      console.error('❌ Error testing background sound:', error);
      return false;
    }
  };

  // Manually register player ID with server
  const manuallyRegisterPlayer = async (): Promise<{ success: boolean; playerId?: string; error?: string }> => {
    try {
      return await oneSignalService.manuallyRegisterPlayer();
    } catch (error) {
      console.error('❌ Error manually registering player:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Manually create OneSignal subscription
  const manuallyCreateSubscription = async (): Promise<{ success: boolean; playerId?: string; error?: string }> => {
    try {
      return await oneSignalService.manuallyCreateSubscription();
    } catch (error) {
      console.error('❌ Error manually creating subscription:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Verify server integration
  const verifyServerIntegration = async (): Promise<{
    success: boolean;
    playerId?: string;
    userSession?: any;
    serverStoreSuccess?: boolean;
    serverRemoveSuccess?: boolean;
    error?: string;
  }> => {
    try {
      return await oneSignalService.verifyServerIntegration();
    } catch (error) {
      console.error('❌ Error verifying server integration:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Force retry player registration
  const retryPlayerRegistration = async (): Promise<void> => {
    try {
      console.log('🔄 Forcing player registration retry...');
      await oneSignalService.ensurePlayerRegistration();
    } catch (error) {
      console.error('❌ Error retrying player registration:', error);
    }
  };

  // Get comprehensive notification status
  const getNotificationStatus = async (): Promise<any> => {
    try {
      const oneSignalSettings = await oneSignalService.getNotificationSettings();

      return {
        isInitialized,
        hasPermission,
        playerId,
        oneSignal: oneSignalSettings,
        appState,
        system: 'OneSignal',
        backgroundCapable: true,
        apiIntegration: {
          enabled: true,
          endpoint: '/common/store-notification-id',
          status: playerId ? 'Player ID registered' : 'Waiting for Player ID',
        },
        actions: {
          retryRegistration: retryPlayerRegistration,
          manualRegister: manuallyRegisterPlayer,
          createSubscription: manuallyCreateSubscription,
        },
      };
    } catch (error) {
      console.error('❌ Error getting notification status:', error);
      return {};
    }
  };

  // Set up app state monitoring
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log(`🔄 App state changed: ${appState} -> ${nextAppState}`);
      setAppState(nextAppState);

      // Handle app state changes for notification services
      if (nextAppState === 'active' && isInitialized) {
        // App became active - refresh notification status
        getNotificationStatus().then((status) => {
          console.log('📊 Notification status refreshed:', status);
        });
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState, isInitialized]);

  // Auto-initialize when user is available or handle user switching
  useEffect(() => {
    if (user && !isInitialized) {
      console.log('👤 User available, auto-initializing OneSignal notifications...');
      initializeNotifications();
    } else if (user && isInitialized) {
      // Handle user switching - check if it's a different user
      const currentUserSession = oneSignalService.getUserSession();
      if (currentUserSession && currentUserSession.user_id !== user.user_id) {
        console.log('🔄 Different user detected, switching OneSignal user...');
        console.log('👤 From:', currentUserSession.user_id);
        console.log('👤 To:', user.user_id);
        
        // Use switchUser method for clean user switching
        const newUserSession = {
          access_token: user.access_token,
          restaurant_id: user.restaurant_id,
          branch_id: user.branch_ids?.[0]?.branch_id || '',
          user_id: user.user_id,
        };
        
        oneSignalService.switchUser(newUserSession).then(() => {
          console.log('✅ OneSignal user switched successfully');
          // Refresh the player ID
          oneSignalService.getPlayerId().then((newPlayerId) => {
            setPlayerId(newPlayerId);
          });
        }).catch((error) => {
          console.error('❌ Error switching OneSignal user:', error);
        });
      }
    }
  }, [user, isInitialized]);

  // Clean up on user logout
  useEffect(() => {
    if (!user && isInitialized) {
      console.log('🚪 User logged out, clearing OneSignal data...');
      oneSignalService.clearUserData();
      setIsInitialized(false);
      setHasPermission(false);
      setPlayerId(null);
    }
  }, [user, isInitialized]);

  const contextValue: NotificationContextType = {
    isInitialized,
    hasPermission,
    playerId,
    initializeNotifications,
    sendTestNotification,
    testOrderNotification,
    stopOrderAlert,
    testBackgroundSound,
    getNotificationStatus,
    manuallyRegisterPlayer,
    manuallyCreateSubscription,
    verifyServerIntegration,
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useUnifiedNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useUnifiedNotifications must be used within UnifiedNotificationProvider');
  }
  return context;
};

// Hook for OneSignal-specific operations
export const useOneSignal = () => {
  const context = useUnifiedNotifications();
  return {
    ...context,
    service: oneSignalService,
  };
}; 