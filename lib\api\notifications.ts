import { ENV } from '../config/env';

export interface StoreNotificationIdRequest {
  userId: string;
  deviceId: string;
  isApp: boolean;
}

export interface ApiResponse<T> {
  statusCode: number;
  data: T;
  message?: string;
  error?: string;
}

/**
 * Store user's push notification ID (OneSignal Player ID) on the server
 */
export async function storeNotificationId(
  request: StoreNotificationIdRequest,
  access_token: string
): Promise<void> {
  try {
    console.log('📡 Storing notification ID on server:', { userId: request.userId, deviceId: request.deviceId });

    const response = await fetch(`${ENV.API_BASE_URL}/common/store-notification-id`, {
      method: 'POST',
      headers: {
        'accept': '*/*',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${access_token}`,
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result: ApiResponse<any> = await response.json();
    
    if (result.statusCode && result.statusCode !== 201) {
      console.error('❌ Error storing notification ID:', result);
      throw new Error(result.error || result.message || 'Failed to store notification ID');
    }

    console.log('✅ Notification ID stored successfully on server');
  } catch (error) {
    console.error('❌ Error storing notification ID:', error);
    throw error;
  }
}

/**
 * Remove user's push notification ID from the server (for logout)
 */
export async function removeNotificationId(
  userId: string,
  deviceId: string,
  access_token: string
): Promise<void> {
  try {
    console.log('📡 Removing notification ID from server:', { userId, deviceId });

    // Note: This endpoint might not exist yet, but including for completeness
    const response = await fetch(`${ENV.API_BASE_URL}/common/remove-notification-id`, {
      method: 'DELETE',
      headers: {
        'accept': '*/*',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${access_token}`,
      },
      body: JSON.stringify({ userId, deviceId,isApp:true }),
    });

    if (!response.ok) {
      // Don't throw error for 404 - endpoint might not exist yet
      if (response.status === 404) {
        console.warn('⚠️ Remove notification ID endpoint not found (404) - this is okay if not implemented yet');
        return;
      }
      
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result: ApiResponse<any> = await response.json();
    
    if (result.statusCode && result.statusCode !== 200) {
      throw new Error(result.error || result.message || 'Failed to remove notification ID');
    }

    console.log('✅ Notification ID removed successfully from server');
  } catch (error) {
    console.error('❌ Error removing notification ID:', error);
    // Don't throw error for removal - it's not critical
    console.warn('⚠️ Failed to remove notification ID from server, continuing...');
  }
}

// Export for easy access
export const notificationsApi = {
  storeNotificationId,
  removeNotificationId,
}; 