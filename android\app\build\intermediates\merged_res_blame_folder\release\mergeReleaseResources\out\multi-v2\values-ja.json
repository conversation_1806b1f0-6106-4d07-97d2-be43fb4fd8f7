{"logs": [{"outputFile": "com.cravin.merchant.app-mergeReleaseResources-71:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b1efdd330a6997d521c41b8a19879f0\\transformed\\exoplayer-ui-2.18.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1591,1678,1766,1831,1897,1962,2026,2107,2187,2248,2311,2363,2421,2469,2530,2586,2648,2705,2765,2821,2877,2940,3002,3065,3115,3173,3245,3317", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1586,1673,1761,1826,1892,1957,2021,2102,2182,2243,2306,2358,2416,2464,2525,2581,2643,2700,2760,2816,2872,2935,2997,3060,3110,3168,3240,3312,3361"}, "to": {"startLines": "2,11,15,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,7232,7304,7375,7444,7523,7601,7667,7728,7806,7883,7947,8008,8067,8132,8219,8306,8394,8459,8525,8590,8654,8735,8815,8876,9546,9598,9656,9704,9765,9821,9883,9940,10000,10056,10112,10175,10237,10300,10350,10408,10480,10552", "endLines": "10,14,18,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "371,534,694,7299,7370,7439,7518,7596,7662,7723,7801,7878,7942,8003,8062,8127,8214,8301,8389,8454,8520,8585,8649,8730,8810,8871,8934,9593,9651,9699,9760,9816,9878,9935,9995,10051,10107,10170,10232,10295,10345,10403,10475,10547,10596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "56,57,58,59,60,61,62,239", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3869,3961,4061,4155,4251,4344,4437,18063", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3956,4056,4150,4246,4339,4432,4533,18159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,949,1042,1147,1229,1327,1435,1513,1588,1679,1772,1867,1961,2061,2154,2249,2343,2434,2525,2603,2705,2803,2898,3001,3097,3193,3341,17173", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "944,1037,1142,1224,1322,1430,1508,1583,1674,1767,1862,1956,2056,2149,2244,2338,2429,2520,2598,2700,2798,2893,2996,3092,3188,3336,3433,17247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02a6c65af6aca4fe0b17434c7fe8f02b\\transformed\\play-services-base-18.0.1\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4877,4981,5115,5235,5341,5473,5593,5698,5919,6053,6154,6287,6406,6526,6646,6706,6765", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "4976,5110,5230,5336,5468,5588,5693,5792,6048,6149,6282,6401,6521,6641,6701,6760,6831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4689ac814784dd79f2206f332baa7585\\transformed\\play-services-basement-18.1.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "5797", "endColumns": "121", "endOffsets": "5914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250db4afb8fa7de0d997879e3d4adf3d\\transformed\\media3-exoplayer-1.4.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8939,9004,9064,9129,9191,9265,9324,9404,9481", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "8999,9059,9124,9186,9260,9319,9399,9476,9541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4d7ecf19998ab3515e1b90137c4d08f\\transformed\\biometric-1.1.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,157,242,345,458,572,691,797,913,1009,1128,1247", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "152,237,340,453,567,686,792,908,1004,1123,1242,1350"}, "to": {"startLines": "85,87,145,146,147,148,149,150,151,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6836,7031,10976,11079,11192,11306,11425,11531,11647,11743,11862,11981", "endColumns": "101,84,102,112,113,118,105,115,95,118,118,107", "endOffsets": "6933,7111,11074,11187,11301,11420,11526,11642,11738,11857,11976,12084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd29036936cb81e953b907f3a3c29b2f\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,88,89,141,156,159,161,162,163,164,165,166,167,168,169,170,171,172,173,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "699,3506,3573,3637,3706,3787,4538,4623,4727,7116,7169,10601,12157,12363,12487,12568,12629,12693,12748,12807,12864,12918,13011,13067,13124,13178,13244,13558,13634,13705,13784,13857,13938,14060,14122,14184,14285,14364,14439,14492,14543,14609,14679,14749,14820,14890,14954,15025,15093,15156,15247,15326,15389,15469,15551,15623,15694,15766,15814,15886,15950,16025,16102,16164,16228,16291,16358,16444,16530,16611,16694,16751,16806,17252,17330,17403", "endLines": "22,51,52,53,54,55,63,64,65,88,89,141,156,159,161,162,163,164,165,166,167,168,169,170,171,172,173,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,228,229,230", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "847,3568,3632,3701,3782,3864,4618,4722,4798,7164,7227,10680,12216,12416,12563,12624,12688,12743,12802,12859,12913,13006,13062,13119,13173,13239,13339,13629,13700,13779,13852,13933,14055,14117,14179,14280,14359,14434,14487,14538,14604,14674,14744,14815,14885,14949,15020,15088,15151,15242,15321,15384,15464,15546,15618,15689,15761,15809,15881,15945,16020,16097,16159,16223,16286,16353,16439,16525,16606,16689,16746,16801,16874,17325,17398,17469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,197,265,331,407,473,540,612,687,763,839,906,981,1056,1128,1205,1281,1353,1423,1492,1570,1638,1709,1777", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "118,192,260,326,402,468,535,607,682,758,834,901,976,1051,1123,1200,1276,1348,1418,1487,1565,1633,1704,1772,1843"}, "to": {"startLines": "50,66,155,157,158,160,174,175,176,223,224,225,226,231,232,233,234,235,236,237,238,240,241,242,243", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3438,4803,12089,12221,12287,12421,13344,13411,13483,16879,16955,17031,17098,17474,17549,17621,17698,17774,17846,17916,17985,18164,18232,18303,18371", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "3501,4872,12152,12282,12358,12482,13406,13478,13553,16950,17026,17093,17168,17544,17616,17693,17769,17841,17911,17980,18058,18227,18298,18366,18437"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "86,142,143,144", "startColumns": "4,4,4,4", "startOffsets": "6938,10685,10780,10881", "endColumns": "92,94,100,94", "endOffsets": "7026,10775,10876,10971"}}]}]}