# Cravin Restaurant App

## 🚀 Optimized Background Sync (10-Second Intervals)

This app now features an **optimized background service** that checks for new orders every **10 seconds** using React Query for efficient data management.

### 🔧 New Features

#### 1. **OneSignal Push Notification Service** (`OneSignalService.ts`)
- **10-second intervals** in aggressive mode (configurable)
- **React Query integration** for efficient caching
- **Performance metrics** tracking
- **Smart retry logic** with exponential backoff
- **Health checks** and auto-recovery
- **Battery optimization** with performance modes

#### 2. **React Query Integration**
- **Optimized caching** with 2-second stale time
- **Background sync** with cache invalidation
- **Smart retry policies** for different error types
- **Performance monitoring** and debugging
- **Real-time UI updates** synchronized with background data

#### 3. **Enhanced Order Management**
- `useOptimizedNewOrders()` - Frontend polls every 5 seconds
- `useBackgroundTaskSync()` - Manages background service
- `useBackgroundOrderSync()` - Auto-starts service when authenticated
- Background service polls API every 10 seconds
- Instant notifications with full-screen alerts

### 🎯 Performance Modes

1. **Aggressive Mode** (Default)
   - **10-second** background sync
   - **5-second** frontend polling
   - **Higher battery usage**
   - **Fastest order notifications**

2. **Balanced Mode**
   - **30-second** background sync
   - **Moderate battery usage**
   - **Good balance of speed and efficiency**

3. **Battery Saving Mode**
   - **60-second** background sync
   - **Lower battery usage**
   - **Slower but still effective**

### 📱 Usage

#### Automatic Initialization
The background service starts automatically when the app loads and the user is authenticated.

#### Manual Control
```tsx
import { useBackgroundTaskSync } from '~/lib/hooks/useOrders';

function MyComponent() {
  const {
    status,
    metrics,
    startTask,
    stopTask,
    setPerformanceMode,
    healthCheck,
  } = useBackgroundTaskSync();

  // Start background service
  await startTask.mutateAsync();

  // Change performance mode
  await setPerformanceMode.mutateAsync('aggressive');

  // Health check
  await healthCheck.mutateAsync();
}
```

#### Optimized Order Hooks
```tsx
import { useOptimizedNewOrders } from '~/lib/hooks/useOrders';

function OrdersScreen() {
  const {
    data: orders,
    isLoading,
    error,
    refetch
  } = useOptimizedNewOrders();
  
  // Data automatically syncs every 5 seconds (frontend)
  // Background service syncs every 10 seconds
  // Cache is optimized for real-time updates
}
```

### 🔔 Notification Features

- **Instant alerts** when new orders arrive
- **🔊 CONTINUOUS ALERT SOUNDS** - Sound plays until action is taken
- **Full-screen notifications** on Android
- **Action buttons** (Accept/Reject/View)
- **Custom notification sounds** that play in silent mode
- **High priority** and **bypass DND**
- **Visual sound indicator** with manual stop option
- **Notification history** tracking
- **Auto-stop on user action** (Accept/Reject/Dismiss)

### 🔊 Continuous Alert Sound System

#### How It Works
1. **New Order Detected** → **Sound Starts Playing** (loops continuously)
2. **User Takes Action** (Accept/Reject/Dismiss) → **Sound Stops Immediately**
3. **30-Second Timeout** → **Auto-reject** + **Sound Stops**
4. **Manual Stop** → **Sound Stops** via floating indicator

#### Sound Features
- **Plays in silent mode** - Never miss an order
- **Loops continuously** - Until user action or timeout
- **Visual indicator** - Shows when sound is playing
- **Manual stop button** - Emergency sound control
- **Automatic cleanup** - Prevents sound getting stuck

#### Integration Points
- **Background service** - Starts sound when new orders detected
- **Order modal** - Stops sound on accept/reject/dismiss
- **App layout** - Shows floating sound indicator
- **Settings panel** - Test continuous alerts

### 🧪 Testing & Monitoring

#### Background Service Status
- **Service health** monitoring
- **Performance metrics** (success rate, response times)
- **Execution history** tracking
- **Debug logging** with timestamps

#### Available Controls
- **Send test notifications**
- **🔊 Test continuous alerts** (new!)
- **Health checks**
- **Performance mode switching**
- **Service restart/recovery**
- **Cache invalidation**

### ⚠️ Platform Notes

#### iOS Limitations
- Background sync intervals may be limited by the system
- Actual sync frequency might be longer than configured
- iOS may throttle background tasks to preserve battery

#### Android Optimizations
- **Full-screen notifications** for maximum visibility
- **Doze mode bypass** for critical notifications
- **Heads-up notifications** for urgent orders
- **Background task persistence** across app restarts

### 🔧 Configuration

#### Environment Variables
```bash
# API Base URL for order fetching
API_BASE_URL=https://your-api-endpoint.com

# Enable development debugging
DEV_MODE=true
```

#### Cache Configuration
The React Query client is optimized for real-time updates:
- **Stale time**: 2 seconds (aggressive refresh)
- **Cache time**: 15 minutes (longer retention)
- **Retry logic**: Smart exponential backoff
- **Network mode**: Online-only for accuracy

### 📊 Performance Monitoring

The app tracks detailed metrics:
- **Execution count** and **success rate**
- **Average response times**
- **Error rates** and **retry attempts**
- **Cache hit/miss ratios**
- **Background task health**

### 🚀 Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   - Set up your API endpoints in `lib/config/env.ts`
   - Configure notification permissions

3. **Run the app**:
   ```bash
   npm run dev:android  # For Android
   npm run dev:ios      # For iOS
   ```

4. **Test background sync**:
   - Use the notification settings screen
   - Send test notifications
   - Monitor performance metrics
   - Check background service health

### 📋 Key Files Modified

#### Core Services
- `lib/services/OneSignalService.ts` - OneSignal push notification service for background order notifications
- `lib/hooks/useOrders.ts` - Enhanced React Query hooks
- `lib/query-client.tsx` - Optimized cache configuration

#### UI Components  
- `components/notifications/NotificationSettings.tsx` - Control panel with sound tests
- `components/notifications/OrderNotificationModal.tsx` - Sound stopping on user actions
- `components/notifications/SoundStatusIndicator.tsx` - **NEW** - Floating sound indicator
- `app/_layout.tsx` - Auto-initialization + sound indicator integration
- `app/(tabs)/new-orders.tsx` - Updated to use optimized hooks

The background service now provides **real-time order notifications** with **10-second precision** and **continuous alert sounds** that ensure restaurant staff never miss critical orders. The system maintains excellent performance and battery efficiency while delivering enterprise-level reliability.

## 🎉 **New Continuous Sound Feature Summary**

### ✅ **What Was Added**
- 🔊 **Continuous looping sounds** for new order alerts
- 🛑 **Automatic stop** when user takes action (Accept/Reject/Dismiss)
- 👁️ **Visual sound indicator** with manual stop button
- 🧪 **Test continuous alerts** in notification settings
- 🔇 **Emergency sound control** to prevent stuck alerts
- 📱 **Silent mode support** - plays even when phone is silent

### 🚀 **How It Works**
1. **New order arrives** → Background service detects it
2. **Sound starts playing** → Continuous loop with visual indicator
3. **User sees/hears alert** → Modal appears with action buttons
4. **User takes action** → Sound stops immediately
5. **No action in 30s** → Auto-reject and sound stops

### 🎯 **Benefits**
- **Never miss orders** - Sound continues until acknowledged
- **Immediate feedback** - Sound stops when action is taken
- **User control** - Manual stop option available
- **System reliability** - Automatic cleanup prevents issues
- **Battery efficient** - Smart sound management

The alert system now provides **guaranteed order notification delivery** with **user-controlled continuous sounds**! 🎵
