import { useMemo } from 'react';
import { useColorScheme as useNativewindColorScheme } from 'nativewind';

export function useColorScheme() {
  const { colorScheme, setColorScheme, toggleColorScheme } = useNativewindColorScheme();
  
  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    colorScheme: colorScheme ?? 'dark',
    isDarkColorScheme: colorScheme === 'dark',
    setColorScheme,
    toggleColorScheme,
  }), [colorScheme, setColorScheme, toggleColorScheme]);
}
