{"cli": {"version": ">= 16.13.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"NODE_ENV": "development"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}, "production": {"autoIncrement": true, "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "ios": {"buildConfiguration": "Release"}, "env": {"NODE_ENV": "production"}}, "production-apk": {"extends": "production", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "production", "releaseStatus": "completed"}, "ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}}}}