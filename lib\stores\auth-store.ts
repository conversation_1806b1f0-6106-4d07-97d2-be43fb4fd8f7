import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { createZustandStorage } from '~/lib/utils/storage';
import { authStorage } from '~/lib/utils/auth-storage';
import { oneSignalService } from '~/lib/services/OneSignalService';

export type Branch = {
  branch_id: string;
  branch_name?: string;
};

export type UserSession = {
  access_token: string;
  user_id: string;
  username: string;
  restaurant_id: string;
  branch_ids: Branch[];
  user_type: 'manager' | 'owner' | string;
};

interface AuthState {
  user: UserSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasHydrated: boolean;
  // Actions
  setUser: (user: UserSession) => void;
  logout: () => Promise<void>;
  updateBranches: (branch_ids: Branch[]) => void;
  setHasHydrated: (hasHydrated: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: true, // Start with loading true
      hasHydrated: false,
      setUser: (user) => {
        console.log('Setting user in auth store:', { userId: user.user_id, username: user.username });
        set({ user, isAuthenticated: true });
      },
      logout: async () => {
        console.log('Logging out user from auth store');
        try {
          // 1. Clear OneSignal data first (removes player ID from server)
          await oneSignalService.clearUserData();
          
          // 2. Clear local auth storage
          await authStorage.clearAll();
        } catch (error) {
          console.error('Error clearing auth storage during logout:', error);
        }
        
        // 3. Clear auth state
        set({ user: null, isAuthenticated: false });
      },
      updateBranches: (branch_ids) =>
        set((state) => ({
          user: state.user ? { ...state.user, branch_ids } : null,
        })),
      setHasHydrated: (hasHydrated) => {
        const state = get();
      
        set({ 
          hasHydrated, 
          isLoading: !hasHydrated,
          // Set isAuthenticated to true if user exists after hydration
          isAuthenticated: hasHydrated ? !!state.user : state.isAuthenticated
        });
      },
    }),
    {
      name: 'auth-store',
      storage: createJSONStorage(() => createZustandStorage(true)), // Use secure storage for auth data
      onRehydrateStorage: () => (state, error) => {
        if (error) {
          console.error('Failed to rehydrate auth store:', error);
        } else {
          console.log('Auth store rehydrated successfully:', { 
            user: state?.user ? 'exists' : 'null', 
            isAuthenticated: state?.isAuthenticated 
          });
        }
        // Set hasHydrated to true when rehydration completes
        // This will trigger the setHasHydrated action which will properly set isAuthenticated
        useAuthStore.getState().setHasHydrated(true);
      },
    }
  )
); 