import * as React from 'react';
import { router } from 'expo-router';
import Login from '~/components/Login';
import { useAuth } from '~/lib/hooks/useAuth';
import { View } from 'react-native';

export default function Screen() {
  const { isAuthenticated, isLoading } = useAuth();

  React.useEffect(() => {
    console.log('Auth state changed:', { isAuthenticated, isLoading });
    if (!isLoading && isAuthenticated) {
      console.log('Redirecting authenticated user to dashboard...');
      // Redirect to tabs dashboard if user is already authenticated
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, isLoading]);

  // Show nothing while checking authentication status to prevent flash
  if (isLoading) {
    console.log('Showing loading state...');
    return <View style={{ flex: 1, backgroundColor: '#fff' }} />;
  }

  // Show login screen only if user is not authenticated
  if (!isAuthenticated) {
    console.log('Showing login screen...');
    return <Login />;
  }

  // Return empty view while redirecting
  console.log('Redirecting...');
  return <View style={{ flex: 1, backgroundColor: '#fff' }} />;
}
