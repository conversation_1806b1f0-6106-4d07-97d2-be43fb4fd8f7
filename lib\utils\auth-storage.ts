import { secureStorage } from './storage';
import type { UserSession } from '~/lib/stores/auth-store';

const AUTH_TOKEN_KEY = 'auth_token';
const USER_SESSION_KEY = 'user_session';

/**
 * Utility functions for managing authentication storage
 */
export const authStorage = {
  /**
   * Store authentication token securely
   */
  async setAuthToken(token: string): Promise<void> {
    try {
      await secureStorage.setItem(AUTH_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to store auth token:', error);
      throw error;
    }
  },

  /**
   * Get stored authentication token
   */
  async getAuthToken(): Promise<string | null> {
    try {
      return await secureStorage.getItem(AUTH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  },

  /**
   * Remove authentication token
   */
  async removeAuthToken(): Promise<void> {
    try {
      await secureStorage.removeItem(AUTH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to remove auth token:', error);
      throw error;
    }
  },

  /**
   * Store complete user session securely
   */
  async setUserSession(session: UserSession): Promise<void> {
    try {
      await secureStorage.setItem(USER_SESSION_KEY, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to store user session:', error);
      throw error;
    }
  },

  /**
   * Get stored user session
   */
  async getUserSession(): Promise<UserSession | null> {
    try {
      const sessionData = await secureStorage.getItem(USER_SESSION_KEY);
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Failed to get user session:', error);
      return null;
    }
  },

  /**
   * Remove user session
   */
  async removeUserSession(): Promise<void> {
    try {
      await secureStorage.removeItem(USER_SESSION_KEY);
    } catch (error) {
      console.error('Failed to remove user session:', error);
      throw error;
    }
  },

  /**
   * Clear all authentication data
   */
  async clearAll(): Promise<void> {
    try {
      await Promise.all([
        this.removeAuthToken(),
        this.removeUserSession()
      ]);
    } catch (error) {
      console.error('Failed to clear auth storage:', error);
      throw error;
    }
  }
}; 