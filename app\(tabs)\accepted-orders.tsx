import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  FlatList,
  Modal,
  TextInput,
  Alert,
  Clipboard,
  Share as RNShare,
} from 'react-native';
import { Text } from '~/components/ui/text';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import {
  useAcceptedOrders,
  useMarkOrderReady,
  useCompleteOrder,
  useMarkOrderOutForDelivery,
  useMarkOrderReadyForPickup,
  useMarkOrderPickedUp,
  useDeliveryDrivers,
  useBranchDeliveryStatus,
} from '~/lib/hooks/useOrders';
import { useAuth } from '~/lib/hooks/useAuth';
import { Order } from '~/lib/api/orders';
import { useColorScheme } from '~/lib/useColorScheme';
import { OrdersHeader } from '~/components/food/OrdersHeader';
import { X, Printer, Share as ShareIcon, Copy, ExternalLink } from 'lucide-react-native';
import { printService } from '~/lib/services/PrintService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth >= 768;

// Move constants outside component
const FOOD_THEME_COLORS = {
  primary: '#ff2b2b',
  accent: '#ff2b2b',
} as const;

// Food theme colors with dark mode support - memoized function
const getFoodTheme = (isDark: boolean) => ({
  primary: FOOD_THEME_COLORS.primary,
  primaryBg: isDark ? 'bg-red-600 dark:bg-red-600' : 'bg-red-500',
  primaryDark: isDark ? 'bg-red-700 dark:bg-red-700' : 'bg-red-600',
  primaryLight: isDark ? 'bg-red-900/20 dark:bg-red-900/20' : 'bg-red-50',
  primaryBorder: isDark ? 'border-red-500 dark:border-red-500' : 'border-red-400',
  primaryText: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-600',
  primaryTextDark: isDark ? 'text-red-300 dark:text-red-300' : 'text-red-700',
  accent: FOOD_THEME_COLORS.accent,
});

// Memoized format functions
const formatTimeAgo = (dateString: string) => {
  const now = new Date();
  const orderTime = new Date(dateString);
  const diffMs = now.getTime() - orderTime.getTime();
  const diffMins = Math.floor(diffMs / 60000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours}h ago`;
  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays}d ago`;
};

const formatOrderTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

const getStatusColor = (status: string, isDark: boolean) => {
  switch (status) {
    case 'accepted': return {
      bg: isDark ? 'bg-red-900/30 dark:bg-red-900/30' : 'bg-red-100',
      text: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-800',
      dot: isDark ? 'bg-red-400 dark:bg-red-400' : 'bg-red-500'
    };
    case 'ready':
    case 'ready_for_pickup': return {
      bg: isDark ? 'bg-green-900/30 dark:bg-green-900/30' : 'bg-green-100',
      text: isDark ? 'text-green-400 dark:text-green-400' : 'text-green-800',
      dot: isDark ? 'bg-green-400 dark:bg-green-400' : 'bg-green-500'
    };
    case 'out_for_delivery': return {
      bg: isDark ? 'bg-blue-900/30 dark:bg-blue-900/30' : 'bg-blue-100',
      text: isDark ? 'text-blue-400 dark:text-blue-400' : 'text-blue-800',
      dot: isDark ? 'bg-blue-400 dark:bg-blue-400' : 'bg-blue-500'
    };
    case 'picked_up': return {
      bg: isDark ? 'bg-purple-900/30 dark:bg-purple-900/30' : 'bg-purple-100',
      text: isDark ? 'text-purple-400 dark:text-purple-400' : 'text-purple-800',
      dot: isDark ? 'bg-purple-400 dark:bg-purple-400' : 'bg-purple-500'
    };
    default: return {
      bg: isDark ? 'bg-gray-800 dark:bg-gray-800' : 'bg-gray-100',
      text: isDark ? 'text-gray-400 dark:text-gray-400' : 'text-gray-800',
      dot: isDark ? 'bg-gray-400 dark:bg-gray-400' : 'bg-gray-500'
    };
  }
};

// Memoized OrderCard component
const OrderCardComponent: React.FC<{
  order: Order;
  isOrderProcessing: (orderId: string) => boolean;
  isDark: boolean;
  foodTheme: ReturnType<typeof getFoodTheme>;
  onPress: (order: Order) => void;
  onMarkReady: (orderId: string) => void;
  onCompleteOrder: (orderId: string) => void;
  getOutForDeliveryHandler: (order: Order) => (orderId: string) => void;
  onMarkReadyForPickup: (orderId: string) => void;
  onMarkPickedUp: (orderId: string) => void;
  onPrintBill: (order: Order) => void;
  onShareBill: (order: Order) => void;
  sharingProgress?: string;
  isSharing?: boolean;
}> = ({ order, isOrderProcessing, isDark, foodTheme, onPress, onMarkReady, onCompleteOrder, getOutForDeliveryHandler, onMarkReadyForPickup, onMarkPickedUp, onPrintBill, onShareBill, sharingProgress, isSharing }) => {
  const statusColors = useMemo(() => getStatusColor(order.status, isDark), [order.status, isDark]);
  const timeAgo = useMemo(() => formatTimeAgo(order.ordered_on), [order.ordered_on]);
  const orderTime = useMemo(() => formatOrderTime(order.ordered_on), [order.ordered_on]);
  const totalBill = useMemo(() =>
    ((order as any).bill?.total_bill || order.bill_amount?.total_bill || 0).toFixed(2),
    [order]
  );

  // Check if this specific order is being processed
  const isThisOrderProcessing = useMemo(() => isOrderProcessing(order.order_id), [isOrderProcessing, order.order_id]);

  const handlePress = useCallback(() => onPress(order), [onPress, order]);
  const handleMarkReady = useCallback(() => onMarkReady(order.order_id), [onMarkReady, order.order_id]);
  const handleComplete = useCallback(() => onCompleteOrder(order.order_id), [onCompleteOrder, order.order_id]);

  return (
    <TouchableOpacity
      className="mb-4"
      onPress={handlePress}
      disabled={isThisOrderProcessing}
      activeOpacity={0.7}
    >
      <Card className="shadow-lg border-0 bg-card">
        <CardContent className="p-4">
          {/* Header */}
          <View className="flex-row items-start justify-between mb-3">
            <View className="flex-row items-start flex-1 mr-3">
              <View className={`w-12 h-12 ${order.order_type === 'delivery'
                ? (isDark ? 'bg-red-900/30' : 'bg-red-100')
                : (isDark ? 'bg-red-900/20' : 'bg-red-50')
                } rounded-xl mr-3 items-center justify-center`}>
                <Text className="text-xl">
                  {order.order_type === 'delivery' ? '🚚' : '🍽️'}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="font-bold text-lg text-foreground" numberOfLines={2}>
                  {(order as any).receiver_name || order.customer_name}
                </Text>
                <View className="flex-row items-center mt-1 mb-1">
                  <View className={`w-2 h-2 ${statusColors.dot} rounded-full mr-2`} />
                  <Text className={`text-sm font-medium ${statusColors.text} uppercase tracking-wide`}>
                    {order.status === 'out_for_delivery' ? 'Out For Delivery' :
                      order.status === 'ready_for_pickup' ? 'Ready For Pickup' :
                        order.status === 'picked_up' ? 'Picked Up' : order.status}
                  </Text>
                </View>
                {(order as any).receiver_name && order.customer_name !== (order as any).receiver_name && (
                  <Text className="text-xs text-muted-foreground" numberOfLines={1}>
                    Ordered by: {order.customer_name}
                  </Text>
                )}
              </View>
            </View>
            <View className="items-end">
              <Text className="font-bold text-xl text-foreground">
                AED {totalBill}
              </Text>
              {order.order_type === 'delivery' && ((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge) > 0 && (
                <Text className="text-xs text-muted-foreground">
                  +AED {((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge || 0).toFixed(2)} delivery
                </Text>
              )}
              <Text className="text-sm text-muted-foreground mt-1">
                {orderTime}
              </Text>
            </View>
          </View>

          {/* Order Info */}
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center space-x-3">
              <View className={`px-3 py-1.5 ${order.order_type === 'delivery'
                ? (isDark ? 'bg-red-900/30' : 'bg-red-100')
                : (isDark ? 'bg-red-900/20' : 'bg-red-50')
                } rounded-full`}>
                <Text className={`text-xs font-bold uppercase tracking-wide ${order.order_type === 'delivery'
                  ? (isDark ? 'text-red-300' : 'text-red-700')
                  : (isDark ? 'text-red-400' : 'text-red-600')
                  }`}>
                  {order.order_type}
                </Text>
              </View>

              <View className="flex-row items-center mr-3 ml-3">
                <View className={`w-2 h-2 ${isDark ? 'bg-gray-500' : 'bg-gray-400'} rounded-full mr-2`} />
                <Text className="text-sm text-muted-foreground">
                  {order.cart_items?.length || 0} item{(order.cart_items?.length || 0) !== 1 ? 's' : ''}
                </Text>
              </View>

              <View className="flex-row items-center">
                <View className={`w-2 h-2 ${isDark ? 'bg-blue-500' : 'bg-blue-400'} rounded-full mr-2`} />
                <Text className="text-xs text-muted-foreground uppercase tracking-wide">
                  {timeAgo}
                </Text>
              </View>
            </View>

            <Text className={`text-sm font-bold ${foodTheme.primaryText}`}>
              #{order.order_name}
            </Text>
          </View>

          {/* Action Buttons */}
          <View onStartShouldSetResponder={() => true}>
            {/* Print and Share Bill Buttons - Always visible for accepted orders */}
            <View className="flex-row mb-3 gap-3">
              <TouchableOpacity
                className={`flex-1 flex-row items-center justify-center p-3 ${isDark ? 'bg-gray-800 dark:bg-gray-800' : 'bg-gray-100'} rounded-lg border ${isDark ? 'border-gray-700' : 'border-gray-300'}`}
                onPress={() => onPrintBill(order)}
                activeOpacity={0.7}
              >
                <Printer size={18} className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mr-2`} />
                <Text className={`${isDark ? 'text-gray-300' : 'text-gray-700'} font-medium pl-2`}>
                  Print Bill
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                className={`flex-1 flex-row items-center justify-center p-3 ${isDark ? 'bg-blue-800/30 dark:bg-blue-800/30' : 'bg-blue-100'} rounded-lg border ${isDark ? 'border-blue-700' : 'border-blue-300'}`}
                onPress={() => onShareBill(order)}
                activeOpacity={0.7}
                disabled={isSharing}
              >
                {isSharing ? (
                  <ActivityIndicator size={18} color={isDark ? '#60a5fa' : '#2563eb'} className="mr-2" />
                ) : (
                  <ShareIcon size={18} className={`${isDark ? 'text-blue-400' : 'text-blue-600'} mr-2`} />
                )}
                <Text className={`${isDark ? 'text-blue-300' : 'text-blue-700'} font-medium pl-2`}>
                  {isSharing ? (sharingProgress || 'Sharing...') : 'Share Bill'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* For Pickup Orders: accepted → ready_for_pickup */}
            {order.status === 'accepted' && order.order_type === 'pickup' && (
              <Button
                size="lg"
                className={foodTheme.primaryDark}
                onPress={() => onMarkReadyForPickup(order.order_id)}
                disabled={isThisOrderProcessing}
              >
                <Text className="text-white font-semibold">
                  {isThisOrderProcessing ? 'Marking...' : 'Ready For Pickup'}
                </Text>
              </Button>
            )}

            {/* For Delivery Orders: accepted → out_for_delivery */}
            {order.status === 'accepted' && order.order_type === 'delivery' && (
              <Button
                size="lg"
                className="bg-blue-600 dark:bg-blue-600"
                onPress={() => getOutForDeliveryHandler(order)(order.order_id)}
                disabled={isThisOrderProcessing}
              >
                <Text className="text-white font-semibold">
                  {isThisOrderProcessing ? 'Sending...' : 'Out For Delivery'}
                </Text>
              </Button>
            )}

            {/* For Pickup Orders: ready_for_pickup → picked_up */}
            {order.status === 'ready_for_pickup' && order.order_type === 'pickup' && (
              <Button
                size="lg"
                className="bg-green-600 dark:bg-green-600"
                onPress={() => onMarkPickedUp(order.order_id)}
                disabled={isThisOrderProcessing}
              >
                <Text className="text-white font-semibold">
                  {isThisOrderProcessing ? 'Completing...' : 'Picked Up'}
                </Text>
              </Button>
            )}

            {/* For Delivery Orders: out_for_delivery → completed */}
            {order.status === 'out_for_delivery' && (
              <Button
                size="lg"
                className="bg-green-600 dark:bg-green-600"
                onPress={() => onCompleteOrder(order.order_id)}
                disabled={isThisOrderProcessing}
              >
                <Text className="text-white font-semibold">
                  {isThisOrderProcessing ? 'Completing...' : 'Mark Delivered'}
                </Text>
              </Button>
            )}
          </View>
        </CardContent>
      </Card>
    </TouchableOpacity>
  );
};

OrderCardComponent.displayName = 'OrderCard';
const OrderCard = React.memo(OrderCardComponent);

// Memoized OrderDetails component
const OrderDetailsComponent: React.FC<{
  order: Order | null;
  inModal?: boolean;
  isDark: boolean;
  foodTheme: ReturnType<typeof getFoodTheme>;
  onBack: () => void;
  isOrderProcessing: (orderId: string) => boolean;
  onMarkReady: (orderId: string) => void;
  onCompleteOrder: (orderId: string) => void;
  getOutForDeliveryHandler: (order: Order) => (orderId: string) => void;
  onMarkReadyForPickup: (orderId: string) => void;
  onMarkPickedUp: (orderId: string) => void;
  onPrintBill: (order: Order) => void;
  onShareBill: (order: Order) => void;
  onCopyCustomerInfo: (order: Order) => void;
  onShareCustomerInfo: (order: Order) => void;
  sharingProgress?: string;
  isSharing?: boolean;
}> = ({ order, inModal = false, isDark, foodTheme, onBack, isOrderProcessing, onMarkReady, onCompleteOrder, getOutForDeliveryHandler, onMarkReadyForPickup, onMarkPickedUp, onPrintBill, onShareBill, onCopyCustomerInfo, onShareCustomerInfo, sharingProgress, isSharing }) => {
  if (!order) {
    return (
      <View className="flex-1 items-center justify-center p-8">
        <View className={`w-20 h-20 ${isDark ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
          <Text className="text-3xl">👨‍🍳</Text>
        </View>
        <Text className="text-foreground text-center text-lg font-medium mb-2">
          No Order Selected
        </Text>
        <Text className="text-muted-foreground text-center">
          Select an order from the list to view its details
        </Text>
      </View>
    );
  }

  const statusColors = useMemo(() => getStatusColor(order.status, isDark), [order.status, isDark]);
  const timeAgo = useMemo(() => formatTimeAgo(order.ordered_on), [order.ordered_on]);
  const orderTime = useMemo(() => formatOrderTime(order.ordered_on), [order.ordered_on]);
  const totalBill = useMemo(() =>
    ((order as any).bill?.total_bill || order.bill_amount?.total_bill || 0).toFixed(2),
    [order]
  );
  const deliveryFee = useMemo(() =>
    ((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge || 0).toFixed(2),
    [order]
  );
  const subTotal = useMemo(() =>
    ((order as any).bill?.sub_total || order.bill_amount?.sub_total || 0).toFixed(2),
    [order]
  );

  // Check if this specific order is being processed
  const isThisOrderProcessing = useMemo(() => isOrderProcessing(order.order_id), [isOrderProcessing, order.order_id]);

  const handleComplete = useCallback(() => {
    onCompleteOrder(order.order_id)
    onBack()
  }, [onCompleteOrder, order.order_id, onBack]);
  const handleMarkReadyForPickup = useCallback(() => {
    onMarkReadyForPickup(order.order_id)
    onBack()
  }, [onMarkReadyForPickup, order.order_id, onBack]);
  const handleMarkPickedUp = useCallback(() => {
    onMarkPickedUp(order.order_id)
    onBack()
  }, [onMarkPickedUp, order.order_id, onBack]);
  const handleOutForDelivery = useCallback(() => {
    getOutForDeliveryHandler(order)(order.order_id)
    onBack()
  }, [getOutForDeliveryHandler, order]);

  return (
    <View style={{ maxHeight: inModal ? screenHeight * 0.85 : undefined }}>
      {inModal && (
        <View className="p-4 border-b border-border flex-row items-center justify-between bg-card">
          <Text className="text-xl font-bold text-foreground">Order Details</Text>
          <TouchableOpacity onPress={onBack} className="p-2 rounded-full bg-muted">
            <X size={20} className="text-muted-foreground" />
          </TouchableOpacity>
        </View>
      )}
      <ScrollView showsVerticalScrollIndicator={false}>
        {!isTablet && !inModal && (
          <View className={`flex-row items-center p-4 ${foodTheme.primaryBg} ${isDark ? 'border-red-700' : 'border-red-300'
            } border-b`}>
            <TouchableOpacity
              onPress={onBack}
              className="w-10 h-10 bg-white/20 rounded-full items-center justify-center mr-4"
            >
              <Text className="text-lg text-white">←</Text>
            </TouchableOpacity>
            <Text className="text-xl font-bold text-white">Order Details</Text>
          </View>
        )}

        <View className="p-4">
          {/* Order Header */}
          <Card className="mb-6 bg-card border border-border shadow-lg overflow-hidden">
            {/* Header Section */}
            <View className={`px-6 py-4 ${isDark ? 'bg-gray-900/50' : 'bg-gray-50'} border-b border-border`}>
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <View className={`w-10 h-10 ${isDark ? 'bg-gray-800' : 'bg-white'} rounded-lg items-center justify-center mr-3 shadow-sm`}>
                    <Text className="text-lg">{order.order_type === 'delivery' ? '🚚' : '🏪'}</Text>
                  </View>
                  <View>
                    <Text className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                      Order Details
                    </Text>
                    <Text className="text-xl font-bold text-foreground">
                      #{order.order_name}
                    </Text>
                  </View>
                </View>
                <View className={`px-3 py-1.5 ${statusColors.bg} rounded-lg`}>
                  <View className="flex-row items-center">
                    <View className={`w-2 h-2 ${statusColors.dot} rounded-full mr-2`} />
                    <Text className={`text-xs font-semibold ${statusColors.text} uppercase tracking-wide`}>
                      {order.status === 'out_for_delivery' ? 'Out For Delivery' :
                        order.status === 'ready_for_pickup' ? 'Ready For Pickup' :
                          order.status === 'picked_up' ? 'Picked Up' : order.status}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <CardContent className="p-6">
              {/* Order Information */}
              <View className="mb-6">
                <View className="flex-row items-center justify-between mb-4">
                  <View className="flex-row items-center space-x-4">
                    <View className={`px-3 py-2 ${order.order_type === 'delivery'
                      ? (isDark ? 'bg-red-900/20' : 'bg-red-50')
                      : (isDark ? 'bg-blue-900/20' : 'bg-blue-50')
                      } rounded-lg border ${order.order_type === 'delivery'
                      ? (isDark ? 'border-red-800/50' : 'border-red-200')
                      : (isDark ? 'border-blue-800/50' : 'border-blue-200')
                      }`}>
                      <Text className={`text-sm font-semibold ${order.order_type === 'delivery'
                        ? (isDark ? 'text-red-400' : 'text-red-700')
                        : (isDark ? 'text-blue-400' : 'text-blue-700')
                        }`}>
                        {order.order_type === 'delivery' ? 'Delivery Order' : 'Pickup Order'}
                      </Text>
                    </View>
                    <View className={`px-3 py-2 ml-2  ${isDark ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-lg`}>
                      <Text className="text-sm font-medium text-muted-foreground">
                        {order.cart_items?.length || 0} item{(order.cart_items?.length || 0) !== 1 ? 's' : ''}
                      </Text>
                    </View>
                  </View>
                  <View className="items-end">
                    <Text className="text-sm text-muted-foreground">Order Time</Text>
                    <Text className="text-base font-semibold text-foreground">
                      {orderTime}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      {timeAgo}
                    </Text>
                  </View>
                </View>

                {/* Total Amount Display */}
                <View className={`p-4 ${isDark ? 'bg-green-900/20' : 'bg-green-50'} rounded-lg border ${isDark ? 'border-green-800/50' : 'border-green-200'}`}>
                  <View className="flex-row items-center justify-between">
                    <View>
                      <Text className="text-sm text-muted-foreground">Total Amount</Text>
                      <Text className="text-2xl font-bold text-green-600 dark:text-green-400">
                        AED {totalBill}
                      </Text>
                      {order.order_type === 'delivery' && ((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge) > 0 && (
                        <Text className="text-xs text-muted-foreground">
                          Includes AED {((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge || 0).toFixed(2)} delivery fee
                        </Text>
                      )}
                    </View>
                    <View className={`w-16 h-16 ${isDark ? 'bg-green-800/30' : 'bg-green-100'} rounded-full items-center justify-center`}>
                      <Text className="text-2xl">💰</Text>
                    </View>
                  </View>
                </View>
              </View>

              {/* Action Buttons */}
              <View>
                {/* Print and Share Bill Buttons */}
                <View className="flex-row mb-4 gap-3">
                  <TouchableOpacity
                    className={`flex-1 flex-row items-center justify-center p-4 ${isDark ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-lg border ${isDark ? 'border-gray-700' : 'border-gray-300'} shadow-sm`}
                    onPress={() => onPrintBill(order)}
                    activeOpacity={0.7}
                  >
                    <Printer size={20} className={`${isDark ? 'text-gray-400' : 'text-gray-600'} mr-2`} />
                    <Text className={`${isDark ? 'text-gray-300' : 'text-gray-700'} font-semibold text-base pl-2`}>
                      Print Bill
                    </Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    className={`flex-1 flex-row items-center justify-center p-4 ${isDark ? 'bg-blue-800/30' : 'bg-blue-100'} rounded-lg border ${isDark ? 'border-blue-700' : 'border-blue-300'} shadow-sm`}
                    onPress={() => onShareBill(order)}
                    activeOpacity={0.7}
                    disabled={isSharing}
                  >
                    {isSharing ? (
                      <ActivityIndicator size={20} color={isDark ? '#60a5fa' : '#2563eb'} className="mr-2" />
                    ) : (
                                              <ShareIcon size={20} className={`${isDark ? 'text-blue-400' : 'text-blue-600'} mr-2`} />
                    )}
                    <Text className={`${isDark ? 'text-blue-300' : 'text-blue-700'} font-semibold text-base pl-2`}>
                      {isSharing ? (sharingProgress || 'Sharing...') : 'Share Bill'}
                    </Text>
                  </TouchableOpacity>
                </View>

                {order.status === 'accepted' && order.order_type === 'pickup' && (
                  <Button
                    size="lg"
                    className={`${foodTheme.primaryBg} w-full shadow-lg`}
                    onPress={handleMarkReadyForPickup}
                    disabled={isThisOrderProcessing}
                  >
                    <View className="flex-row items-center">
                      {isThisOrderProcessing ? (
                        <ActivityIndicator size="small" color="white" className="mr-2" />
                      ) : (
                        <Text className="text-white text-lg mr-2">✓</Text>
                      )}
                      <Text className="text-white font-semibold text-base">
                        {isThisOrderProcessing ? 'Processing...' : 'Mark Ready For Pickup'}
                      </Text>
                    </View>
                  </Button>
                )}

                {order.status === 'accepted' && order.order_type === 'delivery' && (
                  <Button
                    size="lg"
                    className="bg-blue-600 dark:bg-blue-600 w-full shadow-lg"
                    onPress={handleOutForDelivery}
                    disabled={isThisOrderProcessing}
                  >
                    <View className="flex-row items-center">
                      {isThisOrderProcessing ? (
                        <ActivityIndicator size="small" color="white" className="mr-2" />
                      ) : (
                        <Text className="text-white text-lg mr-2">🚚</Text>
                      )}
                      <Text className="text-white font-semibold text-base">
                        {isThisOrderProcessing ? 'Processing...' : 'Send Out For Delivery'}
                      </Text>
                    </View>
                  </Button>
                )}

                {order.status === 'ready_for_pickup' && order.order_type === 'pickup' && (
                  <Button
                    size="lg"
                    className="bg-green-600 dark:bg-green-600 w-full shadow-lg"
                    onPress={handleMarkPickedUp}
                    disabled={isThisOrderProcessing}
                  >
                    <View className="flex-row items-center">
                      {isThisOrderProcessing ? (
                        <ActivityIndicator size="small" color="white" className="mr-2" />
                      ) : (
                        <Text className="text-white text-lg mr-2">✅</Text>
                      )}
                      <Text className="text-white font-semibold text-base">
                        {isThisOrderProcessing ? 'Processing...' : 'Mark as Picked Up'}
                      </Text>
                    </View>
                  </Button>
                )}

                {order.status === 'out_for_delivery' && (
                  <Button
                    size="lg"
                    className="bg-green-600 dark:bg-green-600 w-full shadow-lg"
                    onPress={handleComplete}
                    disabled={isThisOrderProcessing}
                  >
                    <View className="flex-row items-center">
                      {isThisOrderProcessing ? (
                        <ActivityIndicator size="small" color="white" className="mr-2" />
                      ) : (
                        <Text className="text-white text-lg mr-2">🎉</Text>
                      )}
                      <Text className="text-white font-semibold text-base">
                        {isThisOrderProcessing ? 'Processing...' : 'Mark as Delivered'}
                      </Text>
                    </View>
                  </Button>
                )}
              </View>
            </CardContent>
          </Card>

                      {/* Customer Information */}
          <Card className="mb-6 bg-card border border-border shadow-lg">
            <CardContent className="p-6">
              <View className="flex-row items-center justify-between mb-4">
                <Text className="text-lg font-bold text-foreground">Customer Information</Text>
                <View className="flex-row">
                  <TouchableOpacity
                    onPress={() => onCopyCustomerInfo(order)}
                    className={`p-2 rounded-lg mr-2 ${isDark ? 'bg-gray-800' : 'bg-gray-100'} border ${isDark ? 'border-gray-700' : 'border-gray-300'}`}
                    activeOpacity={0.7}
                  >
                    <Copy size={18} className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`} />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => onShareCustomerInfo(order)}
                    className={`p-2 rounded-lg ${isDark ? 'bg-blue-800/30' : 'bg-blue-100'} border ${isDark ? 'border-blue-700' : 'border-blue-300'}`}
                    activeOpacity={0.7}
                  >
                    <ShareIcon size={18} className={`${isDark ? 'text-blue-400' : 'text-blue-600'}`} />
                  </TouchableOpacity>
                </View>
              </View>
              
              <View className="space-y-4">
                {/* Order Name */}
                <View>
                  <Text className="text-sm text-muted-foreground mb-1">Order Number</Text>
                  <Text className="text-base font-semibold text-foreground">
                    #{order.order_name}
                  </Text>
                </View>

                {/* Customer Name */}
                <View>
                  <Text className="text-sm text-muted-foreground mb-1">
                    {order.order_type === 'delivery' ? 'Receiver Name' : 'Customer Name'}
                  </Text>
                  <Text className="text-base font-semibold text-foreground">
                    {(order as any).receiver_name || order.customer_name}
                  </Text>
                  {(order as any).receiver_name && order.customer_name !== (order as any).receiver_name && (
                    <Text className="text-sm text-muted-foreground mt-1">
                      Ordered by: {order.customer_name}
                    </Text>
                  )}
                </View>

                {/* Phone Number */}
                <View>
                  <Text className="text-sm text-muted-foreground mb-1">
                    {order.order_type === 'delivery' ? 'Receiver Phone' : 'Phone Number'}
                  </Text>
                  <Text className="text-base font-semibold text-foreground">
                    {(order as any).receiver_number || order.customer_number}
                  </Text>
                </View>

                {/* Address */}
                {order.address && (
                  <View>
                    <Text className="text-sm text-muted-foreground mb-1">Delivery Address</Text>
                    <Text className="text-base font-semibold text-foreground leading-relaxed">
                      {order.address}
                    </Text>
                  </View>
                )}

                {/* Order Timing */}
                <View className={`p-4 mt-4 ${isDark ? 'bg-gray-800/30' : 'bg-gray-50'} rounded-lg border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
                  <Text className="text-sm text-muted-foreground mb-2">Order Timing</Text>
                  <View className="flex-row justify-between">
                    <View>
                      <Text className="text-xs text-muted-foreground">Ordered at</Text>
                      <Text className="text-sm font-semibold text-foreground">{orderTime}</Text>
                    </View>
                    <View className="items-end">
                      <Text className="text-xs text-muted-foreground">Time ago</Text>
                      <Text className="text-sm font-semibold text-foreground">{timeAgo}</Text>
                    </View>
                  </View>
                </View>
              </View>
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card className="bg-card border border-border shadow-lg">
            <CardContent className="p-6">
              <View className="flex-row items-center justify-between mb-6">
                <Text className="text-lg font-bold text-foreground">Order Items</Text>
                <View className={`px-3 py-1.5 ${isDark ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-md`}>
                  <Text className="text-sm font-medium text-muted-foreground">
                    {order?.cart_items?.length || 0} items
                  </Text>
                </View>
              </View>

              <View className="space-y-4">
                {order.cart_items?.map((item, index) => (
                  <View key={index} className={`p-4 mb-2 ${isDark ? 'bg-gray-800/30' : 'bg-gray-50'} rounded-lg border ${isDark ? 'border-gray-700' : 'border-gray-200'}`}>
                    <View className="flex-row items-start justify-between mb-3">
                      <View className="flex-1 mr-4">
                        <Text className="text-base font-semibold text-foreground mb-2">
                          {item.name}
                        </Text>
                        <View className="flex-row items-center space-x-4">
                          <View className={`px-2 py-1 ${isDark ? 'bg-blue-900/30' : 'bg-blue-100'} rounded`}>
                            <Text className={`text-xs font-medium ${isDark ? 'text-blue-300' : 'text-blue-700'}`}>
                              Qty: {item.quantity}
                            </Text>
                          </View>
                          <Text className="text-sm text-muted-foreground ml-2">
                            AED {item.price.toFixed(2)} each
                          </Text>
                        </View>
                      </View>
                      <Text className="text-lg font-bold text-foreground">
                        AED {(item.quantity * item.price).toFixed(2)}
                      </Text>
                    </View>
                    
                    {item.notes && (
                      <View className={`p-3 ${isDark ? 'bg-yellow-900/20' : 'bg-yellow-50'} rounded-md border-l-2 ${isDark ? 'border-yellow-600' : 'border-yellow-400'}`}>
                        <Text className="text-xs text-muted-foreground mb-1">Special Instructions</Text>
                        <Text className="text-sm text-foreground">
                          {item.notes}
                        </Text>
                      </View>
                    )}
                  </View>
                ))}
              </View>

              {/* Order Summary */}
              <View className={`mt-6 p-4 ${isDark ? 'bg-green-900/20' : 'bg-green-50'} rounded-lg border ${isDark ? 'border-green-800' : 'border-green-200'}`}>
                <Text className="text-base font-bold text-foreground mb-3">Order Summary</Text>
                
                <View className="space-y-2">
                  <View className="flex-row items-center justify-between">
                    <Text className="text-sm text-muted-foreground">Subtotal</Text>
                    <Text className="text-sm font-semibold text-foreground">AED {subTotal}</Text>
                  </View>

                  {order.order_type === 'delivery' && parseFloat(deliveryFee) > 0 && (
                    <View className="flex-row items-center justify-between">
                      <Text className="text-sm text-muted-foreground">Delivery Fee</Text>
                      <Text className="text-sm font-semibold text-foreground">AED {deliveryFee}</Text>
                    </View>
                  )}

                  {(((order as any).bill?.discount_amount || order.bill_amount?.discount_amount) > 0) && (
                    <View>
                      <View className="flex-row items-center justify-between">
                        <Text className="text-sm text-muted-foreground">Discount</Text>
                        <Text className="text-sm font-semibold text-green-600 dark:text-green-400">
                          -AED {((order as any).bill?.discount_amount || order.bill_amount?.discount_amount || 0).toFixed(2)}
                        </Text>
                      </View>
                      {((order as any).bill?.promo_code || (order as any).promo_code || (order.bill_amount as any)?.promo_code) && (
                        <View className="flex-row items-center justify-between mt-1">
                          <Text className="text-xs text-muted-foreground">Promocode</Text>
                          <Text className="text-xs font-mono text-muted-foreground">
                            {(order as any).bill?.promo_code || (order as any).promo_code || (order.bill_amount as any)?.promo_code}
                          </Text>
                        </View>
                      )}
                    </View>
                  )}

                  <View className={`border-t ${isDark ? 'border-green-700' : 'border-green-300'} pt-2 mt-3`}>
                    <View className="flex-row items-center justify-between">
                      <Text className="text-lg font-bold text-foreground">Total Amount</Text>
                      <Text className="text-xl font-bold text-foreground">AED {totalBill}</Text>
                    </View>
                  </View>
                </View>
              </View>
            </CardContent>
          </Card>
        </View>
      </ScrollView>
    </View>
  );
};

OrderDetailsComponent.displayName = 'OrderDetails';
const OrderDetails = React.memo(OrderDetailsComponent);

function AcceptedOrdersScreenContent() {
  // Authentication
  const { session } = useAuth();
  const user = session?.user;

  // State
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showDriverModal, setShowDriverModal] = useState(false);
  const [selectedOrderForDriver, setSelectedOrderForDriver] = useState<string | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<{ id: string; name: string; number: string } | null>(null);
  const [sharingProgress, setSharingProgress] = useState<{ [orderId: string]: string }>({});
  const [sharingOrderId, setSharingOrderId] = useState<string | null>(null);

  const { isDarkColorScheme } = useColorScheme();

  // Memoize theme to prevent recalculation
  const foodTheme = useMemo(() => getFoodTheme(isDarkColorScheme), [isDarkColorScheme]);



  // Data-fetching hooks
  const {
    data: acceptedOrders = [],
    isLoading: isLoadingAccepted,
    error: acceptedOrdersError,
    refetch: refetchAcceptedOrders,
  } = useAcceptedOrders();

  const {
    data: deliveryDrivers = [],
    isLoading: isLoadingDrivers,
    refetch: fetchDeliveryDrivers,
  } = useDeliveryDrivers();

  const {
    data: branchDeliveryStatus,
    isLoading: isLoadingDeliveryStatus,
  } = useBranchDeliveryStatus();

  // Mutation hooks
  const markReadyMutation = useMarkOrderReady();
  const completeMutation = useCompleteOrder();
  const outForDeliveryMutation = useMarkOrderOutForDelivery();
  const readyForPickupMutation = useMarkOrderReadyForPickup();
  const pickedUpMutation = useMarkOrderPickedUp();

  // Function to check if a specific order is being processed
  const isOrderProcessing = useCallback((orderId: string) => {
    // Check if this specific order is being processed in any mutation
    const isMarkReadyPending = markReadyMutation.isPending && markReadyMutation.variables === orderId;
    const isCompletePending = completeMutation.isPending && completeMutation.variables === orderId;
    const isOutForDeliveryPending = outForDeliveryMutation.isPending &&
      outForDeliveryMutation.variables?.order_id === orderId;
    const isReadyForPickupPending = readyForPickupMutation.isPending &&
      readyForPickupMutation.variables === orderId;
    const isPickedUpPending = pickedUpMutation.isPending && pickedUpMutation.variables === orderId;

    return isMarkReadyPending || isCompletePending || isOutForDeliveryPending ||
      isReadyForPickupPending || isPickedUpPending;
  }, [
    markReadyMutation.isPending, markReadyMutation.variables,
    completeMutation.isPending, completeMutation.variables,
    outForDeliveryMutation.isPending, outForDeliveryMutation.variables,
    readyForPickupMutation.isPending, readyForPickupMutation.variables,
    pickedUpMutation.isPending, pickedUpMutation.variables
  ]);

  // Check if any order is being processed (for modal/global UI state)
  const isAnyOrderProcessing = useMemo(() =>
    markReadyMutation.isPending || completeMutation.isPending || outForDeliveryMutation.isPending ||
    readyForPickupMutation.isPending || pickedUpMutation.isPending,
    [markReadyMutation.isPending, completeMutation.isPending, outForDeliveryMutation.isPending,
    readyForPickupMutation.isPending, pickedUpMutation.isPending]
  );

  // Action Handlers
  const handleMarkReady = useCallback((orderId: string) => {
    markReadyMutation.mutate(orderId);
  }, [markReadyMutation]);

  const handleCompleteOrder = useCallback((orderId: string) => {
    completeMutation.mutate(orderId);
  }, [completeMutation]);

  const handleMarkOutForDelivery = useCallback((orderId: string, driverInfo?: { name: string; number: string; id: string }) => {
    outForDeliveryMutation.mutate({ order_id: orderId, driverInfo });
  }, [outForDeliveryMutation]);

  const handleMarkReadyForPickup = useCallback((orderId: string) => {
    readyForPickupMutation.mutate(orderId);
  }, [readyForPickupMutation]);

  const handleMarkPickedUp = useCallback((orderId: string) => {
    pickedUpMutation.mutate(orderId);
  }, [pickedUpMutation]);

  const handlePrintBill = useCallback(async (order: Order) => {
    if (!user?.access_token) {
      Alert.alert('Error', 'Unable to print: Authentication required');
      return;
    }
    await printService.printOrderBillWithDialog(order, user.access_token);
  }, [user?.access_token]);

  const handleShareBill = useCallback(async (order: Order) => {
    if (!user?.access_token) {
      Alert.alert('Error', 'Unable to share: Authentication required');
      return;
    }

    // If sharing from modal, dismiss it to prevent interference
    const wasModalOpen = showDetails && selectedOrder?.order_id === order.order_id;
    if (wasModalOpen) {
      setShowDetails(false);
      setSelectedOrder(null);
    }

    // Mark this order as starting to share
    setSharingOrderId(order.order_id);
    setSharingProgress(prev => ({
      ...prev,
      [order.order_id]: 'Preparing to share...'
    }));
    
    const onProgress = (message: string) => {
      // Simple progress updates without interference concerns since modal is dismissed
      setSharingProgress(prev => {
        if (prev[order.order_id] !== message) {
          return {
            ...prev,
            [order.order_id]: message
          };
        }
        return prev;
      });
    };

    try {
      await printService.shareBillAsPDFWithDialog(order, user.access_token, onProgress);
    } catch (error) {
      console.error('Error sharing bill:', error);
      Alert.alert('Error', 'Failed to share bill. Please try again.');
    } finally {
      // Clear progress after sharing is complete
      setTimeout(() => {
        setSharingOrderId(null);
        setSharingProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[order.order_id];
          return newProgress;
        });
      }, 300);
    }
  }, [user?.access_token, showDetails, selectedOrder?.order_id]);

  // Copy and Share helper functions for customer information
  const formatCustomerInfo = useCallback((order: Order) => {
    const customerName = (order as any).receiver_name || order.customer_name;
    const customerPhone = (order as any).receiver_number || order.customer_number;
    const address = order.address;
    
    let formattedText = `Order Number: ${order.order_name}\nName: ${customerName}\nContact: ${customerPhone}`;
    
    if (address) {
      formattedText += `\nAddress: ${address}`;
      
      // Add directions link if coordinates are available
      if ((order as any).latitude && (order as any).longitude) {
        const lat = (order as any).latitude;
        const lng = (order as any).longitude;
        formattedText += `\n\nDirections: https://maps.google.com/maps?q=${lat},${lng}&hl=es;z=1&amp`;
      }
    }
    
    return formattedText;
  }, []);

  const handleCopyCustomerInfo = useCallback((order: Order) => {
    const formattedText = formatCustomerInfo(order);
    Clipboard.setString(formattedText);
    Alert.alert('Copied', 'Customer information copied to clipboard');
  }, [formatCustomerInfo]);

  const handleShareCustomerInfo = useCallback(async (order: Order) => {
    try {
      const formattedText = formatCustomerInfo(order);
      await RNShare.share({
        message: formattedText,
        title: 'Share Customer Information',
      });
    } catch (error) {
      console.error('Error sharing customer info:', error);
      Alert.alert('Error', 'Failed to share customer information');
    }
  }, [formatCustomerInfo]);

  const handleOutForDeliveryWithDriver = useCallback(async (orderId: string) => {
    // Show driver selection modal when branch has delivery access
    setSelectedOrderForDriver(orderId);
    await fetchDeliveryDrivers();
    setShowDriverModal(true);
  }, [fetchDeliveryDrivers]);

  const handleDriverSelection = useCallback(() => {
    if (selectedDriver && selectedOrderForDriver) {
      handleMarkOutForDelivery(selectedOrderForDriver, selectedDriver);
      setShowDriverModal(false);
      setSelectedDriver(null);
      setSelectedOrderForDriver(null);
    }
  }, [selectedDriver, selectedOrderForDriver, handleMarkOutForDelivery]);

  const handleSimpleOutForDelivery = useCallback((orderId: string) => {
    handleMarkOutForDelivery(orderId);
  }, [handleMarkOutForDelivery]);

  // Determine which handler to use based on branch delivery access
  const getOutForDeliveryHandler = useCallback((order: Order) => {
    // Implement the same condition as food merchant:
    // optionalbuttondata && optionalbuttondata.length > 0 && optionalbuttondata[0].branch_delivery_access && currentOrder?.order_type === "delivery"
    const hasDeliveryAccess = branchDeliveryStatus &&
      Array.isArray(branchDeliveryStatus) &&
      branchDeliveryStatus.length > 0 &&
      branchDeliveryStatus[0].branch_delivery_access &&
      order.order_type === "delivery";

    // Debug logging
    if (__DEV__) {
      console.log('Branch Delivery Status:', JSON.stringify(branchDeliveryStatus, null, 2));
      console.log(`Order ${order.order_id} - Order type: ${order.order_type}, Has delivery access: ${hasDeliveryAccess}, Using ${hasDeliveryAccess ? 'driver selection' : 'simple'} handler`);
    }

    return hasDeliveryAccess ? handleOutForDeliveryWithDriver : handleSimpleOutForDelivery;
  }, [branchDeliveryStatus, handleOutForDeliveryWithDriver, handleSimpleOutForDelivery]);

  const onRefresh = useCallback(() => {
    refetchAcceptedOrders();
  }, [refetchAcceptedOrders]);

  const handleOrderPress = useCallback((order: Order) => {
    setSelectedOrder(order);
    setShowDetails(true);
  }, []);

  const handleBackPress = useCallback(() => {
    setShowDetails(false);
    setSelectedOrder(null);
  }, []);

  // Render Functions
  const renderLoadingSpinner = useCallback(() => (
    <View className="flex-1 items-center justify-center py-16">
      <ActivityIndicator size="large" color={foodTheme.primary} />
      <Text className="mt-4 text-base text-muted-foreground font-medium">Loading orders in progress...</Text>
    </View>
  ), [foodTheme.primary]);

  const renderError = useCallback((error: Error | null) => {
    if (!error) return null;
    return (
      <View className="flex-1 items-center justify-center py-16 px-6">
        <View className={`w-16 h-16 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-4`}>
          <Text className="text-2xl">❌</Text>
        </View>
        <Text className={`${foodTheme.primaryText} text-center mb-6 text-base font-medium`}>
          Failed to load orders in progress
        </Text>
        <Text className="text-muted-foreground text-center mb-6 text-sm">{error.message}</Text>
        <Button onPress={onRefresh} className={foodTheme.primaryDark}>
          <Text className="text-white font-semibold">Try Again</Text>
        </Button>
      </View>
    );
  }, [isDarkColorScheme, foodTheme, onRefresh]);

  const renderEmptyState = useCallback(() => (
    <View className="flex-1 items-center justify-center py-16">
      <View className={`w-20 h-20 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
        <Text className="text-3xl">👨‍🍳</Text>
      </View>
      <Text className="text-foreground text-lg font-medium mb-2">No Orders in Progress</Text>
      <Text className="text-muted-foreground text-center">Orders being prepared will appear here</Text>
    </View>
  ), [isDarkColorScheme]);

  // Memoized order list rendering using FlatList for better performance
  const renderOrderItem = useCallback(({ item: order }: { item: Order }) => (
    <OrderCard
      order={order}
      isOrderProcessing={isOrderProcessing}
      isDark={isDarkColorScheme}
      foodTheme={foodTheme}
      onPress={handleOrderPress}
      onMarkReady={handleMarkReady}
      onCompleteOrder={handleCompleteOrder}
      getOutForDeliveryHandler={getOutForDeliveryHandler}
      onMarkReadyForPickup={handleMarkReadyForPickup}
      onMarkPickedUp={handleMarkPickedUp}
      onPrintBill={handlePrintBill}
      onShareBill={handleShareBill}
      sharingProgress={sharingProgress[order.order_id]}
      isSharing={sharingOrderId === order.order_id}
    />
  ), [isOrderProcessing, isDarkColorScheme, foodTheme, handleOrderPress, handleMarkReady, handleCompleteOrder, getOutForDeliveryHandler, handleMarkReadyForPickup, handleMarkPickedUp, handlePrintBill, handleShareBill, sharingProgress, sharingOrderId]);

  const keyExtractor = useCallback((item: Order) => item.order_id, []);

  const renderOrderList = useCallback(() => {
    if (isLoadingAccepted) return renderLoadingSpinner();
    if (acceptedOrdersError) return renderError(acceptedOrdersError);
    if (acceptedOrders.length === 0) return renderEmptyState();

    return (
      <FlatList
        data={acceptedOrders}
        renderItem={renderOrderItem}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ padding: 16 }}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingAccepted}
            onRefresh={onRefresh}
            tintColor={foodTheme.primary}
          />
        }
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        windowSize={10}
      />
    );
  }, [
    isLoadingAccepted,
    acceptedOrdersError,
    acceptedOrders,
    renderLoadingSpinner,
    renderError,
    renderEmptyState,
    renderOrderItem,
    keyExtractor,
    onRefresh,
    foodTheme.primary
  ]);

  // Memoize sharing props to prevent unnecessary re-renders
  const sharingPropsForModal = useMemo(() => {
    if (!selectedOrder) {
      return { sharingProgress: undefined, isSharing: false };
    }
    
    const orderId = selectedOrder.order_id;
    return {
      sharingProgress: sharingProgress[orderId],
      isSharing: sharingOrderId === orderId
    };
  }, [selectedOrder, sharingProgress, sharingOrderId]);

  // Order Details Modal
  const OrderDetailsModal = () => (
    <Modal
      visible={showDetails}
      transparent={true}
      animationType="slide"
      onRequestClose={handleBackPress}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-card rounded-t-3xl max-h-[90%]">
          <OrderDetails
            order={selectedOrder}
            inModal={true}
            isDark={isDarkColorScheme}
            foodTheme={foodTheme}
            onBack={handleBackPress}
            isOrderProcessing={isOrderProcessing}
            onMarkReady={handleMarkReady}
            onCompleteOrder={handleCompleteOrder}
            getOutForDeliveryHandler={getOutForDeliveryHandler}
            onMarkReadyForPickup={handleMarkReadyForPickup}
            onMarkPickedUp={handleMarkPickedUp}
            onPrintBill={handlePrintBill}
            onShareBill={handleShareBill}
            onCopyCustomerInfo={handleCopyCustomerInfo}
            onShareCustomerInfo={handleShareCustomerInfo}
            sharingProgress={sharingPropsForModal.sharingProgress}
            isSharing={sharingPropsForModal.isSharing}
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <OrdersHeader
        title="Orders In Progress"
      />

      {/* Order List */}
      <View className="flex-1 bg-card">
        {renderOrderList()}
      </View>

      {/* Order Details Modal */}
      <OrderDetailsModal />

      {/* Delivery Driver Selection Modal */}
      <Modal
        visible={showDriverModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDriverModal(false)}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-4">
          <View className="bg-card rounded-lg p-6 w-full max-w-md">
            <Text className="text-xl font-bold text-foreground mb-4">Select Delivery Driver</Text>

            {isLoadingDrivers ? (
              <View className="items-center py-8">
                <ActivityIndicator size="large" color={foodTheme.primary} />
                <Text className="mt-2 text-muted-foreground">Loading drivers...</Text>
              </View>
            ) : (
              <ScrollView className="max-h-64 mb-4">
                {deliveryDrivers.length === 0 ? (
                  <Text className="text-muted-foreground text-center py-4">
                    No delivery drivers available
                  </Text>
                ) : (
                  deliveryDrivers.map((driver: any) => (
                    <TouchableOpacity
                      key={driver.id}
                      className={`p-3 rounded-lg mb-2 border ${selectedDriver?.id === driver.id
                          ? 'bg-red-100 border-red-500'
                          : 'bg-muted border-border'
                        }`}
                      onPress={() => setSelectedDriver(driver)}
                    >
                      <Text className="font-semibold text-foreground">{driver.name}</Text>
                      <Text className="text-sm text-muted-foreground">{driver.number}</Text>
                    </TouchableOpacity>
                  ))
                )}
              </ScrollView>
            )}

            <View className="flex-row justify-end space-x-3">
              <Button
                variant="outline"
                onPress={() => {
                  setShowDriverModal(false);
                  setSelectedDriver(null);
                  setSelectedOrderForDriver(null);
                }}
                className="px-4 mr-4"
              >
                <Text>Cancel</Text>
              </Button>
              <Button
                onPress={handleDriverSelection}
                disabled={!selectedDriver || isAnyOrderProcessing}
                className={`px-4 ${foodTheme.primaryDark}`}
              >
                <Text className="text-white">
                  {isAnyOrderProcessing ? 'Processing...' : 'Confirm'}
                </Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

export default function AcceptedOrdersScreen() {
  return <AcceptedOrdersScreenContent />;
} 