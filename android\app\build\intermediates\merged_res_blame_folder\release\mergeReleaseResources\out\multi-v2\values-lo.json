{"logs": [{"outputFile": "com.cravin.merchant.app-mergeReleaseResources-71:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "85,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "7440,11539,11637,11752", "endColumns": "97,97,114,99", "endOffsets": "7533,11632,11747,11847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4689ac814784dd79f2206f332baa7585\\transformed\\play-services-basement-18.1.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "74", "startColumns": "4", "startOffsets": "6096", "endColumns": "131", "endOffsets": "6223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd29036936cb81e953b907f3a3c29b2f\\transformed\\material-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1048,1114,1203,1272,1331,1426,1492,1557,1615,1680,1741,1801,1907,1968,2028,2086,2157,2276,2362,2439,2529,2614,2696,2839,2914,2990,3121,3211,3289,3344,3399,3465,3534,3608,3679,3758,3831,3908,3977,4047,4144,4229,4304,4397,4490,4564,4633,4727,4779,4862,4929,5013,5097,5159,5223,5286,5356,5455,5553,5648,5742,5801,5860,5939,6024,6101", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "264,338,409,490,576,659,774,893,976,1043,1109,1198,1267,1326,1421,1487,1552,1610,1675,1736,1796,1902,1963,2023,2081,2152,2271,2357,2434,2524,2609,2691,2834,2909,2985,3116,3206,3284,3339,3394,3460,3529,3603,3674,3753,3826,3903,3972,4042,4139,4224,4299,4392,4485,4559,4628,4722,4774,4857,4924,5008,5092,5154,5218,5281,5351,5450,5548,5643,5737,5796,5855,5934,6019,6096,6172"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,87,88,140,154,157,159,160,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,220,221,222", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,3586,3660,3731,3812,3898,4687,4802,4921,7637,7704,11450,13085,13306,13432,13527,13593,13658,13716,13781,13842,13902,14008,14069,14129,14187,14258,14444,14530,14607,14697,14782,14864,15007,15082,15158,15289,15379,15457,15512,15567,15633,15702,15776,15847,15926,15999,16076,16145,16215,16312,16397,16472,16565,16658,16732,16801,16895,16947,17030,17097,17181,17265,17327,17391,17454,17524,17623,17721,17816,17910,17969,18028,18189,18274,18351", "endLines": "22,50,51,52,53,54,62,63,64,87,88,140,154,157,159,160,161,162,163,164,165,166,167,168,169,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,220,221,222", "endColumns": "12,73,70,80,85,82,114,118,82,66,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,76,89,84,81,142,74,75,130,89,77,54,54,65,68,73,70,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78,84,76,75", "endOffsets": "914,3655,3726,3807,3893,3976,4797,4916,4999,7699,7765,11534,13149,13360,13522,13588,13653,13711,13776,13837,13897,14003,14064,14124,14182,14253,14372,14525,14602,14692,14777,14859,15002,15077,15153,15284,15374,15452,15507,15562,15628,15697,15771,15842,15921,15994,16071,16140,16210,16307,16392,16467,16560,16653,16727,16796,16890,16942,17025,17092,17176,17260,17322,17386,17449,17519,17618,17716,17811,17905,17964,18023,18102,18269,18346,18422"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250db4afb8fa7de0d997879e3d4adf3d\\transformed\\media3-exoplayer-1.4.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,185,251,316,391,461,553,640", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "119,180,246,311,386,456,548,635,707"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9680,9749,9810,9876,9941,10016,10086,10178,10265", "endColumns": "68,60,65,64,74,69,91,86,71", "endOffsets": "9744,9805,9871,9936,10011,10081,10173,10260,10332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02a6c65af6aca4fe0b17434c7fe8f02b\\transformed\\play-services-base-18.0.1\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "66,67,68,69,70,71,72,73,75,76,77,78,79,80,81,82,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5080,5188,5358,5488,5595,5747,5872,5980,6228,6380,6485,6651,6780,6951,7112,7180,7249", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "5183,5353,5483,5590,5742,5867,5975,6091,6375,6480,6646,6775,6946,7107,7175,7244,7329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "55,56,57,58,59,60,61,225", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3981,4077,4180,4279,4377,4478,4576,18569", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "4072,4175,4274,4372,4473,4571,4682,18665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b1efdd330a6997d521c41b8a19879f0\\transformed\\exoplayer-ui-2.18.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,477,655,737,817,894,982,1064,1140,1204,1297,1389,1459,1523,1586,1656,1766,1873,1983,2051,2128,2198,2274,2358,2440,2502,2565,2618,2676,2724,2785,2844,2912,2973,3039,3103,3162,3226,3293,3360,3414,3474,3548,3622", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "281,472,650,732,812,889,977,1059,1135,1199,1292,1384,1454,1518,1581,1651,1761,1868,1978,2046,2123,2193,2269,2353,2435,2497,2560,2613,2671,2719,2780,2839,2907,2968,3034,3098,3157,3221,3288,3355,3409,3469,3543,3617,3673"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,572,7770,7852,7932,8009,8097,8179,8255,8319,8412,8504,8574,8638,8701,8771,8881,8988,9098,9166,9243,9313,9389,9473,9555,9617,10337,10390,10448,10496,10557,10616,10684,10745,10811,10875,10934,10998,11065,11132,11186,11246,11320,11394", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,81,79,76,87,81,75,63,92,91,69,63,62,69,109,106,109,67,76,69,75,83,81,61,62,52,57,47,60,58,67,60,65,63,58,63,66,66,53,59,73,73,55", "endOffsets": "376,567,745,7847,7927,8004,8092,8174,8250,8314,8407,8499,8569,8633,8696,8766,8876,8983,9093,9161,9238,9308,9384,9468,9550,9612,9675,10385,10443,10491,10552,10611,10679,10740,10806,10870,10929,10993,11060,11127,11181,11241,11315,11389,11445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,131,201,283,350,417,488", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "126,196,278,345,412,483,554"}, "to": {"startLines": "65,155,156,158,172,223,224", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5004,13154,13224,13365,14377,18427,18498", "endColumns": "75,69,81,66,66,70,70", "endOffsets": "5075,13219,13301,13427,14439,18493,18564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,219", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1022,1125,1238,1323,1427,1538,1616,1693,1784,1877,1969,2063,2163,2256,2351,2447,2538,2629,2710,2817,2921,3019,3122,3226,3330,3487,18107", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "1017,1120,1233,1318,1422,1533,1611,1688,1779,1872,1964,2058,2158,2251,2346,2442,2533,2624,2705,2812,2916,3014,3117,3221,3325,3482,3581,18184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4d7ecf19998ab3515e1b90137c4d08f\\transformed\\biometric-1.1.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,260,370,504,627,764,883,1010,1105,1243,1374", "endColumns": "105,98,109,133,122,136,118,126,94,137,130,118", "endOffsets": "156,255,365,499,622,759,878,1005,1100,1238,1369,1488"}, "to": {"startLines": "84,86,144,145,146,147,148,149,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7334,7538,11852,11962,12096,12219,12356,12475,12602,12697,12835,12966", "endColumns": "105,98,109,133,122,136,118,126,94,137,130,118", "endOffsets": "7435,7632,11957,12091,12214,12351,12470,12597,12692,12830,12961,13080"}}]}]}