import * as React from 'react';
import { TextInput } from 'react-native';
import { cn } from '~/lib/utils';

export interface InputProps extends React.ComponentProps<typeof TextInput> {
  className?: string;
}

const Input = React.forwardRef<TextInput, InputProps>(
  ({ className, ...props }, ref) => {
    return (
      <TextInput
        ref={ref}
        className={cn(
          'h-12 rounded-lg border border-input bg-background px-3 py-2 text-base text-foreground placeholder:text-muted-foreground web:focus:outline-none web:focus:ring-2 web:focus:ring-ring web:focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          className
        )}
        {...props}
      />
    );
  }
);

Input.displayName = 'Input';

export { Input }; 