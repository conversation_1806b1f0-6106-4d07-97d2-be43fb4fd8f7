import '../global.css';

import {
  useFonts,
  Inter_100Thin,
  Inter_300Light,
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
  Inter_900Black,
} from '@expo-google-fonts/inter';
import * as SplashScreen from 'expo-splash-screen';

import { DarkTheme, DefaultTheme, Theme, ThemeProvider } from '@react-navigation/native';
import { Slot, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as React from 'react';
import { Appearance, Platform, View } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NAV_THEME } from '~/lib/constants';
import { useColorScheme } from '~/lib/useColorScheme';
import { PortalHost } from '@rn-primitives/portal';
import { QueryProvider } from '~/lib/query-client';
import { setAndroidNavigationBar } from '~/lib/android-navigation-bar';
import { UnifiedNotificationProvider } from '~/lib/providers/UnifiedNotificationProvider';
import { ApiNotificationProvider } from '~/lib/providers/ApiNotificationProvider';

const LIGHT_THEME: Theme = {
  ...DefaultTheme,
  colors: NAV_THEME.light,
};
const DARK_THEME: Theme = {
  ...DarkTheme,
  colors: NAV_THEME.dark,
};

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from 'expo-router';

// OneSignal handles notification responses and navigation automatically

function useSetWebBackgroundClassName(isDarkColorScheme: boolean) {
  React.useEffect(() => {
    document.body.classList.add('bg-background');
    return () => {
      document.body.classList.remove('bg-background');
    };
  }, []);

  React.useEffect(() => {
    if (isDarkColorScheme) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkColorScheme]);
}

const noop = () => { };

const usePlatformSpecificSetup = Platform.select({
  web: useSetWebBackgroundClassName,
  android: (isDark) => setAndroidNavigationBar(isDark ? 'dark' : 'light'),
  default: noop,
});

export default function RootLayout() {
  const [fontsLoaded, fontError] = useFonts({
    Inter_100Thin,
    Inter_300Light,
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
    Inter_700Bold,
    Inter_900Black,
  });

  const { isDarkColorScheme } = useColorScheme();
  usePlatformSpecificSetup(isDarkColorScheme);

  React.useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Keep splash screen visible while fonts load
  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <QueryProvider>
          <UnifiedNotificationProvider>
            <ApiNotificationProvider>
              <ThemeProvider value={isDarkColorScheme ? DARK_THEME : LIGHT_THEME}>
                <StatusBar style={isDarkColorScheme ? 'light' : 'dark'} />
                <View style={{ flex: 1 }}>
                  <Slot />
                </View>
                <PortalHost />
              </ThemeProvider>
            </ApiNotificationProvider>
          </UnifiedNotificationProvider>
        </QueryProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

 