{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "cravin-merchant", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logo.png", "scheme": "cravin<PERSON><PERSON>t", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.cravin.merchant", "buildNumber": "1", "infoPlist": {"UIBackgroundModes": ["processing", "audio"], "CFBundleDisplayName": "<PERSON><PERSON><PERSON>"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.cravin.merchant", "versionCode": 1, "permissions": ["WAKE_LOCK", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "SYSTEM_ALERT_WINDOW", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/logo.png"}, "plugins": ["expo-router", "expo-secure-store", ["onesignal-expo-plugin", {"mode": "production"}], "expo-audio"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"build": {"experimental": {"ios": {"appExtensions": [{"targetName": "OneSignalNotificationServiceExtension", "bundleIdentifier": "com.cravin.merchant.OneSignalNotificationServiceExtension", "entitlements": {"com.apple.security.application-groups": ["group.com.cravin.merchant.onesignal"]}}, {"targetName": "OneSignalNotificationServiceExtension", "bundleIdentifier": "com.cravin.merchant.OneSignalNotificationServiceExtension", "entitlements": {"com.apple.security.application-groups": ["group.com.cravin.merchant.onesignal"]}}, {"targetName": "OneSignalNotificationServiceExtension", "bundleIdentifier": "com.cravin.merchant.OneSignalNotificationServiceExtension", "entitlements": {"com.apple.security.application-groups": ["group.com.cravin.merchant.onesignal"]}}]}}}, "projectId": "************************************"}}}}