@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HH:\\Cravin\\cravin-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-DCMAKE_FIND_ROOT_PATH=H:\\Cravin\\cravin-app\\android\\app\\.cxx\\RelWithDebInfo\\6554243o\\prefab\\arm64-v8a\\prefab" ^
  "-BH:\\Cravin\\cravin-app\\android\\app\\.cxx\\RelWithDebInfo\\6554243o\\arm64-v8a" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=H:\\Cravin\\cravin-app\\android\\app\\build" ^
  "-DPROJECT_ROOT_DIR=H:\\Cravin\\cravin-app\\android" ^
  "-DREACT_ANDROID_DIR=H:\\Cravin\\cravin-app\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
