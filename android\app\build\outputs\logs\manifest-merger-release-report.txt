-- Merging decision tree log ---
manifest
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:1:1-41:12
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:1:1-41:12
MERGED from [:react-native-gesture-handler] H:\Cravin\cravin-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] H:\Cravin\cravin-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] H:\Cravin\cravin-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] H:\Cravin\cravin-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] H:\Cravin\cravin-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-onesignal] H:\Cravin\cravin-app\node_modules\react-native-onesignal\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] H:\Cravin\cravin-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] H:\Cravin\cravin-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] H:\Cravin\cravin-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu] H:\Cravin\cravin-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-menu-interface] H:\Cravin\cravin-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-18:12
MERGED from [host.exp.exponent:expo.modules.av:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7d784b1be5e2369f95f298bb989f58\transformed\expo.modules.av-15.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18d4c57a39b6281cb2c7f9a0213928ae\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b3c8037b98d96420bf52805fc30b2c\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1fcf9a7e2fdee1cbfa45ddc2de3118\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9616061a70c48f7f6f08941ffa751889\transformed\react-android-0.79.5-release\AndroidManifest.xml:2:1-12:12
MERGED from [:expo-constants] H:\Cravin\cravin-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] H:\Cravin\cravin-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-manifests] H:\Cravin\cravin-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] H:\Cravin\cravin-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] H:\Cravin\cravin-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd9d7ba25d1438208f96157ddcd6e358\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79abbfa205322d40c6b84839f63416b0\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\252080c1424b57af4d7507d029d8591a\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:2:1-33:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a45eaa01c94036bfce1be4073840d76\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\469c4df5952573b4ed36c6c2062c1133\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0ce2567ccc087fb22aa7b99b1e0462\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.navigationbar:4.2.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01e470a716b449ca6703c2e0cec7372\transformed\expo.modules.navigationbar-4.2.7\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.print:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2862bc9df2a2ab0fbb5fd0f17196117\transformed\expo.modules.print-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\697d0f2cfe938fbbebe2f2bc2e226e02\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:2:1-29:12
MERGED from [com.onesignal:OneSignal:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb258541fbedb11686b9bf73f0f40985\transformed\OneSignal-5.1.34\AndroidManifest.xml:2:1-9:12
MERGED from [com.onesignal:in-app-messages:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b47daf1560bb780136208263a881b4\transformed\in-app-messages-5.1.34\AndroidManifest.xml:2:1-9:12
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:2:1-113:12
MERGED from [com.onesignal:location:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42d1a13e414193dbd93f5ef64c4067f\transformed\location-5.1.34\AndroidManifest.xml:2:1-12:12
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73a7775f35e417780184b8f501ba2117\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc2afe36c727cd4c319cac1b77229cf\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\daf877faecb4db893e000faf757056c7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\281993a8081a7f8ed7163ce2e30584f1\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a24e10b8c009f789fedc6ea50f1b85c\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d1aafd621ebff9ee1ba890f8f51a7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7351d3833f13a2cc542fe59def656210\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ef79d754d050ead123a8dc574816b32\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39189d1f65b3b9d5d51083ae6ee8e753\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89ea14f423ad5c8e358b53e4a9b81859\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a88349e37837a6cee380b95f0f29f7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0bff994c226a93b8e8ded58ee81cbc\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13722947dc5f00d0d1a1c9b2d82d5407\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5799eb2ead5366c18900bdc6eb607ded\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\356fc999dba310762fcb51e029991c71\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68b695252e1a975cc49d4b6bc33f0c98\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e536ed9c62de1fa526e47192528d03\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595ddea61318adcd7bdc9d5a52426fe2\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c674cd16f9f5e5f23c9c3b7e3a3f7588\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8028197b572d896dd9f8b8804aa0fd09\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\432c227539738b0a6fa92207b5f4f4e6\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2555262b72297a642e7a95d8688571b9\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a30dd3c9c373083e00c82f964d10f3\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34d85fffb9c43b690472da434082af14\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\280f1670cd45c825a6613a5172c11f7a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc548028e1fe578ec6cdb8cf56f9a06c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab7e0c4acc242ba6899988208e639af\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb15aeac3381993b7b6be5a355bfea61\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\200e032c88fe4eeaab6cfc9d3de54cb0\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\993e9886b0746368e368e024aca617d1\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41e7aa391a69310f9c865af9efcc14ac\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\973fffd5662755d4e69b886e239d1df3\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92383f2905d19505013069f0bdc93d86\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d8a2a37b30a9c89433a23bfbe2c62e\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b10410589cd543d28a5071420020c9e\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c93d95e280558f6ca9e7696f6cbd2cdc\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84c2e1fdf67a20a30b80c97a36fac620\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa74fe9b56d39051c08965a002f0b9a\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47c4be2db69016738bbafb806598f314\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e80fdcd82127bd0c9c0544df1e2cfa\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06e5a4bc13c42f302c7d1242ac46a49c\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\211be35423f7bfaec0e55aefa1405712\transformed\media3-extractor-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0392dcde0f5057ee09db92292159e852\transformed\media3-container-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a220c0850ea7a4312f5767b5b3a47390\transformed\media3-datasource-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103d981b5962056e853bb637be791128\transformed\media3-decoder-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\529f1223e88b2da943ded65d9d311cec\transformed\media3-database-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af51e38e80ddf7c842cfbea983da50d3\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a3097707dc566d1228ea0db1842b762\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae019187e5c516a5cb0b7ac11247c8c7\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\448ca3090c033310e421ff7270af74f2\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d51b6e88007b5429952582adf6ad94\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675f4f7d07ab458ad78ae7586d1e5993\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\942d929df50e13a06eea64c3f56ce2e6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc83fc3662b1f056d8c5fcf6cc0b2ce8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\504eeb7c7bcb8f63fc45f7fe459b21ca\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a69fcdfcfaf951b7d4c1b7aa539671\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93599d426d717b29c71811f738de6550\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbb53b0031d6b8cf2af31614c779d6c\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae76c217763a0357fb15e4bf3b4067b3\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30816eecce2e37d429d2df995908dc08\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10f2e59836afcfaeb7024ffa9f730642\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a337a35254edb8a41af5a93dcd364b0a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f83150c8cd0912cbab709d7028c29e4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b1efdd330a6997d521c41b8a19879f0\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52de6cc1bf4e0cbce2ccf873236402e3\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad04b768963391ba6665aa73cb02336d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb3414a5b9006ede02e6334a5c1c6e5\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88f934b05d112786789c29637c6dc478\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60de6752652a833c9a313b58ddaff005\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\900e1bfa4bca01d85722b25f0cd283a5\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\385207845dcfb3956e3a4c461df893df\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ce4db16154b1473cd097236076b5c4a\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\417f62f3d94f17f3e5d09a4e5b5cb22f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\484febe24aacd0df853b318a482220cb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d327f7d72e200149bb16e5512a317\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ac8370b68d7cf5c72cf3ece9b72580\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3236732610ecd0bdf86bfde650060d85\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc9f8e9a1a6d58dfeddb1833d8dc0f9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc8aa523a6c477e084b3ea9cd8fb499\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1735a5bdc5556b5fcd9734fc7bce3c\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2727b732b2ccccec1ebeef9a3638abbf\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2ad778840a272b59e429623bbe3eda\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0caca4f943fdf24a419b6298848cfc96\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc14df0b6916a817c4ff4d00c27f2b4\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6550963f3403c0d1d6c453e7b7e3228\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\df75d6af1b7fa262029bb3cf0826a17c\transformed\hermes-android-0.79.5-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b214cd1187ca8ca8f548d8878a9b523\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c942b0acc1f7702ab490f1c891f0cfb5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02014e6ae21bddbc8831a1642129fc1d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e440d6d7f8b81f1ee5821c2f7734b6c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\af53d4f4b13e08684217b30f24915ec1\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd49a2bc21d7481306f5fab22b13b93\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efe3a061c6deb0a5c90e5593eb0157f8\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\819b0f19fd7a4e0628a596acf3c0edf7\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98254bf55b4a99883211a00c74ee064c\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982380dbe80441682c717ab01f65a7f9\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76679a59b9f9ae735a48e7d215f630b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\353c17a77463ee884dbc33275c068645\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db2b7a71f5dbcd5883ac91edcd6931d\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6937096d739cfeb784d5c810ef736db\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\072824e130dfb32121ac6c53e61c0c7e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cefce8cad880e9116d74623f30bb2411\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a750665b475a53f1748cb783f578fd\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\207646751c7680dd4c2eebb78ef5b048\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f5330b574122aa672d96a0f5922e634\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:2:3-64
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:8:5-67
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:9:5-67
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:9:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:2:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:3:3-77
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:8:5-80
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:8:5-80
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:3:20-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:4:3-77
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:10:5-80
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:4:20-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:5:3-78
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:29:5-81
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:29:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:5:20-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:6:3-68
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:7:5-71
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:7:5-71
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:6:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:7:3-75
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:7:20-73
uses-permission#android.permission.VIBRATE
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:8:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:24:5-66
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:24:5-66
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:8:20-61
uses-permission#android.permission.WAKE_LOCK
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:9:3-65
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:18:5-68
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:18:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:9:20-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:10:3-78
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:9:5-81
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:10:20-76
queries
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:11:3-17:13
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:7:5-15:15
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:7:5-15:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:12:5-16:14
action#android.intent.action.VIEW
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:7-58
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:15-56
category#android.intent.category.BROWSABLE
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:7-67
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:17-65
data
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
	android:scheme
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
application
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:3-40:17
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:3-40:17
MERGED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-16:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:20:5-31:19
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:17:5-27:19
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:17:5-27:19
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:48:5-111:19
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:48:5-111:19
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:12:5-22:19
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:12:5-22:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:221-247
	android:label
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:48-80
	android:fullBackupContent
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:248-306
	android:roundIcon
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:116-161
	android:icon
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:81-115
	android:allowBackup
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:162-188
	android:theme
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:189-220
	android:dataExtractionRules
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:307-376
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:16-47
meta-data#com.google.firebase.messaging.default_notification_channel_id
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:5-117
	android:value
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:93-115
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:16-92
meta-data#com.google.firebase.messaging.default_notification_color
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:5-139
	android:resource
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:88-137
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:16-87
meta-data#com.google.firebase.messaging.default_notification_icon
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:5-135
	android:resource
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:87-133
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:16-86
meta-data#expo.modules.updates.ENABLED
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:5-83
	android:value
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:60-81
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:5-105
	android:value
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:81-103
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:5-99
	android:value
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:80-97
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:16-79
activity#com.cravin.merchant.MainActivity
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:5-39:16
	android:screenOrientation
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:280-316
	android:launchMode
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:135-166
	android:windowSoftInputMode
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:167-209
	android:exported
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:256-279
	android:configChanges
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:44-134
	android:theme
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:210-255
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:26:7-29:23
action#android.intent.action.MAIN
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:27:9-60
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:27:17-58
category#android.intent.category.LAUNCHER
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:28:9-68
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:28:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:cravinmerchant+data:scheme:exp+cravin-app+data:scheme:exp+cravin-merchant+data:scheme:myapp
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:30:7-38:23
category#android.intent.category.DEFAULT
ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:32:9-67
	android:name
		ADDED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:32:19-65
uses-sdk
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
MERGED from [:react-native-gesture-handler] H:\Cravin\cravin-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] H:\Cravin\cravin-app\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] H:\Cravin\cravin-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] H:\Cravin\cravin-app\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] H:\Cravin\cravin-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] H:\Cravin\cravin-app\node_modules\react-native-screens\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\Cravin\cravin-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] H:\Cravin\cravin-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] H:\Cravin\cravin-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] H:\Cravin\cravin-app\node_modules\expo\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-onesignal] H:\Cravin\cravin-app\node_modules\react-native-onesignal\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-onesignal] H:\Cravin\cravin-app\node_modules\react-native-onesignal\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] H:\Cravin\cravin-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] H:\Cravin\cravin-app\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] H:\Cravin\cravin-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] H:\Cravin\cravin-app\node_modules\react-native-svg\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] H:\Cravin\cravin-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] H:\Cravin\cravin-app\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] H:\Cravin\cravin-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] H:\Cravin\cravin-app\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] H:\Cravin\cravin-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] H:\Cravin\cravin-app\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7d784b1be5e2369f95f298bb989f58\transformed\expo.modules.av-15.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\df7d784b1be5e2369f95f298bb989f58\transformed\expo.modules.av-15.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18d4c57a39b6281cb2c7f9a0213928ae\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\18d4c57a39b6281cb2c7f9a0213928ae\transformed\expo.modules.font-13.3.2\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b3c8037b98d96420bf52805fc30b2c\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b3c8037b98d96420bf52805fc30b2c\transformed\expo.modules.splashscreen-0.30.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1fcf9a7e2fdee1cbfa45ddc2de3118\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.systemui:5.0.10] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1fcf9a7e2fdee1cbfa45ddc2de3118\transformed\expo.modules.systemui-5.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9616061a70c48f7f6f08941ffa751889\transformed\react-android-0.79.5-release\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\9616061a70c48f7f6f08941ffa751889\transformed\react-android-0.79.5-release\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] H:\Cravin\cravin-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] H:\Cravin\cravin-app\node_modules\expo-constants\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] H:\Cravin\cravin-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] H:\Cravin\cravin-app\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] H:\Cravin\cravin-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] H:\Cravin\cravin-app\node_modules\expo-manifests\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] H:\Cravin\cravin-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] H:\Cravin\cravin-app\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] H:\Cravin\cravin-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] H:\Cravin\cravin-app\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd9d7ba25d1438208f96157ddcd6e358\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd9d7ba25d1438208f96157ddcd6e358\transformed\expo.modules.asset-11.1.7\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.audio:expo.modules.audio:0.4.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79abbfa205322d40c6b84839f63416b0\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.blur:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\79abbfa205322d40c6b84839f63416b0\transformed\expo.modules.blur-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\252080c1424b57af4d7507d029d8591a\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\252080c1424b57af4d7507d029d8591a\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:6:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a45eaa01c94036bfce1be4073840d76\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a45eaa01c94036bfce1be4073840d76\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\469c4df5952573b4ed36c6c2062c1133\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\469c4df5952573b4ed36c6c2062c1133\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0ce2567ccc087fb22aa7b99b1e0462\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.linking:7.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0ce2567ccc087fb22aa7b99b1e0462\transformed\expo.modules.linking-7.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.navigationbar:4.2.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01e470a716b449ca6703c2e0cec7372\transformed\expo.modules.navigationbar-4.2.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.navigationbar:4.2.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\b01e470a716b449ca6703c2e0cec7372\transformed\expo.modules.navigationbar-4.2.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.print:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2862bc9df2a2ab0fbb5fd0f17196117\transformed\expo.modules.print-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.print:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\f2862bc9df2a2ab0fbb5fd0f17196117\transformed\expo.modules.print-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\697d0f2cfe938fbbebe2f2bc2e226e02\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.securestore:14.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\697d0f2cfe938fbbebe2f2bc2e226e02\transformed\expo.modules.securestore-14.2.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:5:5-44
MERGED from [com.onesignal:OneSignal:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb258541fbedb11686b9bf73f0f40985\transformed\OneSignal-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:OneSignal:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\bb258541fbedb11686b9bf73f0f40985\transformed\OneSignal-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:in-app-messages:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b47daf1560bb780136208263a881b4\transformed\in-app-messages-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:in-app-messages:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\94b47daf1560bb780136208263a881b4\transformed\in-app-messages-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:5:5-7:41
MERGED from [com.onesignal:location:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42d1a13e414193dbd93f5ef64c4067f\transformed\location-5.1.34\AndroidManifest.xml:8:5-10:41
MERGED from [com.onesignal:location:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\f42d1a13e414193dbd93f5ef64c4067f\transformed\location-5.1.34\AndroidManifest.xml:8:5-10:41
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:4:5-6:41
MERGED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:4:5-6:41
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73a7775f35e417780184b8f501ba2117\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73a7775f35e417780184b8f501ba2117\transformed\work-runtime-ktx-2.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc2afe36c727cd4c319cac1b77229cf\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc2afe36c727cd4c319cac1b77229cf\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\daf877faecb4db893e000faf757056c7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\daf877faecb4db893e000faf757056c7\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\281993a8081a7f8ed7163ce2e30584f1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\281993a8081a7f8ed7163ce2e30584f1\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a24e10b8c009f789fedc6ea50f1b85c\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4a24e10b8c009f789fedc6ea50f1b85c\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d1aafd621ebff9ee1ba890f8f51a7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\232d1aafd621ebff9ee1ba890f8f51a7\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7351d3833f13a2cc542fe59def656210\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7351d3833f13a2cc542fe59def656210\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ef79d754d050ead123a8dc574816b32\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ef79d754d050ead123a8dc574816b32\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39189d1f65b3b9d5d51083ae6ee8e753\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39189d1f65b3b9d5d51083ae6ee8e753\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89ea14f423ad5c8e358b53e4a9b81859\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89ea14f423ad5c8e358b53e4a9b81859\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a88349e37837a6cee380b95f0f29f7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\53a88349e37837a6cee380b95f0f29f7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0bff994c226a93b8e8ded58ee81cbc\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0bff994c226a93b8e8ded58ee81cbc\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13722947dc5f00d0d1a1c9b2d82d5407\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13722947dc5f00d0d1a1c9b2d82d5407\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5799eb2ead5366c18900bdc6eb607ded\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5799eb2ead5366c18900bdc6eb607ded\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\356fc999dba310762fcb51e029991c71\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\356fc999dba310762fcb51e029991c71\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68b695252e1a975cc49d4b6bc33f0c98\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\68b695252e1a975cc49d4b6bc33f0c98\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e536ed9c62de1fa526e47192528d03\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e536ed9c62de1fa526e47192528d03\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595ddea61318adcd7bdc9d5a52426fe2\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\595ddea61318adcd7bdc9d5a52426fe2\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c674cd16f9f5e5f23c9c3b7e3a3f7588\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c674cd16f9f5e5f23c9c3b7e3a3f7588\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8028197b572d896dd9f8b8804aa0fd09\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8028197b572d896dd9f8b8804aa0fd09\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\432c227539738b0a6fa92207b5f4f4e6\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\432c227539738b0a6fa92207b5f4f4e6\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2555262b72297a642e7a95d8688571b9\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2555262b72297a642e7a95d8688571b9\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a30dd3c9c373083e00c82f964d10f3\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66a30dd3c9c373083e00c82f964d10f3\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34d85fffb9c43b690472da434082af14\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34d85fffb9c43b690472da434082af14\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\280f1670cd45c825a6613a5172c11f7a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\280f1670cd45c825a6613a5172c11f7a\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc548028e1fe578ec6cdb8cf56f9a06c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc548028e1fe578ec6cdb8cf56f9a06c\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab7e0c4acc242ba6899988208e639af\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab7e0c4acc242ba6899988208e639af\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb15aeac3381993b7b6be5a355bfea61\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb15aeac3381993b7b6be5a355bfea61\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\200e032c88fe4eeaab6cfc9d3de54cb0\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\200e032c88fe4eeaab6cfc9d3de54cb0\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\993e9886b0746368e368e024aca617d1\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\993e9886b0746368e368e024aca617d1\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41e7aa391a69310f9c865af9efcc14ac\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\41e7aa391a69310f9c865af9efcc14ac\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\973fffd5662755d4e69b886e239d1df3\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\973fffd5662755d4e69b886e239d1df3\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92383f2905d19505013069f0bdc93d86\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\92383f2905d19505013069f0bdc93d86\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d8a2a37b30a9c89433a23bfbe2c62e\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d8a2a37b30a9c89433a23bfbe2c62e\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b10410589cd543d28a5071420020c9e\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b10410589cd543d28a5071420020c9e\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c93d95e280558f6ca9e7696f6cbd2cdc\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c93d95e280558f6ca9e7696f6cbd2cdc\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84c2e1fdf67a20a30b80c97a36fac620\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84c2e1fdf67a20a30b80c97a36fac620\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa74fe9b56d39051c08965a002f0b9a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa74fe9b56d39051c08965a002f0b9a\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47c4be2db69016738bbafb806598f314\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\47c4be2db69016738bbafb806598f314\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e80fdcd82127bd0c9c0544df1e2cfa\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e80fdcd82127bd0c9c0544df1e2cfa\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06e5a4bc13c42f302c7d1242ac46a49c\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource-okhttp:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\06e5a4bc13c42f302c7d1242ac46a49c\transformed\media3-datasource-okhttp-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\211be35423f7bfaec0e55aefa1405712\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\211be35423f7bfaec0e55aefa1405712\transformed\media3-extractor-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0392dcde0f5057ee09db92292159e852\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0392dcde0f5057ee09db92292159e852\transformed\media3-container-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a220c0850ea7a4312f5767b5b3a47390\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a220c0850ea7a4312f5767b5b3a47390\transformed\media3-datasource-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103d981b5962056e853bb637be791128\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\103d981b5962056e853bb637be791128\transformed\media3-decoder-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\529f1223e88b2da943ded65d9d311cec\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\529f1223e88b2da943ded65d9d311cec\transformed\media3-database-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af51e38e80ddf7c842cfbea983da50d3\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\af51e38e80ddf7c842cfbea983da50d3\transformed\media3-exoplayer-dash-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a3097707dc566d1228ea0db1842b762\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a3097707dc566d1228ea0db1842b762\transformed\media3-exoplayer-hls-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae019187e5c516a5cb0b7ac11247c8c7\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae019187e5c516a5cb0b7ac11247c8c7\transformed\media3-exoplayer-smoothstreaming-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\448ca3090c033310e421ff7270af74f2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\448ca3090c033310e421ff7270af74f2\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d51b6e88007b5429952582adf6ad94\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\83d51b6e88007b5429952582adf6ad94\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675f4f7d07ab458ad78ae7586d1e5993\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675f4f7d07ab458ad78ae7586d1e5993\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\942d929df50e13a06eea64c3f56ce2e6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\942d929df50e13a06eea64c3f56ce2e6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc83fc3662b1f056d8c5fcf6cc0b2ce8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fc83fc3662b1f056d8c5fcf6cc0b2ce8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\504eeb7c7bcb8f63fc45f7fe459b21ca\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\504eeb7c7bcb8f63fc45f7fe459b21ca\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a69fcdfcfaf951b7d4c1b7aa539671\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a69fcdfcfaf951b7d4c1b7aa539671\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93599d426d717b29c71811f738de6550\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93599d426d717b29c71811f738de6550\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbb53b0031d6b8cf2af31614c779d6c\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbb53b0031d6b8cf2af31614c779d6c\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae76c217763a0357fb15e4bf3b4067b3\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae76c217763a0357fb15e4bf3b4067b3\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30816eecce2e37d429d2df995908dc08\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\30816eecce2e37d429d2df995908dc08\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10f2e59836afcfaeb7024ffa9f730642\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\10f2e59836afcfaeb7024ffa9f730642\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a337a35254edb8a41af5a93dcd364b0a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a337a35254edb8a41af5a93dcd364b0a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f83150c8cd0912cbab709d7028c29e4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8f83150c8cd0912cbab709d7028c29e4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b1efdd330a6997d521c41b8a19879f0\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b1efdd330a6997d521c41b8a19879f0\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52de6cc1bf4e0cbce2ccf873236402e3\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\52de6cc1bf4e0cbce2ccf873236402e3\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad04b768963391ba6665aa73cb02336d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad04b768963391ba6665aa73cb02336d\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb3414a5b9006ede02e6334a5c1c6e5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb3414a5b9006ede02e6334a5c1c6e5\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88f934b05d112786789c29637c6dc478\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\88f934b05d112786789c29637c6dc478\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60de6752652a833c9a313b58ddaff005\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60de6752652a833c9a313b58ddaff005\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\900e1bfa4bca01d85722b25f0cd283a5\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\900e1bfa4bca01d85722b25f0cd283a5\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\385207845dcfb3956e3a4c461df893df\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\385207845dcfb3956e3a4c461df893df\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ce4db16154b1473cd097236076b5c4a\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ce4db16154b1473cd097236076b5c4a\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\417f62f3d94f17f3e5d09a4e5b5cb22f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\417f62f3d94f17f3e5d09a4e5b5cb22f\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\484febe24aacd0df853b318a482220cb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\484febe24aacd0df853b318a482220cb\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d327f7d72e200149bb16e5512a317\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\946d327f7d72e200149bb16e5512a317\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ac8370b68d7cf5c72cf3ece9b72580\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\68ac8370b68d7cf5c72cf3ece9b72580\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3236732610ecd0bdf86bfde650060d85\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3236732610ecd0bdf86bfde650060d85\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc9f8e9a1a6d58dfeddb1833d8dc0f9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc9f8e9a1a6d58dfeddb1833d8dc0f9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc8aa523a6c477e084b3ea9cd8fb499\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc8aa523a6c477e084b3ea9cd8fb499\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1735a5bdc5556b5fcd9734fc7bce3c\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1735a5bdc5556b5fcd9734fc7bce3c\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2727b732b2ccccec1ebeef9a3638abbf\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2727b732b2ccccec1ebeef9a3638abbf\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2ad778840a272b59e429623bbe3eda\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2ad778840a272b59e429623bbe3eda\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0caca4f943fdf24a419b6298848cfc96\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0caca4f943fdf24a419b6298848cfc96\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc14df0b6916a817c4ff4d00c27f2b4\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc14df0b6916a817c4ff4d00c27f2b4\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6550963f3403c0d1d6c453e7b7e3228\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6550963f3403c0d1d6c453e7b7e3228\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\df75d6af1b7fa262029bb3cf0826a17c\transformed\hermes-android-0.79.5-release\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\df75d6af1b7fa262029bb3cf0826a17c\transformed\hermes-android-0.79.5-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b214cd1187ca8ca8f548d8878a9b523\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b214cd1187ca8ca8f548d8878a9b523\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Dimezis:BlurView:version-2.0.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c942b0acc1f7702ab490f1c891f0cfb5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c942b0acc1f7702ab490f1c891f0cfb5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02014e6ae21bddbc8831a1642129fc1d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02014e6ae21bddbc8831a1642129fc1d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e440d6d7f8b81f1ee5821c2f7734b6c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5e440d6d7f8b81f1ee5821c2f7734b6c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\af53d4f4b13e08684217b30f24915ec1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\af53d4f4b13e08684217b30f24915ec1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd49a2bc21d7481306f5fab22b13b93\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd49a2bc21d7481306f5fab22b13b93\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efe3a061c6deb0a5c90e5593eb0157f8\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\efe3a061c6deb0a5c90e5593eb0157f8\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\819b0f19fd7a4e0628a596acf3c0edf7\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\819b0f19fd7a4e0628a596acf3c0edf7\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98254bf55b4a99883211a00c74ee064c\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\98254bf55b4a99883211a00c74ee064c\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982380dbe80441682c717ab01f65a7f9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\982380dbe80441682c717ab01f65a7f9\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76679a59b9f9ae735a48e7d215f630b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76679a59b9f9ae735a48e7d215f630b1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\353c17a77463ee884dbc33275c068645\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\353c17a77463ee884dbc33275c068645\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db2b7a71f5dbcd5883ac91edcd6931d\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db2b7a71f5dbcd5883ac91edcd6931d\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6937096d739cfeb784d5c810ef736db\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6937096d739cfeb784d5c810ef736db\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\072824e130dfb32121ac6c53e61c0c7e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\072824e130dfb32121ac6c53e61c0c7e\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cefce8cad880e9116d74623f30bb2411\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cefce8cad880e9116d74623f30bb2411\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a750665b475a53f1748cb783f578fd\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a750665b475a53f1748cb783f578fd\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\207646751c7680dd4c2eebb78ef5b048\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\207646751c7680dd4c2eebb78ef5b048\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f5330b574122aa672d96a0f5922e634\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f5330b574122aa672d96a0f5922e634\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
	android:resource
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
	android:name
		ADDED from [host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
action#android.intent.action.SEND
ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
	android:name
		ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
provider#expo.modules.sharing.SharingFileProvider
ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
	android:grantUriPermissions
		ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
	android:exported
		ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:10:5-12:47
	android:protectionLevel
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:12:9-44
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:11:9-63
permission#com.cravin.merchant.permission.C2D_MESSAGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:10:5-12:47
	android:protectionLevel
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:12:9-44
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:11:9-63
uses-permission#${applicationId}.permission.C2D_MESSAGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:22-76
uses-permission#com.cravin.merchant.permission.C2D_MESSAGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:5-79
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:17:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:17:22-74
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:19:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:19:22-79
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:31:5-86
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:31:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:32:5-87
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:32:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:33:5-81
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:33:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:34:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:35:5-88
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:35:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:36:5-92
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:36:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:37:5-84
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:37:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:38:5-83
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:38:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:39:5-91
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:39:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:40:5-92
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:40:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:41:5-93
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:41:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:43:5-82
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:43:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:44:5-83
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:44:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:45:5-88
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:45:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:46:5-89
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:46:22-86
receiver#com.onesignal.notifications.receivers.FCMBroadcastReceiver
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:49:9-60:20
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:52:13-73
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:50:13-86
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:${applicationId}
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:13-59:29
	android:priority
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:28-50
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE+category:name:com.cravin.merchant
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:13-59:29
	android:priority
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:28-50
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:17-81
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:25-78
category#${applicationId}
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:17-61
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:27-58
category#com.cravin.merchant
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:17-61
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:27-58
service#com.onesignal.notifications.services.HmsMessageServiceOneSignal
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:62:9-68:19
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:64:13-37
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:63:13-91
intent-filter#action:name:com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:65:13-67:29
action#com.huawei.push.action.MESSAGING_EVENT
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:66:17-81
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:66:25-78
activity#com.onesignal.NotificationOpenedActivityHMS
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:69:9-77:20
	android:noHistory
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:72:13-37
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:71:13-36
	android:theme
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:73:13-72
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:70:13-71
intent-filter#action:name:android.intent.action.VIEW
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:74:13-76:29
receiver#com.onesignal.notifications.receivers.NotificationDismissReceiver
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:79:9-81:39
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:81:13-36
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:80:13-93
receiver#com.onesignal.notifications.receivers.BootUpReceiver
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:82:9-89:20
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:83:13-80
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:85:13-88:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:17-79
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:25-76
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:87:17-82
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:87:25-79
receiver#com.onesignal.notifications.receivers.UpgradeReceiver
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:90:9-96:20
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:92:13-36
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:91:13-81
intent-filter#action:name:android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:93:13-95:29
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:94:17-84
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:94:25-81
activity#com.onesignal.notifications.activities.NotificationOpenedActivity
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:98:9-104:75
	android:excludeFromRecents
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:100:13-46
	android:noHistory
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:102:13-37
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:101:13-36
	android:theme
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:99:13-93
activity#com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder
ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:105:9-110:75
	android:excludeFromRecents
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:107:13-46
	android:noHistory
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:109:13-37
	android:exported
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:108:13-36
	android:theme
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:110:13-72
	android:name
		ADDED from [com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:106:13-110
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:10:22-76
service#com.onesignal.core.services.SyncJobService
ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:13:9-16:72
	android:exported
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:15:13-37
	android:permission
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:16:13-69
	android:name
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:14:13-70
activity#com.onesignal.core.activities.PermissionsActivity
ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:18:9-21:75
	android:exported
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:20:13-37
	android:theme
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:21:13-72
	android:name
		ADDED from [com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:19:13-77
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:50:13-52:29
	android:priority
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:50:28-51
action#com.google.firebase.MESSAGING_EVENT
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:51:17-78
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:51:25-75
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.cravin.merchant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.cravin.merchant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
