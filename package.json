{"name": "cravin-app", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css", "build": "eas build --platform android --profile production", "build:apk": "eas build --platform android --profile production-apk", "build:ios": "eas build --platform ios --profile production", "build:all": "eas build --platform all --profile production", "build:preview": "eas build --platform android --profile preview", "build:dev": "eas build --platform android --profile development", "build:local": "eas build --platform android --profile production --local", "build:status": "eas build:list", "submit": "eas submit --platform android --profile production", "submit:ios": "eas submit --platform ios --profile production"}, "dependencies": {"@expo-google-fonts/inter": "^0.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.0.0", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "~1.2.0", "@rn-primitives/slot": "~1.2.0", "@rn-primitives/tooltip": "~1.2.0", "@tanstack/react-query": "^5.80.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "expo": "53.0.17", "expo-asset": "^11.1.7", "expo-audio": "~0.4.8", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-constants": "^17.1.6", "expo-dev-client": "~5.2.4", "expo-device": "^7.1.4", "expo-file-system": "^18.1.11", "expo-haptics": "^14.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-navigation-bar": "~4.2.7", "expo-print": "^14.1.4", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "onesignal-expo-plugin": "^2.0.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-onesignal": "^5.2.12", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.2", "expo-sharing": "~13.1.5"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.14", "typescript": "^5.8.3"}, "private": true}