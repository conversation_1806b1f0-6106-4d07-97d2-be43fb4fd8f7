{"logs": [{"outputFile": "com.cravin.merchant.app-mergeReleaseResources-71:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "62,63,64,65,66,67,68,245", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4588,4685,4787,4885,4989,5092,5194,20615", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "4680,4782,4880,4984,5087,5189,5306,20711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b1efdd330a6997d521c41b8a19879f0\\transformed\\exoplayer-ui-2.18.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,645,958,1045,1133,1216,1314,1415,1498,1563,1660,1754,1825,1895,1959,2027,2149,2277,2399,2476,2556,2629,2709,2816,2924,2992,3057,3110,3168,3216,3277,3347,3416,3479,3544,3607,3664,3740,3809,3883,3935,3998,4075,4152", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "318,640,953,1040,1128,1211,1309,1410,1493,1558,1655,1749,1820,1890,1954,2022,2144,2272,2394,2471,2551,2624,2704,2811,2919,2987,3052,3105,3163,3211,3272,3342,3411,3474,3539,3602,3659,3735,3804,3878,3930,3993,4070,4147,4201"}, "to": {"startLines": "2,11,17,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,740,8369,8456,8544,8627,8725,8826,8909,8974,9071,9165,9236,9306,9370,9438,9560,9688,9810,9887,9967,10040,10120,10227,10335,10403,11144,11197,11255,11303,11364,11434,11503,11566,11631,11694,11751,11827,11896,11970,12022,12085,12162,12239", "endLines": "10,16,22,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "endColumns": "17,12,12,86,87,82,97,100,82,64,96,93,70,69,63,67,121,127,121,76,79,72,79,106,107,67,64,52,57,47,60,69,68,62,64,62,56,75,68,73,51,62,76,76,53", "endOffsets": "413,735,1048,8451,8539,8622,8720,8821,8904,8969,9066,9160,9231,9301,9365,9433,9555,9683,9805,9882,9962,10035,10115,10222,10330,10398,10463,11192,11250,11298,11359,11429,11498,11561,11626,11689,11746,11822,11891,11965,12017,12080,12157,12234,12288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,212,283,352,433,504,571,641,724,807,889,961,1035,1117,1194,1276,1358,1434,1512,1589,1673,1747,1829,1901", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,207,278,347,428,499,566,636,719,802,884,956,1030,1112,1189,1271,1353,1429,1507,1584,1668,1742,1824,1896,1978"}, "to": {"startLines": "56,72,161,163,164,166,180,181,182,229,230,231,232,237,238,239,240,241,242,243,244,246,247,248,249", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4060,5627,13994,14141,14210,14354,15396,15463,15533,19341,19424,19506,19578,19977,20059,20136,20218,20300,20376,20454,20531,20716,20790,20872,20944", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "4128,5706,14060,14205,14286,14420,15458,15528,15611,19419,19501,19573,19647,20054,20131,20213,20295,20371,20449,20526,20610,20785,20867,20939,21021"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd29036936cb81e953b907f3a3c29b2f\\transformed\\material-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,367,456,545,633,731,822,928,1054,1138,1202,1268,1362,1438,1501,1613,1673,1738,1792,1862,1922,1978,2090,2147,2209,2265,2338,2472,2557,2634,2723,2804,2889,3032,3116,3199,3333,3422,3499,3555,3610,3676,3749,3826,3897,3976,4050,4126,4201,4274,4379,4467,4540,4630,4721,4793,4867,4958,5010,5092,5159,5243,5330,5392,5456,5519,5588,5691,5799,5897,6001,6061,6120,6197,6284,6360", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "362,451,540,628,726,817,923,1049,1133,1197,1263,1357,1433,1496,1608,1668,1733,1787,1857,1917,1973,2085,2142,2204,2260,2333,2467,2552,2629,2718,2799,2884,3027,3111,3194,3328,3417,3494,3550,3605,3671,3744,3821,3892,3971,4045,4121,4196,4269,4374,4462,4535,4625,4716,4788,4862,4953,5005,5087,5154,5238,5325,5387,5451,5514,5583,5686,5794,5892,5996,6056,6115,6192,6279,6355,6433"}, "to": {"startLines": "23,57,58,59,60,61,69,70,71,94,95,147,162,165,167,168,169,170,171,172,173,174,175,176,177,178,179,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,234,235,236", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1053,4133,4222,4311,4399,4497,5311,5417,5543,8239,8303,12293,14065,14291,14425,14537,14597,14662,14716,14786,14846,14902,15014,15071,15133,15189,15262,15616,15701,15778,15867,15948,16033,16176,16260,16343,16477,16566,16643,16699,16754,16820,16893,16970,17041,17120,17194,17270,17345,17418,17523,17611,17684,17774,17865,17937,18011,18102,18154,18236,18303,18387,18474,18536,18600,18663,18732,18835,18943,19041,19145,19205,19264,19736,19823,19899", "endLines": "28,57,58,59,60,61,69,70,71,94,95,147,162,165,167,168,169,170,171,172,173,174,175,176,177,178,179,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,234,235,236", "endColumns": "12,88,88,87,97,90,105,125,83,63,65,93,75,62,111,59,64,53,69,59,55,111,56,61,55,72,133,84,76,88,80,84,142,83,82,133,88,76,55,54,65,72,76,70,78,73,75,74,72,104,87,72,89,90,71,73,90,51,81,66,83,86,61,63,62,68,102,107,97,103,59,58,76,86,75,77", "endOffsets": "1315,4217,4306,4394,4492,4583,5412,5538,5622,8298,8364,12382,14136,14349,14532,14592,14657,14711,14781,14841,14897,15009,15066,15128,15184,15257,15391,15696,15773,15862,15943,16028,16171,16255,16338,16472,16561,16638,16694,16749,16815,16888,16965,17036,17115,17189,17265,17340,17413,17518,17606,17679,17769,17860,17932,18006,18097,18149,18231,18298,18382,18469,18531,18595,18658,18727,18830,18938,19036,19140,19200,19259,19336,19818,19894,19972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "92,148,149,150", "startColumns": "4,4,4,4", "startOffsets": "8042,12387,12491,12603", "endColumns": "105,103,111,101", "endOffsets": "8143,12486,12598,12700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250db4afb8fa7de0d997879e3d4adf3d\\transformed\\media3-exoplayer-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,183,247,311,386,467,566,657", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "118,178,242,306,381,462,561,652,726"}, "to": {"startLines": "120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10468,10536,10596,10660,10724,10799,10880,10979,11070", "endColumns": "67,59,63,63,74,80,98,90,73", "endOffsets": "10531,10591,10655,10719,10794,10875,10974,11065,11139"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4689ac814784dd79f2206f332baa7585\\transformed\\play-services-basement-18.1.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6713", "endColumns": "139", "endOffsets": "6848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02a6c65af6aca4fe0b17434c7fe8f02b\\transformed\\play-services-base-18.0.1\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5711,5817,5982,6111,6218,6365,6495,6609,6853,7011,7118,7285,7415,7567,7719,7789,7851", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "5812,5977,6106,6213,6360,6490,6604,6708,7006,7113,7280,7410,7562,7714,7784,7846,7930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,233", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1320,1432,1534,1642,1729,1832,1951,2032,2110,2202,2296,2391,2485,2580,2674,2770,2870,2962,3054,3138,3246,3354,3454,3567,3675,3780,3960,19652", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "1427,1529,1637,1724,1827,1946,2027,2105,2197,2291,2386,2480,2575,2669,2765,2865,2957,3049,3133,3241,3349,3449,3562,3670,3775,3955,4055,19731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4d7ecf19998ab3515e1b90137c4d08f\\transformed\\biometric-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,253,374,512,642,768,893,1033,1132,1275,1409", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "157,248,369,507,637,763,888,1028,1127,1270,1404,1537"}, "to": {"startLines": "91,93,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7935,8148,12705,12826,12964,13094,13220,13345,13485,13584,13727,13861", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "8037,8234,12821,12959,13089,13215,13340,13480,13579,13722,13856,13989"}}]}]}