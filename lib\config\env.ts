// Environment configuration
export const ENV = {
  // API Configuration
  API_BASE_URL: 'https://cravin-food-backend-iwz2.onrender.com',
  // API_BASE_URL:"https://backend.justcravin.com/food/api/v1",
  
  // Auth Configuration
  AUTH_TOKEN_KEY: 'auth_token',
  REFRESH_TOKEN_KEY: 'refresh_token',
  
  // App Configuration
  APP_VERSION: '1.0.0',
  ENVIRONMENT: process.env.NODE_ENV || 'development',
  
  // OneSignal Configuration
  ONESIGNAL_APP_ID: process.env.EXPO_PUBLIC_ONESIGNAL_APP_ID || "************************************",
  
  // Feature Flags
  ENABLE_MOCK_DATA: process.env.EXPO_PUBLIC_ENABLE_MOCK_DATA === 'true' || false, // Enable mock data by default for development
  ENABLE_REAL_TIME_UPDATES: process.env.EXPO_PUBLIC_ENABLE_REAL_TIME_UPDATES === 'true' || true,
  ENABLE_ONESIGNAL_NOTIFICATIONS: process.env.EXPO_PUBLIC_ENABLE_ONESIGNAL === 'true' || true,
  
  // Intervals (in milliseconds)
  NEW_ORDERS_REFETCH_INTERVAL: 5000, // 5 seconds
  ACCEPTED_ORDERS_REFETCH_INTERVAL: 5000, // 5 seconds
  PAST_ORDERS_REFETCH_INTERVAL: 20000, // 20 seconds
  
  // Query Configuration
  DEFAULT_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_CACHE_TIME: 10 * 60 * 1000, // 10 minutes
};

// Type definitions for environment
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  retries: number;
}

export const getApiConfig = (): ApiConfig => ({
  baseUrl: ENV.API_BASE_URL,
  timeout: 30000, // 30 seconds
  retries: 3,
});

// Helper function to determine if we're in development
export const isDevelopment = () => ENV.ENVIRONMENT === 'development';

// Helper function to determine if we're using mock data
export const shouldUseMockData = () => false; 