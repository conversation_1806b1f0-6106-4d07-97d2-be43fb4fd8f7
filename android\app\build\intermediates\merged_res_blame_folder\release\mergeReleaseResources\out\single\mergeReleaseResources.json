[{"merged": "com.cravin.merchant.app-release-73:/raw_keep.xml.flat", "source": "com.cravin.merchant.app-res-68:/raw/keep.xml"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_assets_sounds_ordernotification.wav.flat", "source": "com.cravin.merchant.app-res-68:/raw/assets_sounds_ordernotification.wav"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_100thin_italic_inter_100thin_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_100thin_italic_inter_100thin_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_400regular_inter_400regular.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_400regular_inter_400regular.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-hdpi_splashscreen_logo.png.flat", "source": "com.cravin.merchant.app-main-74:/drawable-hdpi/splashscreen_logo.png"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_600semibold_italic_inter_600semibold_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_600semibold_italic_inter_600semibold_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "com.cravin.merchant.app-main-74:/drawable-xxxhdpi/splashscreen_logo.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable_notification_icon.xml.flat", "source": "com.cravin.merchant.app-main-74:/drawable/notification_icon.xml"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_800extrabold_italic_inter_800extrabold_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_800extrabold_italic_inter_800extrabold_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_500medium_inter_500medium.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_500medium_inter_500medium.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_200extralight_italic_inter_200extralight_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_200extralight_italic_inter_200extralight_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_700bold_italic_inter_700bold_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_700bold_italic_inter_700bold_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_notification_old.wav.flat", "source": "com.cravin.merchant.app-main-74:/raw/notification_old.wav"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable_rn_edit_text_material.xml.flat", "source": "com.cravin.merchant.app-main-74:/drawable/rn_edit_text_material.xml"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_searchicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_searchicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_assets_images_cravincommercelogo.webp.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/assets_images_cravincommercelogo.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_500medium_italic_inter_500medium_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_500medium_italic_inter_500medium_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable_ic_launcher_background.xml.flat", "source": "com.cravin.merchant.app-main-74:/drawable/ic_launcher_background.xml"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_forward.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_forward.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_400regular_italic_inter_400regular_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_400regular_italic_inter_400regular_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_sitemap.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_sitemap.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_900black_inter_900black.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_900black_inter_900black.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_error.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_error.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_notification.wav.flat", "source": "com.cravin.merchant.app-main-74:/raw/notification.wav"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_closeicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_closeicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_assets_sounds_notification.wav.flat", "source": "com.cravin.merchant.app-res-68:/raw/assets_sounds_notification.wav"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_200extralight_inter_200extralight.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_200extralight_inter_200extralight.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_pkg.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_pkg.png"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_300light_italic_inter_300light_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_300light_italic_inter_300light_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_100thin_inter_100thin.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_100thin_inter_100thin.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_splashscreen_logo.png.flat", "source": "com.cravin.merchant.app-main-74:/drawable-mdpi/splashscreen_logo.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_900black_italic_inter_900black_italic.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_900black_italic_inter_900black_italic.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxhdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxhdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_arrow_down.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_arrow_down.png"}, {"merged": "com.cravin.merchant.app-release-73:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.cravin.merchant.app-main-74:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_700bold_inter_700bold.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_700bold_inter_700bold.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xhdpi_splashscreen_logo.png.flat", "source": "com.cravin.merchant.app-main-74:/drawable-xhdpi/splashscreen_logo.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_800extrabold_inter_800extrabold.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_800extrabold_inter_800extrabold.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_300light_inter_300light.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_300light_inter_300light.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_assets_images_cravinlogo.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/assets_images_cravinlogo.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_assets_images_cravinfoodlogo.webp.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/assets_images_cravinfoodlogo.webp"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxhdpi_splashscreen_logo.png.flat", "source": "com.cravin.merchant.app-main-74:/drawable-xxhdpi/splashscreen_logo.png"}, {"merged": "com.cravin.merchant.app-release-73:/raw_node_modules_expogooglefonts_inter_600semibold_inter_600semibold.ttf.flat", "source": "com.cravin.merchant.app-res-68:/raw/node_modules_expogooglefonts_inter_600semibold_inter_600semibold.ttf"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-xxxhdpi_node_modules_reactnavigation_elements_lib_module_assets_backicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-xxxhdpi/node_modules_reactnavigation_elements_lib_module_assets_backicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_backiconmask.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_reactnavigation_elements_lib_module_assets_clearicon.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_reactnavigation_elements_lib_module_assets_clearicon.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_unmatched.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_unmatched.png"}, {"merged": "com.cravin.merchant.app-release-73:/drawable-mdpi_node_modules_exporouter_assets_file.png.flat", "source": "com.cravin.merchant.app-res-68:/drawable-mdpi/node_modules_exporouter_assets_file.png"}]