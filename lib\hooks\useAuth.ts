import { useMemo } from 'react';
import { useAuthStore } from '../stores/auth-store';

/**
 * Global hook to access authentication state from Zustand store.
 * Uses stable selectors to prevent unnecessary re-renders.
 */
export const useAuth = () => {
  // Create stable selectors
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const hasHydrated = useAuthStore((state) => state.hasHydrated);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    session: user ? { user } : null,
    isAuthenticated,
    isLoading: !hasHydrated, // Loading is true until hydration completes
  }), [user, isAuthenticated, hasHydrated]);
};