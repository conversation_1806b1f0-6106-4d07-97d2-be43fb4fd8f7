const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Custom resolver to handle ~ alias properly with Expo's existing resolver
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Handle ~ alias - resolve to absolute path from project root
  if (moduleName.startsWith('~/')) {
    const absolutePath = path.resolve(__dirname, moduleName.slice(2));
    
    // Check if the resolved path exists and return it
    try {
      const fs = require('fs');
      // Try different extensions that Metro supports
      const extensions = ['.ts', '.tsx', '.js', '.jsx', '.json', '.css', '.scss', '.sass'];
      
      for (const ext of extensions) {
        const fullPath = absolutePath + ext;
        if (fs.existsSync(fullPath)) {
          return {
            filePath: fullPath,
            type: 'sourceFile',
          };
        }
      }
      
      // Also check if it's a directory with an index file
      for (const ext of extensions) {
        const indexPath = path.join(absolutePath, 'index' + ext);
        if (fs.existsSync(indexPath)) {
          return {
            filePath: indexPath,
            type: 'sourceFile',
          };
        }
      }
    } catch (error) {
      // Fall through to default resolver if file system check fails
    }
  }
  
  // Ensure you call the default resolver for all other cases
  return context.resolveRequest(context, moduleName, platform);
};

module.exports = withNativeWind(config, { input: './global.css' });
