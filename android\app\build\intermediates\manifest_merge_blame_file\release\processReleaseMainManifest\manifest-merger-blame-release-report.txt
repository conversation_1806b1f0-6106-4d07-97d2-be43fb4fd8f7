1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cravin.merchant"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:2:3-64
11-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
12-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:3:3-77
12-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:3:20-75
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:4:3-77
13-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:5:3-78
14-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:5:20-76
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:6:3-68
15-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:7:3-75
16-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:8:3-63
17-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:9:3-65
18-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:9:20-63
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:10:3-78
19-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:10:20-76
20
21    <queries>
21-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:11:3-17:13
22        <intent>
22-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:12:5-16:14
23            <action android:name="android.intent.action.VIEW" />
23-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:7-58
23-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:15-56
24
25            <category android:name="android.intent.category.BROWSABLE" />
25-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:7-67
25-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:17-65
26
27            <data android:scheme="https" />
27-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
27-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
28        </intent>
29        <!-- Query open documents -->
30        <intent>
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
31            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
31-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
32        </intent>
33        <intent>
33-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
34
35            <!-- Required for file sharing if targeting API 30 -->
36            <action android:name="android.intent.action.SEND" />
36-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
36-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
37
38            <data android:mimeType="*/*" />
38-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
39        </intent>
40    </queries>
41    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
42    <permission
42-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:10:5-12:47
43        android:name="com.cravin.merchant.permission.C2D_MESSAGE"
43-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:11:9-63
44        android:protectionLevel="signature" />
44-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:12:9-44
45
46    <uses-permission android:name="com.cravin.merchant.permission.C2D_MESSAGE" /> <!-- Required runtime permission to display notifications on Android 13 -->
46-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:5-79
46-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:14:22-76
47    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
47-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:17:5-77
47-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:17:22-74
48    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- START: ShortcutBadger -->
48-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:19:5-82
48-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:19:22-79
49    <!-- Samsung -->
50    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
50-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:31:5-86
50-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:31:22-83
51    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
51-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:32:5-87
51-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:32:22-84
52    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
52-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:33:5-81
52-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:33:22-78
53    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
53-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:34:5-83
53-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:34:22-80
54    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
54-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:35:5-88
54-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:35:22-85
55    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
55-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:36:5-92
55-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:36:22-89
56    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
56-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:37:5-84
56-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:37:22-81
57    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
57-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:38:5-83
57-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:38:22-80
58    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
58-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:39:5-91
58-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:39:22-88
59    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
59-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:40:5-92
59-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:40:22-89
60    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
60-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:41:5-93
60-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:41:22-90
61    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
61-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:42:5-73
61-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:42:22-70
62    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
62-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:43:5-82
62-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:43:22-79
63    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
63-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:44:5-83
63-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:44:22-80
64    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
64-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:45:5-88
64-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:45:22-85
65    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
65-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:46:5-89
65-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:46:22-86
66    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
66-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:10:5-79
66-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:10:22-76
67    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
67-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
67-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
68    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
68-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
68-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
69    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
69-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
69-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
70
71    <permission
71-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
72        android:name="com.cravin.merchant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
72-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
73        android:protectionLevel="signature" />
73-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
74
75    <uses-permission android:name="com.cravin.merchant.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
75-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
76
77    <application
77-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:3-40:17
78        android:name="com.cravin.merchant.MainApplication"
78-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:16-47
79        android:allowBackup="true"
79-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:162-188
80        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
80-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
81        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
81-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:307-376
82        android:extractNativeLibs="false"
83        android:fullBackupContent="@xml/secure_store_backup_rules"
83-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:248-306
84        android:icon="@mipmap/ic_launcher"
84-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:81-115
85        android:label="@string/app_name"
85-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:48-80
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:116-161
87        android:supportsRtl="true"
87-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:221-247
88        android:theme="@style/AppTheme" >
88-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:18:189-220
89        <meta-data
89-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:5-117
90            android:name="com.google.firebase.messaging.default_notification_channel_id"
90-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:16-92
91            android:value="orders" />
91-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:19:93-115
92        <meta-data
92-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:5-139
93            android:name="com.google.firebase.messaging.default_notification_color"
93-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:16-87
94            android:resource="@color/notification_icon_color" />
94-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:20:88-137
95        <meta-data
95-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:5-135
96            android:name="com.google.firebase.messaging.default_notification_icon"
96-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:16-86
97            android:resource="@drawable/notification_icon" />
97-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:21:87-133
98        <meta-data
98-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:5-83
99            android:name="expo.modules.updates.ENABLED"
99-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:16-59
100            android:value="false" />
100-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:22:60-81
101        <meta-data
101-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:5-105
102            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
102-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:16-80
103            android:value="ALWAYS" />
103-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:23:81-103
104        <meta-data
104-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:5-99
105            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
105-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:16-79
106            android:value="0" />
106-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:24:80-97
107
108        <activity
108-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:5-39:16
109            android:name="com.cravin.merchant.MainActivity"
109-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:15-43
110            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
110-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:44-134
111            android:exported="true"
111-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:256-279
112            android:launchMode="singleTask"
112-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:135-166
113            android:screenOrientation="portrait"
113-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:280-316
114            android:theme="@style/Theme.App.SplashScreen"
114-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:210-255
115            android:windowSoftInputMode="adjustResize" >
115-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:25:167-209
116            <intent-filter>
116-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:26:7-29:23
117                <action android:name="android.intent.action.MAIN" />
117-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:27:9-60
117-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:27:17-58
118
119                <category android:name="android.intent.category.LAUNCHER" />
119-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:28:9-68
119-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:28:19-66
120            </intent-filter>
121            <intent-filter>
121-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:30:7-38:23
122                <action android:name="android.intent.action.VIEW" />
122-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:7-58
122-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:15-56
123
124                <category android:name="android.intent.category.DEFAULT" />
124-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:32:9-67
124-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:32:19-65
125                <category android:name="android.intent.category.BROWSABLE" />
125-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:7-67
125-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:14:17-65
126
127                <data android:scheme="myapp" />
127-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
127-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
128                <data android:scheme="exp+cravin-app" />
128-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
128-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
129                <data android:scheme="exp+cravin-merchant" />
129-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
129-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
130                <data android:scheme="cravinmerchant" />
130-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:7-37
130-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:15:13-35
131            </intent-filter>
132        </activity>
133
134        <meta-data
134-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-11:89
135            android:name="org.unimodules.core.AppLoader#react-native-headless"
135-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-79
136            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
136-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-86
137        <meta-data
137-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-15:45
138            android:name="com.facebook.soloader.enabled"
138-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-57
139            android:value="true" />
139-->[:expo-modules-core] H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-33
140
141        <provider
141-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
142            android:name="expo.modules.filesystem.FileSystemFileProvider"
142-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
143            android:authorities="com.cravin.merchant.FileSystemFileProvider"
143-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
144            android:exported="false"
144-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
145            android:grantUriPermissions="true" >
145-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
146            <meta-data
146-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
147                android:name="android.support.FILE_PROVIDER_PATHS"
147-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
148                android:resource="@xml/file_system_provider_paths" />
148-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
149        </provider>
150        <provider
150-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
151            android:name="expo.modules.sharing.SharingFileProvider"
151-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
152            android:authorities="com.cravin.merchant.SharingFileProvider"
152-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
153            android:exported="false"
153-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
154            android:grantUriPermissions="true" >
154-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
155            <meta-data
155-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
156                android:name="android.support.FILE_PROVIDER_PATHS"
156-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
157                android:resource="@xml/sharing_provider_paths" />
157-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
158        </provider>
159
160        <receiver
160-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:49:9-60:20
161            android:name="com.onesignal.notifications.receivers.FCMBroadcastReceiver"
161-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:50:13-86
162            android:exported="true"
162-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:51:13-36
163            android:permission="com.google.android.c2dm.permission.SEND" >
163-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:52:13-73
164
165            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
166            <intent-filter android:priority="999" >
166-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:13-59:29
166-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:55:28-50
167                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
167-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:17-81
167-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:25-78
168
169                <category android:name="com.cravin.merchant" />
169-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:17-61
169-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:58:27-58
170            </intent-filter>
171        </receiver>
172
173        <service
173-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:62:9-68:19
174            android:name="com.onesignal.notifications.services.HmsMessageServiceOneSignal"
174-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:63:13-91
175            android:exported="false" >
175-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:64:13-37
176            <intent-filter>
176-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:65:13-67:29
177                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
177-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:66:17-81
177-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:66:25-78
178            </intent-filter>
179        </service> <!-- CAUTION: OneSignal backend includes the activity name in the payload, modifying the name without sync may result in notification click not firing -->
180        <activity
180-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:69:9-77:20
181            android:name="com.onesignal.NotificationOpenedActivityHMS"
181-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:70:13-71
182            android:exported="true"
182-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:71:13-36
183            android:noHistory="true"
183-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:72:13-37
184            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
184-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:73:13-72
185            <intent-filter>
185-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:74:13-76:29
186                <action android:name="android.intent.action.VIEW" />
186-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:7-58
186-->H:\Cravin\cravin-app\android\app\src\main\AndroidManifest.xml:13:15-56
187            </intent-filter>
188        </activity>
189
190        <receiver
190-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:79:9-81:39
191            android:name="com.onesignal.notifications.receivers.NotificationDismissReceiver"
191-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:80:13-93
192            android:exported="true" />
192-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:81:13-36
193        <receiver
193-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:82:9-89:20
194            android:name="com.onesignal.notifications.receivers.BootUpReceiver"
194-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:83:13-80
195            android:exported="true" >
195-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:84:13-36
196            <intent-filter>
196-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:85:13-88:29
197                <action android:name="android.intent.action.BOOT_COMPLETED" />
197-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:17-79
197-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:25-76
198                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
198-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:87:17-82
198-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:87:25-79
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:90:9-96:20
202            android:name="com.onesignal.notifications.receivers.UpgradeReceiver"
202-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:91:13-81
203            android:exported="true" >
203-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:92:13-36
204            <intent-filter>
204-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:93:13-95:29
205                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
205-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:94:17-84
205-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:94:25-81
206            </intent-filter>
207        </receiver>
208
209        <activity
209-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:98:9-104:75
210            android:name="com.onesignal.notifications.activities.NotificationOpenedActivity"
210-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:99:13-93
211            android:excludeFromRecents="true"
211-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:100:13-46
212            android:exported="true"
212-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:101:13-36
213            android:noHistory="true"
213-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:102:13-37
214            android:taskAffinity=""
214-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:103:13-36
215            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
215-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:104:13-72
216        <activity
216-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:105:9-110:75
217            android:name="com.onesignal.notifications.activities.NotificationOpenedActivityAndroid22AndOlder"
217-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:106:13-110
218            android:excludeFromRecents="true"
218-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:107:13-46
219            android:exported="true"
219-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:108:13-36
220            android:noHistory="true"
220-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:109:13-37
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
221-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:110:13-72
222
223        <service
223-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:13:9-16:72
224            android:name="com.onesignal.core.services.SyncJobService"
224-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:14:13-70
225            android:exported="false"
225-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:15:13-37
226            android:permission="android.permission.BIND_JOB_SERVICE" />
226-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:16:13-69
227
228        <activity
228-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:18:9-21:75
229            android:name="com.onesignal.core.activities.PermissionsActivity"
229-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:19:13-77
230            android:exported="false"
230-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:20:13-37
231            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
231-->[com.onesignal:core:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\AndroidManifest.xml:21:13-72
232
233        <receiver
233-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
234            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
234-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
235            android:exported="true"
235-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
236            android:permission="com.google.android.c2dm.permission.SEND" >
236-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
237            <intent-filter>
237-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
238                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
238-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:17-81
238-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:56:25-78
239            </intent-filter>
240
241            <meta-data
241-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
242                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
242-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
243                android:value="true" />
243-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
244        </receiver>
245        <!--
246             FirebaseMessagingService performs security checks at runtime,
247             but set to not exported to explicitly avoid allowing another app to call it.
248        -->
249        <service
249-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
250            android:name="com.google.firebase.messaging.FirebaseMessagingService"
250-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
251            android:directBootAware="true"
251-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
252            android:exported="false" >
252-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
253            <intent-filter android:priority="-500" >
253-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:50:13-52:29
253-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:50:28-51
254                <action android:name="com.google.firebase.MESSAGING_EVENT" />
254-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:51:17-78
254-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:51:25-75
255            </intent-filter>
256        </service>
257        <service
257-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
258            android:name="com.google.firebase.components.ComponentDiscoveryService"
258-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:55:13-84
259            android:directBootAware="true"
259-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
260            android:exported="false" >
260-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:56:13-37
261            <meta-data
261-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
262                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
262-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
264            <meta-data
264-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
265                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
265-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
267            <meta-data
267-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
268                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
268-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
270            <meta-data
270-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
271                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
271-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
273            <meta-data
273-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
274                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
274-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
276            <meta-data
276-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
277                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
277-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
279            <meta-data
279-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
280                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
280-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
281                android:value="com.google.firebase.components.ComponentRegistrar" />
281-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
282        </service>
283
284        <provider
284-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
285            android:name="com.google.firebase.provider.FirebaseInitProvider"
285-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
286            android:authorities="com.cravin.merchant.firebaseinitprovider"
286-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
287            android:directBootAware="true"
287-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
288            android:exported="false"
288-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
289            android:initOrder="100" />
289-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
290
291        <activity
291-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
292            android:name="com.google.android.gms.common.api.GoogleApiActivity"
292-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
293            android:exported="false"
293-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
294            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
294-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
295
296        <meta-data
296-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
297            android:name="com.google.android.gms.version"
297-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
298            android:value="@integer/google_play_services_version" />
298-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
299
300        <provider
300-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
301            android:name="androidx.startup.InitializationProvider"
301-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
302            android:authorities="com.cravin.merchant.androidx-startup"
302-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
303            android:exported="false" >
303-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
304            <meta-data
304-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
305                android:name="androidx.emoji2.text.EmojiCompatInitializer"
305-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
306                android:value="androidx.startup" />
306-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
307            <meta-data
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
308                android:name="androidx.work.WorkManagerInitializer"
308-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
309                android:value="androidx.startup" />
309-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
310            <meta-data
310-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
311                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
311-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
312                android:value="androidx.startup" />
312-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
313            <meta-data
313-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
314                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
314-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
315                android:value="androidx.startup" />
315-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
316        </provider>
317
318        <service
318-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
319            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
320            android:directBootAware="false"
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
321            android:enabled="@bool/enable_system_alarm_service_default"
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
322            android:exported="false" />
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
323        <service
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
324            android:name="androidx.work.impl.background.systemjob.SystemJobService"
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
325            android:directBootAware="false"
325-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
326            android:enabled="@bool/enable_system_job_service_default"
326-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
327            android:exported="true"
327-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
328            android:permission="android.permission.BIND_JOB_SERVICE" />
328-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
329        <service
329-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
330            android:name="androidx.work.impl.foreground.SystemForegroundService"
330-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
331            android:directBootAware="false"
331-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
332            android:enabled="@bool/enable_system_foreground_service_default"
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
333            android:exported="false" />
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
334
335        <receiver
335-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
336            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
336-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
337            android:directBootAware="false"
337-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
338            android:enabled="true"
338-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
339            android:exported="false" />
339-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
340        <receiver
340-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
341            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
341-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
342            android:directBootAware="false"
342-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
343            android:enabled="false"
343-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
344            android:exported="false" >
344-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
345            <intent-filter>
345-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
346                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
347                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
347-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
347-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
348            </intent-filter>
349        </receiver>
350        <receiver
350-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
351            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
351-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
352            android:directBootAware="false"
352-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
353            android:enabled="false"
353-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
354            android:exported="false" >
354-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
355            <intent-filter>
355-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
356                <action android:name="android.intent.action.BATTERY_OKAY" />
356-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
356-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
357                <action android:name="android.intent.action.BATTERY_LOW" />
357-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
357-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
358            </intent-filter>
359        </receiver>
360        <receiver
360-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
361            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
361-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
362            android:directBootAware="false"
362-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
363            android:enabled="false"
363-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
364            android:exported="false" >
364-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
365            <intent-filter>
365-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
366                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
366-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
366-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
367                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
367-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
367-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
368            </intent-filter>
369        </receiver>
370        <receiver
370-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
371            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
371-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
372            android:directBootAware="false"
372-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
373            android:enabled="false"
373-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
374            android:exported="false" >
374-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
375            <intent-filter>
375-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
376                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
376-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
376-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
377            </intent-filter>
378        </receiver>
379        <receiver
379-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
380            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
380-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
381            android:directBootAware="false"
381-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
382            android:enabled="false"
382-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
383            android:exported="false" >
383-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
384            <intent-filter>
384-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
385                <action android:name="android.intent.action.BOOT_COMPLETED" />
385-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:17-79
385-->[com.onesignal:notifications:5.1.34] C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\AndroidManifest.xml:86:25-76
386                <action android:name="android.intent.action.TIME_SET" />
386-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
386-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
387                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
387-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
387-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
388            </intent-filter>
389        </receiver>
390        <receiver
390-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
391            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
391-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
392            android:directBootAware="false"
392-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
393            android:enabled="@bool/enable_system_alarm_service_default"
393-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
394            android:exported="false" >
394-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
395            <intent-filter>
395-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
396                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
396-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
396-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
397            </intent-filter>
398        </receiver>
399        <receiver
399-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
400            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
400-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
401            android:directBootAware="false"
401-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
402            android:enabled="true"
402-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
403            android:exported="true"
403-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
404            android:permission="android.permission.DUMP" >
404-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
405            <intent-filter>
405-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
406                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
406-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
406-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
407            </intent-filter>
408        </receiver>
409
410        <service
410-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
411            android:name="androidx.room.MultiInstanceInvalidationService"
411-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
412            android:directBootAware="true"
412-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
413            android:exported="false" />
413-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
414
415        <receiver
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
416            android:name="androidx.profileinstaller.ProfileInstallReceiver"
416-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
417            android:directBootAware="false"
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
418            android:enabled="true"
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
419            android:exported="true"
419-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
420            android:permission="android.permission.DUMP" >
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
421            <intent-filter>
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
422                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
423            </intent-filter>
424            <intent-filter>
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
425                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
426            </intent-filter>
427            <intent-filter>
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
428                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
429            </intent-filter>
430            <intent-filter>
430-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
431                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
431-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
431-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
432            </intent-filter>
433        </receiver>
434
435        <service
435-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
436            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
436-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
437            android:exported="false" >
437-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
438            <meta-data
438-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
439                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
439-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
440                android:value="cct" />
440-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
441        </service>
442        <service
442-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
443            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
443-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
444            android:exported="false"
444-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
445            android:permission="android.permission.BIND_JOB_SERVICE" >
445-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
446        </service>
447
448        <receiver
448-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
449            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
449-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
450            android:exported="false" />
450-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
451    </application>
452
453</manifest>
