{"logs": [{"outputFile": "com.cravin.merchant.app-mergeReleaseResources-71:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "86,142,143,144", "startColumns": "4,4,4,4", "startOffsets": "7558,11773,11873,11986", "endColumns": "99,99,112,97", "endOffsets": "7653,11868,11981,12079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd29036936cb81e953b907f3a3c29b2f\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,88,89,141,156,159,161,162,163,164,165,166,167,168,169,170,171,172,173,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,228,229,230", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,3679,3778,3870,3951,4053,4861,4959,5081,7744,7803,11681,13468,13681,13808,13900,13965,14028,14090,14157,14221,14275,14380,14439,14500,14554,14623,14960,15043,15120,15210,15294,15378,15514,15593,15677,15799,15885,15963,16017,16068,16134,16203,16277,16348,16424,16496,16573,16644,16718,16829,16920,16999,17086,17174,17246,17320,17405,17456,17535,17602,17683,17767,17829,17893,17956,18024,18131,18230,18329,18424,18482,18537,19007,19088,19167", "endLines": "22,51,52,53,54,55,63,64,65,88,89,141,156,159,161,162,163,164,165,166,167,168,169,170,171,172,173,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,228,229,230", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "930,3773,3865,3946,4048,4128,4954,5076,5155,7798,7861,11768,13527,13736,13895,13960,14023,14085,14152,14216,14270,14375,14434,14495,14549,14618,14737,15038,15115,15205,15289,15373,15509,15588,15672,15794,15880,15958,16012,16063,16129,16198,16272,16343,16419,16491,16568,16639,16713,16824,16915,16994,17081,17169,17241,17315,17400,17451,17530,17597,17678,17762,17824,17888,17951,18019,18126,18225,18324,18419,18477,18532,18610,19083,19162,19250"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250db4afb8fa7de0d997879e3d4adf3d\\transformed\\media3-exoplayer-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9863,9936,9999,10063,10138,10219,10293,10387,10473", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "9931,9994,10058,10133,10214,10288,10382,10468,10541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b1efdd330a6997d521c41b8a19879f0\\transformed\\exoplayer-ui-2.18.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3410,3476,3528,3588,3671,3754", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3405,3471,3523,3583,3666,3749,3801"}, "to": {"startLines": "2,11,15,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,575,7866,7952,8039,8122,8210,8294,8362,8426,8524,8622,8687,8755,8821,8894,9014,9134,9252,9327,9409,9485,9553,9643,9734,9799,10546,10599,10659,10707,10768,10832,10903,10967,11032,11097,11156,11221,11285,11351,11403,11463,11546,11629", "endLines": "10,14,18,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "375,570,764,7947,8034,8117,8205,8289,8357,8421,8519,8617,8682,8750,8816,8889,9009,9129,9247,9322,9404,9480,9548,9638,9729,9794,9858,10594,10654,10702,10763,10827,10898,10962,11027,11092,11151,11216,11280,11346,11398,11458,11541,11624,11676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,282,350,431,498,565,639,716,798,877,946,1028,1111,1188,1271,1350,1427,1497,1566,1651,1731,1806", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,277,345,426,493,560,634,711,793,872,941,1023,1106,1183,1266,1345,1422,1492,1561,1646,1726,1801,1879"}, "to": {"startLines": "50,66,155,157,158,160,174,175,176,223,224,225,226,231,232,233,234,235,236,237,238,240,241,242", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3606,5160,13397,13532,13600,13741,14742,14809,14883,18615,18697,18776,18845,19255,19338,19415,19498,19577,19654,19724,19793,19979,20059,20134", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "3674,5238,13463,13595,13676,13803,14804,14878,14955,18692,18771,18840,18922,19333,19410,19493,19572,19649,19719,19788,19873,20054,20129,20207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4d7ecf19998ab3515e1b90137c4d08f\\transformed\\biometric-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,250,371,510,640,762,891,1027,1132,1284,1436", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "159,245,366,505,635,757,886,1022,1127,1279,1431,1558"}, "to": {"startLines": "85,87,145,146,147,148,149,150,151,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7449,7658,12084,12205,12344,12474,12596,12725,12861,12966,13118,13270", "endColumns": "108,85,120,138,129,121,128,135,104,151,151,126", "endOffsets": "7553,7739,12200,12339,12469,12591,12720,12856,12961,13113,13265,13392"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02a6c65af6aca4fe0b17434c7fe8f02b\\transformed\\play-services-base-18.0.1\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5243,5350,5507,5634,5744,5885,6010,6133,6385,6533,6641,6803,6931,7085,7241,7307,7370", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "5345,5502,5629,5739,5880,6005,6128,6232,6528,6636,6798,6926,7080,7236,7302,7365,7444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,227", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1038,1141,1252,1336,1436,1549,1626,1701,1794,1889,1984,2078,2180,2275,2372,2470,2566,2659,2739,2845,2944,3040,3145,3248,3350,3504,18927", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "1033,1136,1247,1331,1431,1544,1621,1696,1789,1884,1979,2073,2175,2270,2367,2465,2561,2654,2734,2840,2939,3035,3140,3243,3345,3499,3601,19002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "56,57,58,59,60,61,62,239", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4133,4228,4330,4428,4527,4635,4740,19878", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "4223,4325,4423,4522,4630,4735,4856,19974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4689ac814784dd79f2206f332baa7585\\transformed\\play-services-basement-18.1.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6237", "endColumns": "147", "endOffsets": "6380"}}]}]}