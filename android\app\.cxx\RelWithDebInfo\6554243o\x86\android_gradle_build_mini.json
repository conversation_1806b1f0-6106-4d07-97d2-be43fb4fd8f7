{"buildFiles": ["H:\\Cravin\\cravin-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "H:\\Cravin\\cravin-app\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\Cravin\\cravin-app\\android\\app\\.cxx\\RelWithDebInfo\\6554243o\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\Cravin\\cravin-app\\android\\app\\.cxx\\RelWithDebInfo\\6554243o\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "x86", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86", "output": "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f5330b574122aa672d96a0f5922e634\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86", "output": "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f5330b574122aa672d96a0f5922e634\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86", "output": "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libappmodules.so", "runtimeFiles": ["H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_safeareacontext.so", "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_rnscreens.so", "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f5330b574122aa672d96a0f5922e634\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "x86", "output": "H:\\Cravin\\cravin-app\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\6554243o\\obj\\x86\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\9616061a70c48f7f6f08941ffa751889\\transformed\\react-android-0.79.5-release\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7f5330b574122aa672d96a0f5922e634\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86", "runtimeFiles": []}}}