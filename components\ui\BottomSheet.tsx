import React, { useEffect, ReactNode } from 'react';
import { View, StyleSheet, Pressable, Dimensions } from 'react-native';
import { Gesture, GestureDetector, PanGestureHandlerEventPayload, GestureUpdateEvent } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useColorScheme } from '~/lib/useColorScheme';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface BottomSheetProps {
    children: ReactNode;
    isVisible: boolean;
    onClose: () => void;
}

const BottomSheet = ({ children, isVisible, onClose }: BottomSheetProps) => {
  const translateY = useSharedValue(SCREEN_HEIGHT);
  const context = useSharedValue({ y: 0 });
  const { isDarkColorScheme } = useColorScheme();

  const scrollTo = (destination: number) => {
    'worklet';
    if (destination === 0) {
      translateY.value = withSpring(0, { damping: 15, stiffness: 250 });
    } else {
      translateY.value = withTiming(SCREEN_HEIGHT, { duration: 250 }, () => {
        runOnJS(onClose)();
      });
    }
  };

  useEffect(() => {
    if (isVisible) {
      scrollTo(0);
    } else if (translateY.value < SCREEN_HEIGHT) { // Only animate close if it's visible
      scrollTo(SCREEN_HEIGHT);
    }
  }, [isVisible]);

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event: GestureUpdateEvent<PanGestureHandlerEventPayload>) => {
      translateY.value = event.translationY + context.value.y;
      translateY.value = Math.max(translateY.value, -SCREEN_HEIGHT * 0.1); // Allow slight over-drag up
    })
    .onEnd(() => {
      if (translateY.value > SCREEN_HEIGHT / 4) {
        scrollTo(SCREEN_HEIGHT);
      } else {
        scrollTo(0);
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });
  
  const backdropAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isVisible ? 1 : 0),
    };
  });

  // Early return if not visible and animation is complete
  if (!isVisible && translateY.value === SCREEN_HEIGHT) {
    return null;
  }

  return (
    <>
      <Pressable style={StyleSheet.absoluteFill} onPress={() => scrollTo(SCREEN_HEIGHT)} disabled={!isVisible}>
        <Animated.View style={[StyleSheet.absoluteFill, styles.backdrop, backdropAnimatedStyle]} />
      </Pressable>
      <GestureDetector gesture={gesture}>
        <Animated.View style={[
          styles.bottomSheetContainer, 
          { backgroundColor: isDarkColorScheme ? '#111827' : 'white' }, // dark:bg-gray-900
          animatedStyle
        ]}>
          <View style={[
            styles.line,
            { backgroundColor: isDarkColorScheme ? '#4B5563' : '#E5E5E5' } // dark:bg-gray-600
          ]} />
          {children}
        </Animated.View>
      </GestureDetector>
    </>
  );
};

const styles = StyleSheet.create({
  bottomSheetContainer: {
    height: SCREEN_HEIGHT,
    width: '100%',
    position: 'absolute',
    top: 0,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  line: {
    width: 75,
    height: 4,
    alignSelf: 'center',
    marginVertical: 15,
    borderRadius: 2,
  },
  backdrop: {
    backgroundColor: 'rgba(0,0,0,0.5)',
  }
});

export default BottomSheet; 