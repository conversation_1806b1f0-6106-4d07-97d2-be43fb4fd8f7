import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ordersApi, Order } from '../api/orders';
import { Alert } from 'react-native';
import { useAuth } from './useAuth';
import { useMemo, useEffect } from 'react';

// Query keys
export const orderKeys = {
  all: ['orders'] as const,
  lists: () => [...orderKeys.all, 'list'] as const,
  list: (filters: string) => [...orderKeys.lists(), { filters }] as const,
  details: () => [...orderKeys.all, 'detail'] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  newOrders: (restaurantId: string, branchId: string) => 
    [...orderKeys.all, 'initiated', restaurantId, branchId] as const,
  acceptedOrders: (restaurantId: string, branchId: string) => 
    [...orderKeys.all, 'accepted', restaurantId, branchId] as const,
  pastOrders: (restaurantId: string, branchId: string) => 
    [...orderKeys.all, 'past', restaurantId, branchId] as const,
  branches: (restaurantId: string) => 
    ['branches', restaurantId] as const,
};

// Custom hook to get user session with proper error handling and memoization
const useUserSession = () => {
    const { session, isAuthenticated } = useAuth();
    
    // Memoize the user object to prevent unnecessary re-renders
    return useMemo(() => {
        // Return null if not authenticated instead of throwing error
        if (!isAuthenticated || !session?.user) {
            return null;
        }
        
        return session.user;
    }, [isAuthenticated, session?.user?.user_id, session?.user?.restaurant_id, session?.user?.access_token]);
}

/**
 * Hook to fetch new orders (status: 'initiated')
 */
export const useNewOrders = () => {
    const user = useUserSession();

    // Extract stable values for dependencies
    const restaurantId = user?.restaurant_id;
    const branchId = user?.branch_ids?.[0]?.branch_id;
    const accessToken = user?.access_token;
    const isUserValid = !!(user && restaurantId && branchId && accessToken);

    // Create stable query key - only depend on the actual values we need
    const queryKey = useMemo(() => {
        if (!isUserValid) return ['orders', 'new', 'disabled'];
        return ['orders', 'initiated', restaurantId, branchId];
    }, [isUserValid, restaurantId, branchId]);

    return useQuery({
        queryKey,
        queryFn: async () => {
            if (!isUserValid) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getNewOrders(restaurantId!, branchId!, accessToken!);
        },
        enabled: isUserValid,
        refetchInterval: isUserValid ? 5000 : false,
        // Add these options to make queries more stable
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        staleTime: 0, // Always consider data stale for real-time updates
        notifyOnChangeProps: ['data', 'error', 'isLoading'],
    });
};

/**
 * Hook to fetch accepted orders
 */
export const useAcceptedOrders = () => {
    const user = useUserSession();

    // Extract stable values for dependencies
    const restaurantId = user?.restaurant_id;
    const branchId = user?.branch_ids?.[0]?.branch_id;
    const accessToken = user?.access_token;
    const isUserValid = !!(user && restaurantId && branchId && accessToken);

    // Create stable query key - only depend on the actual values we need
    const queryKey = useMemo(() => {
        if (!isUserValid) return ['orders', 'accepted', 'disabled'];
        return ['orders', 'accepted', restaurantId, branchId];
    }, [isUserValid, restaurantId, branchId]);

    return useQuery({
        queryKey,
        queryFn: async () => {
            if (!isUserValid) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getAcceptedOrders(restaurantId!, branchId!, accessToken!);
        },
        enabled: isUserValid,
        refetchInterval: isUserValid ? 5000 : false,
        // Add these options to make queries more stable
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        staleTime: 0, // Always consider data stale for real-time updates
        notifyOnChangeProps: ['data', 'error', 'isLoading'],
    });
};

/**
 * Hook to fetch past orders
 * @param branchId - Optional branch ID to filter past orders. If null, fetches for all branches.
 */
export const usePastOrders = (branchId?: string | null) => {
    const user = useUserSession();
    
    // Use provided branchId, or default for non-owner users.
    // For owners, branchId can be null (for all) or a specific string.
    const effectiveBranchId = branchId !== undefined ? branchId : user?.branch_ids[0].branch_id;

    // Create stable query key
    const queryKey = useMemo(() => {
        if (!user) return ['orders', 'past', 'disabled'];
        return orderKeys.pastOrders(user.restaurant_id, effectiveBranchId || 'all');
    }, [user?.restaurant_id, effectiveBranchId]);

    return useQuery({
        queryKey,
        queryFn: () => {
            if (!user) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getPastOrders(user.restaurant_id, effectiveBranchId || '', user.access_token);
        },
        enabled: !!user, // Only run query if user is authenticated
        refetchInterval: user ? 20000 : false, // Only refetch if user is authenticated
    });
};

/**
 * Hook to fetch merchant branches
 */
export const useBranches = () => {
    const user = useUserSession();

    // Create stable query key
    const queryKey = useMemo(() => {
        if (!user) return ['branches', 'disabled'];
        return orderKeys.branches(user.restaurant_id);
    }, [user?.restaurant_id]);

    return useQuery({
        queryKey,
        queryFn: () => {
            if (!user) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getBranches(user.restaurant_id, user.access_token);
        },
        enabled: !!user, // Only run query if user is authenticated
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
}

// Hook for accepting orders
export function useAcceptOrder() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: (order_id: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.acceptOrder(order_id, user.access_token);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.newOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been accepted successfully');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to accept order: ${error.message}`);
    },
  });
}

// OneSignal handles background notifications automatically - no background task management needed

// Enhanced hook for new orders with optimized React Query settings
export const useOptimizedNewOrders = () => {
  const user = useUserSession();

  // Extract stable values for dependencies
  const restaurantId = user?.restaurant_id;
  const branchId = user?.branch_ids?.[0]?.branch_id;
  const accessToken = user?.access_token;
  const isUserValid = !!(user && restaurantId && branchId && accessToken);

  // Create stable query key
  const queryKey = useMemo(() => {
    if (!isUserValid) return ['orders', 'new', 'disabled'];
    return ['orders', 'initiated', restaurantId, branchId];
  }, [isUserValid, restaurantId, branchId]);

  return useQuery({
    queryKey,
    queryFn: async () => {
      if (!isUserValid) {
        throw new Error('User not authenticated');
      }
      return ordersApi.getNewOrders(restaurantId!, branchId!, accessToken!);
    },
    enabled: isUserValid,
    // Optimized for 10-second background sync
    refetchInterval: isUserValid ? 5000 : false, // Frontend polls every 5s
    staleTime: 2000, // Consider data stale after 2 seconds
    gcTime: 10000, // Keep in cache for 10 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    // Performance optimizations
    notifyOnChangeProps: ['data', 'error', 'isLoading'],
    retry: (failureCount, error) => {
      // Quick retry for network errors, no retry for auth errors
      if (error.message.includes('401') || error.message.includes('403')) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 3000),
  });
};

// OneSignal handles background notifications automatically - no background sync needed

// Hook for rejecting orders
export function useRejectOrder() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: ({ order_id, rejected_reason }: { order_id: string; rejected_reason: string }) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.rejectOrder(order_id, user.access_token, rejected_reason);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.newOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      queryClient.invalidateQueries({ queryKey: orderKeys.pastOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been rejected');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to reject order: ${error.message}`);
    },
  });
}

// Hook for marking orders as ready
export function useMarkOrderReady() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: (order_id: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.markOrderReady(order_id, user.access_token);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been marked as ready');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to mark order as ready: ${error.message}`);
    },
  });
}

// Hook for completing orders
export function useCompleteOrder() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: (order_id: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.completeOrder(order_id, user.access_token);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      queryClient.invalidateQueries({ queryKey: orderKeys.pastOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been completed successfully');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to complete order: ${error.message}`);
    },
  });
}

// Hook for marking orders as out for delivery
export function useMarkOrderOutForDelivery() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: ({ order_id, driverInfo }: { order_id: string; driverInfo?: { name: string; number: string; id: string } }) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.markOrderOutForDelivery(order_id, user.access_token, driverInfo);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been marked as out for delivery');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to mark order as out for delivery: ${error.message}`);
    },
  });
}

// Hook for marking pickup orders as ready for pickup
export function useMarkOrderReadyForPickup() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: (order_id: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.markOrderReadyForPickup(order_id, user.access_token);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order is ready for pickup');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to mark order as ready for pickup: ${error.message}`);
    },
  });
}

// Hook for marking pickup orders as picked up  
export function useMarkOrderPickedUp() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  return useMutation({
    mutationFn: (order_id: string) => {
      if (!user) {
        throw new Error('User not authenticated');
      }
      return ordersApi.markOrderPickedUp(order_id, user.access_token);
    },
    onSuccess: () => {
      if (!user) return;
      
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      queryClient.invalidateQueries({ queryKey: orderKeys.pastOrders(user.restaurant_id, user.branch_ids[0].branch_id) });
      
      Alert.alert('Success', 'Order has been picked up successfully');
    },
    onError: (error: Error) => {
      Alert.alert('Error', `Failed to mark order as picked up: ${error.message}`);
    },
  });
}

// Hook for optimistic updates when order status changes locally
export function useOptimisticOrderUpdate() {
  const queryClient = useQueryClient();
  const user = useUserSession();

  const updateOrderStatus = (orderId: string, newStatus: Order['status']) => {
    if (!user) return;
    
    // Update initiated orders cache (new orders)
    queryClient.setQueryData(
      orderKeys.newOrders(user.restaurant_id, user.branch_ids[0].branch_id),
      (oldData: Order[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.filter(order => order.order_id !== orderId);
      }
    );

    // Update accepted orders cache
    queryClient.setQueryData(
      orderKeys.acceptedOrders(user.restaurant_id, user.branch_ids[0].branch_id),
      (oldData: Order[] | undefined) => {
        if (!oldData) return oldData;
        if (newStatus === 'accepted' || newStatus === 'preparing' || newStatus === 'ready' || newStatus === 'ready_for_pickup' || newStatus === 'out_for_delivery' || newStatus === 'picked_up') {
          // Add to accepted orders if it's an accepted status
          const existingOrder = oldData.find(order => order.order_id === orderId);
          if (!existingOrder) {
            // This is an optimistic update, we don't have the full order data
            // The real data will come from the server refetch
            return oldData;
          }
          return oldData.map(order => 
            order.order_id === orderId ? { ...order, status: newStatus } : order
          );
        } else {
          // Remove from accepted orders
          return oldData.filter(order => order.order_id !== orderId);
        }
      }
    );

    // Update past orders cache
    queryClient.setQueryData(
      orderKeys.pastOrders(user.restaurant_id, user.branch_ids[0].branch_id),
      (oldData: Order[] | undefined) => {
        if (!oldData) return oldData;
        if (newStatus === 'completed' || newStatus === 'cancelled' || newStatus === 'rejected') {
          // The real data will come from the server refetch
          return oldData;
        }
        return oldData;
      }
    );
  };

  return { updateOrderStatus };
}

/**
 * Hook to fetch delivery drivers
 */
export const useDeliveryDrivers = () => {
    const user = useUserSession();

    // Extract stable values for dependencies
    const restaurantId = user?.restaurant_id;
    const branchId = user?.branch_ids?.[0]?.branch_id;
    const userType = user?.user_type;
    const accessToken = user?.access_token;
    const isUserValid = !!(user && restaurantId && branchId && accessToken);

    // Create stable query key
    const queryKey = useMemo(() => {
        if (!isUserValid) return ['drivers', 'disabled'];
        return ['drivers', restaurantId, branchId];
    }, [isUserValid, restaurantId, branchId]);

    return useQuery({
        queryKey,
        queryFn: async () => {
            if (!isUserValid) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getDeliveryDrivers(restaurantId!, branchId!, userType!, accessToken!);
        },
        enabled: false, // Only fetch when explicitly called
        staleTime: 5 * 60 * 1000, // 5 minutes
        notifyOnChangeProps: ['data', 'error', 'isLoading'],
    });
};

/**
 * Hook to fetch branch delivery status (includes branch_delivery_access)
 */
export const useBranchDeliveryStatus = () => {
    const user = useUserSession();

    // Extract stable values for dependencies
    const restaurantId = user?.restaurant_id;
    const branchId = user?.branch_ids?.[0]?.branch_id;
    const userType = user?.user_type;
    const accessToken = user?.access_token;
    const isUserValid = !!(user && restaurantId && branchId && accessToken);

    // Create stable query key
    const queryKey = useMemo(() => {
        if (!isUserValid) return ['branchDeliveryStatus', 'disabled'];
        return ['branchDeliveryStatus', restaurantId, branchId];
    }, [isUserValid, restaurantId, branchId]);

    return useQuery({
        queryKey,
        queryFn: async () => {
            if (!isUserValid) {
                throw new Error('User not authenticated');
            }
            return ordersApi.getBranchDeliveryStatus(restaurantId!, branchId!, userType!, accessToken!);
        },
        enabled: isUserValid,
        staleTime: 10 * 60 * 1000, // 10 minutes
        notifyOnChangeProps: ['data', 'error', 'isLoading'],
    });
}; 