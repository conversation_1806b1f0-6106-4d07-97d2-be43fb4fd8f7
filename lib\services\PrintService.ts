import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Platform, Alert } from 'react-native';
import { Order, ordersApi } from '~/lib/api/orders';
import { generatePrintBillHTML } from '~/components/print/PrintBill';

interface BusinessInfo {
  name: string;
  address: string;
  phone: string;
  email?: string;
  vatNumber?: string;
}

interface PrintOptions {
  orientation?: 'portrait' | 'landscape';
  printerUrl?: string;
}

class PrintService {
  private static instance: PrintService;
  private businessInfo: BusinessInfo | null = null;

  private constructor() {}

  static getInstance(): PrintService {
    if (!PrintService.instance) {
      PrintService.instance = new PrintService();
    }
    return PrintService.instance;
  }

  /**
   * Set business information for printing
   */
  setBusinessInfo(info: BusinessInfo) {
    this.businessInfo = info;
  }

  /**
   * Get default business information
   */
  private getDefaultBusinessInfo(): BusinessInfo {
    return {
      name: 'Cravin Restaurant',
      address: 'UAE Business Address',
      phone: '+971 XX XXX XXXX',
      email: '<EMAIL>',
      vatNumber: 'TRN 123456789'
    };
  }

  /**
   * Extract business information from order details
   */
  private extractBusinessInfoFromOrderDetails(orderDetails: any): BusinessInfo {
    return {
      name: orderDetails.restaurant_name || 'Cravin Restaurant',
      address: orderDetails.branch_name || 'Main Branch',
      phone: orderDetails.branch_phone || '+971 XX XXX XXXX',
      email: '<EMAIL>',
      vatNumber: orderDetails.trn_number || 'TRN 123456789'
    };
  }

  /**
   * Check if printing is available on the current platform
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (Platform.OS !== 'android' && Platform.OS !== 'ios') {
        return false;
      }
      // expo-print is generally available on mobile platforms
      return true;
    } catch (error) {
      console.error('Error checking print availability:', error);
      return false;
    }
  }

  /**
   * Print order bill
   */
  async printOrderBill(
    order: Order, 
    accessToken: string,
    options: PrintOptions = {}
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if printing is available
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        return {
          success: false,
          error: 'Printing is not available on this device'
        };
      }

      // Fetch order details to get business information
      let businessInfo: BusinessInfo;
      try {
        const orderDetails = await ordersApi.getOrderDetails(order.order_id, accessToken);
        console.log('orderDetails', orderDetails);
        businessInfo = this.extractBusinessInfoFromOrderDetails(orderDetails);
      } catch (detailsError) {
        console.warn('Failed to fetch order details, using default business info:', detailsError);
        businessInfo = this.businessInfo || this.getDefaultBusinessInfo();
      }

      // Generate HTML content
      const htmlContent = await generatePrintBillHTML(order, businessInfo);

      // Print options
      const printOptions: Print.PrintOptions = {
        html: htmlContent,
        orientation: options.orientation || 'portrait',
        printerUrl: options.printerUrl,
        // For Android, we can set specific print settings
        ...(Platform.OS === 'android' && {
          // Android specific options
          margins: {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          },
        }),
      };

      // Show print dialog
      await Print.printAsync(printOptions);
      
      // Print.printAsync returns void on success
      console.log('Print job completed successfully');
      return { success: true };

    } catch (error) {
      console.error('Error printing order bill:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Print order bill with user-friendly error handling
   */
  async printOrderBillWithDialog(order: Order, accessToken: string): Promise<void> {
    try {
      // order.items=order.cart_items
      const result = await this.printOrderBill(order, accessToken);
      
      if (!result.success) {
        Alert.alert(
          'Print Error',
          result.error || 'Failed to print order bill',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Print Error',
        'An unexpected error occurred while printing',
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Select printer (for future implementation)
   */
  async selectPrinter(): Promise<Print.Printer | null> {
    try {
      if (Platform.OS === 'ios') {
        return await Print.selectPrinterAsync();
      }
      // Android doesn't support printer selection in expo-print
      return null;
    } catch (error) {
      console.error('Error selecting printer:', error);
      return null;
    }
  }

  /**
   * Print to PDF (for sharing or storage)
   */
  async printToPDF(order: Order, accessToken: string, options: { forSharing?: boolean } = {}): Promise<{ success: boolean; uri?: string; error?: string }> {
    try {
      // Fetch order details to get business information
      let businessInfo: BusinessInfo;
      try {
        const orderDetails = await ordersApi.getOrderDetails(order.order_id, accessToken);
        businessInfo = this.extractBusinessInfoFromOrderDetails(orderDetails);
      } catch (detailsError) {
        console.warn('Failed to fetch order details for PDF, using default business info:', detailsError);
        businessInfo = this.businessInfo || this.getDefaultBusinessInfo();
      }

      const htmlContent = await generatePrintBillHTML(order, businessInfo);

      // Configure PDF options based on purpose
      const pdfOptions: any = {
        html: htmlContent,
        base64: false,
      };

      // If generating for sharing, use PRC 1 paper size and remove margins
      if (options.forSharing) {
        // PRC 1 paper size: 102 × 165 mm (4.02 × 6.50 inches)
        // Convert to points (1 inch = 72 points)
        pdfOptions.width = 289; // 4.02 * 72 = 289 points
        pdfOptions.height = 468; // 6.50 * 72 = 468 points
        
        if (Platform.OS === 'ios') {
          pdfOptions.margins = {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          };
        }
      }

      const result = await Print.printToFileAsync(pdfOptions);

      return {
        success: true,
        uri: result.uri
      };
    } catch (error) {
      console.error('Error creating PDF:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create PDF'
      };
    }
  }

  /**
   * Share bill as PDF
   */
  async shareBillAsPDF(order: Order, accessToken: string, onProgress?: (message: string) => void): Promise<void> {
    try {
      onProgress?.('Checking availability...');
      
      // Check if sharing is available on this platform
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert(
          'Not Available',
          'Sharing is not available on this device',
          [{ text: 'OK' }]
        );
        return;
      }

      onProgress?.('Generating PDF...');

      // Generate PDF with sharing optimizations (no gaps/margins)
      const result = await this.printToPDF(order, accessToken, { forSharing: true });
      
      if (result.success && result.uri) {
        onProgress?.('Preparing file...');
        
        // Create a custom filename and copy the file
        const customFileName = `${order.order_name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
        const customFileUri = `${FileSystem.cacheDirectory}${customFileName}`;
        
        // Copy the generated PDF to the new location with custom name
        await FileSystem.copyAsync({
          from: result.uri,
          to: customFileUri
        });
        
        onProgress?.('Opening share dialog...');
        
        // Share the PDF file with custom filename
        await Sharing.shareAsync(customFileUri, {
          mimeType: 'application/pdf',
          dialogTitle: `Share Bill - Order #${order.order_name}`,
          UTI: 'com.adobe.pdf',
        });
      } else {
        Alert.alert(
          'Error',
          result.error || 'Failed to create PDF',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error sharing PDF:', error);
      Alert.alert(
        'Error',
        'Failed to share PDF',
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Share bill as PDF with user-friendly error handling
   */
  async shareBillAsPDFWithDialog(order: Order, accessToken: string, onProgress?: (message: string) => void): Promise<void> {
    try {
      await this.shareBillAsPDF(order, accessToken, onProgress);
    } catch (error) {
      Alert.alert(
        'Share Error',
        'An unexpected error occurred while sharing the bill',
        [{ text: 'OK' }]
      );
    }
  }
}

// Export singleton instance
export const printService = PrintService.getInstance();
export default PrintService; 