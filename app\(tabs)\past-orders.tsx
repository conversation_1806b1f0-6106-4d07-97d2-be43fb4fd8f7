import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  FlatList,
  Modal,
  TextInput,
  Alert,
  Clipboard,
  Share as RNShare,
} from 'react-native';
import { Text } from '~/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import {
  usePastOrders,
  useBranches,
} from '~/lib/hooks/useOrders';
import { useAuth } from '~/lib/hooks/useAuth';
import { Order } from '~/lib/api/orders';
import { useColorScheme } from '~/lib/useColorScheme';
import { OrdersHeader } from '~/components/food/OrdersHeader';
import { X, Filter, Search, Calendar, User, Truck, Printer, Share as ShareIcon, Copy, ExternalLink } from 'lucide-react-native';
import { printService } from '~/lib/services/PrintService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth >= 768;

// Food theme colors with dark mode support
const getFoodTheme = (isDark: boolean) => ({
  primary: '#ff2b2b',
  primaryBg: isDark ? 'bg-red-600 dark:bg-red-600' : 'bg-red-500',
  primaryDark: isDark ? 'bg-red-700 dark:bg-red-700' : 'bg-red-600',
  primaryLight: isDark ? 'bg-red-900/20 dark:bg-red-900/20' : 'bg-red-50',
  primaryBorder: isDark ? 'border-red-500 dark:border-red-500' : 'border-red-400',
  primaryText: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-600',
  primaryTextDark: isDark ? 'text-red-300 dark:text-red-300' : 'text-red-700',
  accent: '#ff2b2b',
});

// Create FilterModal as a separate component outside the main component for stability
interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  tempSearchText: string;
  onSearchTextChange: (text: string) => void;
  tempOrderTypeFilter: 'all' | 'delivery' | 'pickup';
  onOrderTypeChange: (type: 'all' | 'delivery' | 'pickup') => void;
  tempStatusFilter: 'all' | 'completed' | 'cancelled';
  onStatusChange: (status: 'all' | 'completed' | 'cancelled') => void;
  tempDriverFilter: 'all' | 'with_driver' | 'without_driver';
  onDriverChange: (driver: 'all' | 'with_driver' | 'without_driver') => void;
  onClearAllFilters: () => void;
  onApplyFilters: () => void;
  isDarkColorScheme: boolean;
  screenHeight: number;
  foodTheme: any;
}

const FilterModal = React.memo<FilterModalProps>(({
  visible,
  onClose,
  tempSearchText,
  onSearchTextChange,
  tempOrderTypeFilter,
  onOrderTypeChange,
  tempStatusFilter,
  onStatusChange,
  tempDriverFilter,
  onDriverChange,
  onClearAllFilters,
  onApplyFilters,
  isDarkColorScheme,
  screenHeight,
  foodTheme
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-card rounded-t-3xl" style={{ height: screenHeight * 0.65, maxHeight: screenHeight * 0.85 }}>
          <View className="p-4 border-b border-border flex-row items-center justify-between">
            <Text className="text-xl font-bold text-foreground">Filters</Text>
            <TouchableOpacity onPress={onClose} className="p-2 rounded-full bg-muted">
              <X size={20} className="text-muted-foreground" />
            </TouchableOpacity>
          </View>

          <ScrollView
            className="flex-1 p-4"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
            keyboardShouldPersistTaps="handled"
          >
            {/* Search */}
            <View className="mb-6">
              <Text className="text-base font-semibold text-foreground mb-3">Search</Text>
              <View className="flex-row items-center bg-muted rounded-lg px-4 py-3">
                <Search size={18} className="text-muted-foreground mr-3" />
                <TextInput
                  value={tempSearchText}
                  onChangeText={onSearchTextChange}
                  placeholder="Search by order #, customer name, phone..."
                  className="flex-1 text-foreground text-sm"
                  placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
                  autoCorrect={false}
                  autoCapitalize="none"
                />
              </View>
            </View>

            {/* Order Type Filter */}
            <View className="mb-6">
              <Text className="text-base font-semibold text-foreground mb-3">Order Type</Text>
              <View className="flex-row flex-wrap gap-2">
                {['all', 'delivery', 'pickup'].map((type) => (
                  <TouchableOpacity
                    key={type}
                    onPress={() => onOrderTypeChange(type as any)}
                    activeOpacity={0.7}
                    className={`px-3 py-2 rounded-full border ${tempOrderTypeFilter === type
                      ? 'bg-red-100 border-red-500 dark:bg-red-900/30 dark:border-red-400'
                      : 'bg-muted border-border'
                      }`}
                  >
                    <Text className={`text-sm font-medium capitalize ${tempOrderTypeFilter === type
                      ? 'text-red-700 dark:text-red-300'
                      : 'text-foreground'
                      }`}>
                      {type === 'all' ? 'All Types' : type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Status Filter */}
            <View className="mb-6">
              <Text className="text-base font-semibold text-foreground mb-3">Status</Text>
              <View className="flex-row flex-wrap gap-2">
                {['all', 'completed', 'cancelled'].map((status) => (
                  <TouchableOpacity
                    key={status}
                    onPress={() => onStatusChange(status as any)}
                    activeOpacity={0.7}
                    className={`px-3 py-2 rounded-full border ${tempStatusFilter === status
                      ? 'bg-red-100 border-red-500 dark:bg-red-900/30 dark:border-red-400'
                      : 'bg-muted border-border'
                      }`}
                  >
                    <Text className={`text-sm font-medium capitalize ${tempStatusFilter === status
                      ? 'text-red-700 dark:text-red-300'
                      : 'text-foreground'
                      }`}>
                      {status === 'all' ? 'All Status' : status}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Driver Filter */}
            <View className="mb-6">
              <Text className="text-base font-semibold text-foreground mb-3">Delivery Driver</Text>
              <View className="flex-row flex-wrap gap-2">
                {['all', 'with_driver', 'without_driver'].map((driver) => (
                  <TouchableOpacity
                    key={driver}
                    onPress={() => onDriverChange(driver as any)}
                    activeOpacity={0.7}
                    className={`px-3 py-2 rounded-full border ${tempDriverFilter === driver
                      ? 'bg-red-100 border-red-500 dark:bg-red-900/30 dark:border-red-400'
                      : 'bg-muted border-border'
                      }`}
                  >
                    <Text className={`text-sm font-medium ${tempDriverFilter === driver
                      ? 'text-red-700 dark:text-red-300'
                      : 'text-foreground'
                      }`}>
                      {driver === 'all' ? 'All Orders' :
                        driver === 'with_driver' ? 'With Driver' : 'Without Driver'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Clear Filters Button */}
            <View className="mt-2">
              <Button
                onPress={onClearAllFilters}
                variant="outline"
                className="border-muted-foreground"
              >
                <Text className="text-muted-foreground font-semibold">Clear All Filters</Text>
              </Button>
            </View>
          </ScrollView>

          {/* Action Buttons */}
          <View className="p-4 border-t border-border bg-card">
            <View className="flex-row gap-3">
              <Button
                onPress={onClose}
                variant="outline"
                className="flex-1"
              >
                <Text className="text-foreground font-semibold">Cancel</Text>
              </Button>
              <Button
                onPress={onApplyFilters}
                className={`flex-1 ${foodTheme.primaryDark}`}
              >
                <Text className="text-white font-semibold">Apply Filters</Text>
              </Button>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
});

export default function PastOrdersScreen() {
  return <PastOrdersScreenContent />;
}

function PastOrdersScreenContent() {
  // Authentication
  const { session } = useAuth();
  const user = session?.user;

  // State
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedBranchId, setSelectedBranchId] = useState<string | null>(
    user?.user_type === 'owner' ? null : user?.branch_ids[0]?.branch_id || null
  );

  // Filter states
  const [showFilters, setShowFilters] = useState(false);

  // Applied filters (actual filtering)
  const [appliedSearchText, setAppliedSearchText] = useState('');
  const [appliedOrderTypeFilter, setAppliedOrderTypeFilter] = useState<'all' | 'delivery' | 'pickup'>('all');
  const [appliedStatusFilter, setAppliedStatusFilter] = useState<'all' | 'completed' | 'cancelled'>('all');
  const [appliedDriverFilter, setAppliedDriverFilter] = useState<'all' | 'with_driver' | 'without_driver'>('all');

  // Temporary filter states (for UI before applying)
  const [tempSearchText, setTempSearchText] = useState('');
  const [tempOrderTypeFilter, setTempOrderTypeFilter] = useState<'all' | 'delivery' | 'pickup'>('all');
  const [tempStatusFilter, setTempStatusFilter] = useState<'all' | 'completed' | 'cancelled'>('all');
  const [tempDriverFilter, setTempDriverFilter] = useState<'all' | 'with_driver' | 'without_driver'>('all');

  // Print and Share states
  const [sharingProgress, setSharingProgress] = useState<{ [orderId: string]: string }>({});
  const [sharingOrderId, setSharingOrderId] = useState<string | null>(null);

  const { isDarkColorScheme } = useColorScheme();
  const foodTheme = getFoodTheme(isDarkColorScheme);

  // Memoize user type check
  const isOwner = useMemo(() => user?.user_type?.toLowerCase() === 'owner', [user]);

  // Data-fetching hooks
  const {
    data: pastOrders = [],
    isLoading: isLoadingPast,
    error: pastOrdersError,
    refetch: refetchPastOrders,
  } = usePastOrders(selectedBranchId);
  const { data: branches = [] } = useBranches();

  // Filtered orders based on applied filters
  const filteredOrders = useMemo(() => {
    if (!pastOrders) return [];

    return pastOrders.filter(order => {
      // Search filter
      if (appliedSearchText) {
        const searchLower = appliedSearchText.toLowerCase();
        const matchesSearch =
          (order.order_name && order.order_name.toLowerCase().includes(searchLower)) ||
          (order.customer_name && order.customer_name.toLowerCase().includes(searchLower)) ||
          ((order as any).receiver_name && (order as any).receiver_name.toLowerCase().includes(searchLower)) ||
          (order.customer_number && order.customer_number.includes(appliedSearchText)) ||
          ((order as any).receiver_number && (order as any).receiver_number.includes(appliedSearchText));

        if (!matchesSearch) return false;
      }

      // Order type filter
      if (appliedOrderTypeFilter !== 'all' && order.order_type !== appliedOrderTypeFilter) {
        return false;
      }

      // Status filter
      if (appliedStatusFilter !== 'all' && order.status !== appliedStatusFilter) {
        return false;
      }

      // Driver filter
      if (appliedDriverFilter !== 'all') {
        const hasDriver = !!(order as any).delivery_driver;
        if (appliedDriverFilter === 'with_driver' && !hasDriver) return false;
        if (appliedDriverFilter === 'without_driver' && hasDriver) return false;
      }

      return true;
    });
  }, [pastOrders, appliedSearchText, appliedOrderTypeFilter, appliedStatusFilter, appliedDriverFilter]);

  // Memoized functions for better performance
  const formatTimeAgo = useCallback((dateString: string) => {
    const now = new Date();
    const orderTime = new Date(dateString);
    const diffMs = now.getTime() - orderTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  }, []);

  const formatOrderTime = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }, []);

  const getStatusColor = useCallback((status: string) => {
    return {
      bg: isDarkColorScheme ? 'bg-gray-800 dark:bg-gray-800' : 'bg-gray-100',
      text: isDarkColorScheme ? 'text-gray-400 dark:text-gray-400' : 'text-gray-800',
      dot: isDarkColorScheme ? 'bg-gray-400 dark:bg-gray-400' : 'bg-gray-500'
    };
  }, [isDarkColorScheme]);

  const onRefresh = useCallback(() => {
    refetchPastOrders();
  }, [refetchPastOrders]);

  const handleOrderPress = useCallback((order: Order) => {
    console.log('order',order)
    setSelectedOrder(order);
    setShowDetails(true);
  }, []);

  const handleBackPress = useCallback(() => {
    setShowDetails(false);
    setSelectedOrder(null);
  }, []);

  const handleBranchSelect = useCallback((branchId: string | null) => {
    setSelectedBranchId(branchId);
    setSelectedOrder(null);
  }, []);

  // Memoize all filter callback functions to prevent re-creation
  const handleSearchTextChange = useCallback((text: string) => {
    setTempSearchText(text);
  }, []);

  const handleOrderTypeChange = useCallback((type: 'all' | 'delivery' | 'pickup') => {
    setTempOrderTypeFilter(type);
  }, []);

  const handleStatusChange = useCallback((status: 'all' | 'completed' | 'cancelled') => {
    setTempStatusFilter(status);
  }, []);

  const handleDriverChange = useCallback((driver: 'all' | 'with_driver' | 'without_driver') => {
    setTempDriverFilter(driver);
  }, []);

  const handleCloseModal = useCallback(() => {
    setShowFilters(false);
  }, []);

  // Filter handling functions
  const openFilters = useCallback(() => {
    // Set temp values to current applied values when opening
    setTempSearchText(appliedSearchText);
    setTempOrderTypeFilter(appliedOrderTypeFilter);
    setTempStatusFilter(appliedStatusFilter);
    setTempDriverFilter(appliedDriverFilter);
    setShowFilters(true);
  }, [appliedSearchText, appliedOrderTypeFilter, appliedStatusFilter, appliedDriverFilter]);

  const applyFilters = useCallback(() => {
    // Apply the temp values to the actual filter states
    setAppliedSearchText(tempSearchText);
    setAppliedOrderTypeFilter(tempOrderTypeFilter);
    setAppliedStatusFilter(tempStatusFilter);
    setAppliedDriverFilter(tempDriverFilter);
    setShowFilters(false);
  }, [tempSearchText, tempOrderTypeFilter, tempStatusFilter, tempDriverFilter]);

  const clearAllFilters = useCallback(() => {
    setTempSearchText('');
    setTempOrderTypeFilter('all');
    setTempStatusFilter('all');
    setTempDriverFilter('all');
  }, []);

  const resetFilters = useCallback(() => {
    setAppliedSearchText('');
    setAppliedOrderTypeFilter('all');
    setAppliedStatusFilter('all');
    setAppliedDriverFilter('all');
    setTempSearchText('');
    setTempOrderTypeFilter('all');
    setTempStatusFilter('all');
    setTempDriverFilter('all');
  }, []);

  // Print and Share handlers
  const handlePrintBill = useCallback(async (order: Order) => {
    if (!user?.access_token) {
      Alert.alert('Error', 'Unable to print: Authentication required');
      return;
    }
    await printService.printOrderBillWithDialog(order, user.access_token);
  }, [user?.access_token]);

  const handleShareBill = useCallback(async (order: Order) => {
    if (!user?.access_token) {
      Alert.alert('Error', 'Unable to share: Authentication required');
      return;
    }

    // If sharing from modal, dismiss it to prevent interference
    const wasModalOpen = showDetails && selectedOrder?.order_id === order.order_id;
    if (wasModalOpen) {
      setShowDetails(false);
      setSelectedOrder(null);
    }

    // Mark this order as starting to share
    setSharingOrderId(order.order_id);
    setSharingProgress(prev => ({
      ...prev,
      [order.order_id]: 'Preparing to share...'
    }));
    
    const onProgress = (message: string) => {
      // Simple progress updates without interference concerns since modal is dismissed
      setSharingProgress(prev => {
        if (prev[order.order_id] !== message) {
          return {
            ...prev,
            [order.order_id]: message
          };
        }
        return prev;
      });
    };

    try {
      await printService.shareBillAsPDFWithDialog(order, user.access_token, onProgress);
    } catch (error) {
      console.error('Error sharing bill:', error);
      Alert.alert('Error', 'Failed to share bill. Please try again.');
    } finally {
      // Clear progress after sharing is complete
      setTimeout(() => {
        setSharingOrderId(null);
        setSharingProgress(prev => {
          const newProgress = { ...prev };
          delete newProgress[order.order_id];
          return newProgress;
        });
      }, 300);
    }
  }, [user?.access_token, showDetails, selectedOrder?.order_id]);

  // Copy and Share helper functions for customer information
  const formatCustomerInfo = useCallback((order: Order) => {
    const customerName = (order as any).receiver_name || order.customer_name;
    const customerPhone = (order as any).receiver_number || order.customer_number;
    const address = order.customer_address;
    
    let formattedText = `Order Name: ${order.order_name}\nName: ${customerName}\nContact: ${customerPhone}`;
    
    if (address) {
      formattedText += `\nAddress: ${address}`;
      
      // Add directions link if coordinates are available
      if ((order as any).latitude && (order as any).longitude) {
        const lat = (order as any).latitude;
        const lng = (order as any).longitude;
        formattedText += `\n\nDirections: https://maps.google.com/maps?q=${lat},${lng}&hl=es;z=1&amp`;
      }
    }
    
    return formattedText;
  }, []);

  const handleCopyCustomerInfo = useCallback((order: Order) => {
    const formattedText = formatCustomerInfo(order);
    Clipboard.setString(formattedText);
    Alert.alert('Copied', 'Customer information copied to clipboard');
  }, [formatCustomerInfo]);

  const handleShareCustomerInfo = useCallback(async (order: Order) => {
    try {
      const formattedText = formatCustomerInfo(order);
      await RNShare.share({
        message: formattedText,
        title: 'Share Customer Information',
      });
    } catch (error) {
      console.error('Error sharing customer info:', error);
      Alert.alert('Error', 'Failed to share customer information');
    }
  }, [formatCustomerInfo]);

  // Render Functions
  const renderLoadingSpinner = () => (
    <View className="flex-1 items-center justify-center py-16">
      <ActivityIndicator size="large" color={foodTheme.primary} />
      <Text className="mt-4 text-base text-muted-foreground font-medium">Loading past orders...</Text>
    </View>
  );

  const renderError = (error: Error | null) => {
    if (!error) return null;
    return (
      <View className="flex-1 items-center justify-center py-16 px-6">
        <View className={`w-16 h-16 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-4`}>
          <Text className="text-2xl">❌</Text>
        </View>
        <Text className={`${foodTheme.primaryText} text-center mb-6 text-base font-medium`}>
          Failed to load past orders
        </Text>
        <Text className="text-muted-foreground text-center mb-6 text-sm">{error.message}</Text>
        <Button onPress={onRefresh} className={foodTheme.primaryDark}>
          <Text className="text-white font-semibold">Try Again</Text>
        </Button>
      </View>
    );
  };

  // Memoized render function for order cards
  const renderOrderCard = useCallback(({ item: order }: { item: Order }) => {
    const statusColors = getStatusColor(order.status);
    const timeAgo = formatTimeAgo(order.ordered_on);
    const orderTime = formatOrderTime(order.ordered_on);
    const totalBill = ((order as any).bill?.total_bill || order.bill_amount?.total_bill || 0).toFixed(2);

    return (
      <TouchableOpacity
        className="mb-4"
        onPress={() => handleOrderPress(order)}
        activeOpacity={0.7}
      >
        <Card className="shadow-lg border-0 bg-card">
          <CardContent className="p-4">
            {/* Header */}
            <View className="flex-row items-start justify-between mb-3">
              <View className="flex-row items-start flex-1 mr-3">
                <View className={`w-12 h-12 ${order.order_type === 'delivery'
                  ? (isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100')
                  : (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                  } rounded-xl mr-3 items-center justify-center`}>
                  <Text className="text-xl">
                    {order.order_type === 'delivery' ? '🚚' : '🍽️'}
                  </Text>
                </View>
                <View className="flex-1">
                  <Text className="font-bold text-lg text-foreground" numberOfLines={2}>
                    {(order as any).receiver_name || order.customer_name}
                  </Text>
                  <View className="flex-row items-center mt-1 mb-1">
                    <View className={`w-2 h-2 ${statusColors.dot} rounded-full mr-2`} />
                    <Text className={`text-sm font-medium ${statusColors.text} uppercase tracking-wide`}>
                      {order.status === 'completed' ? 'Completed' : order.status}
                    </Text>
                  </View>
                  {(order as any).receiver_name && order.customer_name !== (order as any).receiver_name && (
                    <Text className="text-xs text-muted-foreground" numberOfLines={1}>
                      Ordered by: {order.customer_name}
                    </Text>
                  )}
                </View>
              </View>
              <View className="items-end">
                <Text className="font-bold text-xl text-foreground">
                  AED {totalBill}
                </Text>
                {order.order_type === 'delivery' && ((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge) > 0 && (
                  <Text className="text-xs text-muted-foreground">
                    +AED {((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge || 0).toFixed(2)} delivery
                  </Text>
                )}
                <Text className="text-sm text-muted-foreground mt-1">
                  {orderTime}
                </Text>
              </View>
            </View>

            {/* Order Info */}
            <View className="flex-row items-center justify-between mb-3">
              <View className="flex-row items-center space-x-3">
                <View className={`px-3 py-1.5 ${order.order_type === 'delivery'
                  ? (isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100')
                  : (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                  } rounded-full`}>
                  <Text className={`text-xs font-bold uppercase tracking-wide ${order.order_type === 'delivery'
                    ? (isDarkColorScheme ? 'text-red-300' : 'text-red-700')
                    : (isDarkColorScheme ? 'text-red-400' : 'text-red-600')
                    }`}>
                    {order.order_type}
                  </Text>
                </View>

                <View className="flex-row items-center mr-3 ml-3">
                  <View className={`w-2 h-2 ${isDarkColorScheme ? 'bg-gray-500' : 'bg-gray-400'} rounded-full mr-2`} />
                  <Text className="text-sm text-muted-foreground">
                    {(order.items?.length || order.cart_items?.length || 0)} item{((order.items?.length || order.cart_items?.length || 0)) !== 1 ? 's' : ''}
                  </Text>
                </View>

                <View className="flex-row items-center">
                  <View className={`w-2 h-2 ${isDarkColorScheme ? 'bg-green-500' : 'bg-green-400'} rounded-full mr-2`} />
                  <Text className="text-xs text-muted-foreground uppercase tracking-wide">
                    {timeAgo}
                  </Text>
                </View>

                {/* Delivery Driver Indicator */}
                {order.order_type === 'delivery' && (order as any).delivery_driver && (
                  <View className="flex-row items-center ml-3">
                    <View className={`w-2 h-2 ${isDarkColorScheme ? 'bg-blue-500' : 'bg-blue-400'} rounded-full mr-2`} />
                    <Text className="text-xs text-muted-foreground uppercase tracking-wide">
                      Driver: {(order as any).delivery_driver.name}
                    </Text>
                  </View>
                )}
              </View>

              <Text className={`text-sm font-bold ${foodTheme.primaryText}`}>
                #{order.order_name}
              </Text>
            </View>

            {/* Print and Share Bill Buttons */}
            <View onStartShouldSetResponder={() => true}>
              <View className="flex-row mb-3 gap-3">
                <TouchableOpacity
                  className={`flex-1 flex-row items-center justify-center p-3 ${isDarkColorScheme ? 'bg-gray-800 dark:bg-gray-800' : 'bg-gray-100'} rounded-lg border ${isDarkColorScheme ? 'border-gray-700' : 'border-gray-300'}`}
                  onPress={() => handlePrintBill(order)}
                  activeOpacity={0.7}
                >
                  <Printer size={18} className={`${isDarkColorScheme ? 'text-gray-400' : 'text-gray-600'} mr-2`} />
                  <Text className={`${isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'} font-medium pl-2`}>
                    Print Bill
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  className={`flex-1 flex-row items-center justify-center p-3 ${isDarkColorScheme ? 'bg-blue-800/30 dark:bg-blue-800/30' : 'bg-blue-100'} rounded-lg border ${isDarkColorScheme ? 'border-blue-700' : 'border-blue-300'}`}
                  onPress={() => handleShareBill(order)}
                  activeOpacity={0.7}
                  disabled={sharingOrderId === order.order_id}
                >
                  {sharingOrderId === order.order_id ? (
                    <ActivityIndicator size={18} color={isDarkColorScheme ? '#60a5fa' : '#2563eb'} className="mr-2" />
                  ) : (
                    <ShareIcon size={18} className={`${isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'} mr-2`} />
                  )}
                  <Text className={`${isDarkColorScheme ? 'text-blue-300' : 'text-blue-700'} font-medium pl-2`}>
                    {sharingOrderId === order.order_id ? (sharingProgress[order.order_id] || 'Sharing...') : 'Share Bill'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </CardContent>
        </Card>
      </TouchableOpacity>
    );
  }, [isDarkColorScheme, foodTheme, handleOrderPress, handlePrintBill, handleShareBill, sharingOrderId, sharingProgress]);

  const renderOrderDetails = (inModal = false) => {
    if (!selectedOrder) {
      return (
        <View className="flex-1 items-center justify-center p-8">
          <View className={`w-20 h-20 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
            <Text className="text-3xl">🍽️</Text>
          </View>
          <Text className="text-foreground text-center text-lg font-medium mb-2">
            No Order Selected
          </Text>
          <Text className="text-muted-foreground text-center">
            Select an order from the list to view its details
          </Text>
        </View>
      );
    }

    const statusColors = getStatusColor(selectedOrder.status);
    const timeAgo = formatTimeAgo(selectedOrder.ordered_on);
    const orderTime = formatOrderTime(selectedOrder.ordered_on);
    const totalBill = ((selectedOrder as any).bill?.total_bill || selectedOrder.bill_amount?.total_bill || 0).toFixed(2);
    const deliveryFee = ((selectedOrder as any).bill?.delivery_charge || selectedOrder.bill_amount?.delivery_charge || 0).toFixed(2);
    const subTotal = ((selectedOrder as any).bill?.sub_total || selectedOrder.bill_amount?.sub_total || 0).toFixed(2);

    return (
      <View style={{ maxHeight: inModal ? screenHeight * 0.85 : undefined }}>
        {inModal && (
          <View className="p-4 border-b border-border flex-row items-center justify-between bg-card">
            <Text className="text-xl font-bold text-foreground">Order Details</Text>
            <TouchableOpacity onPress={handleBackPress} className="p-2 rounded-full bg-muted">
              <X size={20} className="text-muted-foreground" />
            </TouchableOpacity>
          </View>
        )}
        <ScrollView showsVerticalScrollIndicator={false}>
          <View className="p-4">
            {/* Order Header */}
            <Card className="mb-6 bg-card border border-border shadow-lg overflow-hidden">
              {/* Header Section */}
              <View className={`px-6 py-4 ${isDarkColorScheme ? 'bg-gray-900/50' : 'bg-gray-50'} border-b border-border`}>
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <View className={`w-10 h-10 ${isDarkColorScheme ? 'bg-gray-800' : 'bg-white'} rounded-lg items-center justify-center mr-3 shadow-sm`}>
                      <Text className="text-lg">{selectedOrder.order_type === 'delivery' ? '🚚' : '🏪'}</Text>
                    </View>
                    <View>
                      <Text className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                        Order Details
                      </Text>
                      <Text className="text-xl font-bold text-foreground">
                        #{selectedOrder.order_name}
                      </Text>
                    </View>
                  </View>
                  <View className={`px-3 py-1.5 ${statusColors.bg} rounded-lg`}>
                    <View className="flex-row items-center">
                      <View className={`w-2 h-2 ${statusColors.dot} rounded-full mr-2`} />
                      <Text className={`text-xs font-semibold ${statusColors.text} uppercase tracking-wide`}>
                        {selectedOrder.status === 'completed' ? 'Completed' : selectedOrder.status}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>

              <CardContent className="p-6">
                {/* Order Information */}
                <View className="mb-6">
                  <View className="flex-row items-center justify-between mb-4">
                    <View className="flex-row items-center space-x-4">
                      <View className={`px-3 py-2 ${selectedOrder.order_type === 'delivery'
                        ? (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                        : (isDarkColorScheme ? 'bg-blue-900/20' : 'bg-blue-50')
                        } rounded-lg border ${selectedOrder.order_type === 'delivery'
                          ? (isDarkColorScheme ? 'border-red-800/50' : 'border-red-200')
                          : (isDarkColorScheme ? 'border-blue-800/50' : 'border-blue-200')
                        }`}>
                        <Text className={`text-sm font-semibold ${selectedOrder.order_type === 'delivery'
                          ? (isDarkColorScheme ? 'text-red-400' : 'text-red-700')
                          : (isDarkColorScheme ? 'text-blue-400' : 'text-blue-700')
                          }`}>
                          {selectedOrder.order_type === 'delivery' ? 'Delivery Order' : 'Pickup Order'}
                        </Text>
                      </View>
                      <View className={`px-3 py-2 ml-2 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-lg`}>
                        <Text className="text-sm font-medium text-muted-foreground">
                          {(selectedOrder.items?.length || selectedOrder.cart_items?.length || 0)} item{((selectedOrder.items?.length || selectedOrder.cart_items?.length || 0)) !== 1 ? 's' : ''}
                        </Text>
                      </View>
                    </View>
                    <View className="items-end">
                      <Text className="text-sm text-muted-foreground">Order Time</Text>
                      <Text className="text-base font-semibold text-foreground">
                        {orderTime}
                      </Text>
                      <Text className="text-xs text-muted-foreground">
                        {timeAgo}
                      </Text>
                    </View>
                  </View>

                  {/* Total Amount Display */}
                  <View className={`p-4 ${isDarkColorScheme ? 'bg-green-900/20' : 'bg-green-50'} rounded-lg border ${isDarkColorScheme ? 'border-green-800/50' : 'border-green-200'}`}>
                    <View className="flex-row items-center justify-between">
                      <View>
                        <Text className="text-sm text-muted-foreground">Total Amount</Text>
                        <Text className="text-2xl font-bold text-green-600 dark:text-green-400">
                          AED {totalBill}
                        </Text>
                        {selectedOrder.order_type === 'delivery' && ((selectedOrder as any).bill?.delivery_charge || selectedOrder.bill_amount?.delivery_charge) > 0 && (
                          <Text className="text-xs text-muted-foreground">
                            Includes AED {((selectedOrder as any).bill?.delivery_charge || selectedOrder.bill_amount?.delivery_charge || 0).toFixed(2)} delivery fee
                          </Text>
                        )}
                      </View>
                      <View className={`w-16 h-16 ${isDarkColorScheme ? 'bg-green-800/30' : 'bg-green-100'} rounded-full items-center justify-center`}>
                        <Text className="text-2xl">💰</Text>
                      </View>
                    </View>
                  </View>

                  {/* Print and Share Bill Buttons */}
                  <View className="mt-6">
                    <View className="flex-row mb-4 gap-3">
                      <TouchableOpacity
                        className={`flex-1 flex-row items-center justify-center p-4 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-lg border ${isDarkColorScheme ? 'border-gray-700' : 'border-gray-300'} shadow-sm`}
                        onPress={() => handlePrintBill(selectedOrder)}
                        activeOpacity={0.7}
                      >
                        <Printer size={20} className={`${isDarkColorScheme ? 'text-gray-400' : 'text-gray-600'} mr-2`} />
                        <Text className={`${isDarkColorScheme ? 'text-gray-300' : 'text-gray-700'} font-semibold text-base pl-2`}>
                          Print Bill
                        </Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity
                        className={`flex-1 flex-row items-center justify-center p-4 ${isDarkColorScheme ? 'bg-blue-800/30' : 'bg-blue-100'} rounded-lg border ${isDarkColorScheme ? 'border-blue-700' : 'border-blue-300'} shadow-sm`}
                        onPress={() => handleShareBill(selectedOrder)}
                        activeOpacity={0.7}
                        disabled={selectedOrder && sharingOrderId === selectedOrder.order_id}
                      >
                        {selectedOrder && sharingOrderId === selectedOrder.order_id ? (
                          <ActivityIndicator size={20} color={isDarkColorScheme ? '#60a5fa' : '#2563eb'} className="mr-2" />
                        ) : (
                          <ShareIcon size={20} className={`${isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'} mr-2`} />
                        )}
                        <Text className={`${isDarkColorScheme ? 'text-blue-300' : 'text-blue-700'} font-semibold text-base pl-2`}>
                          {selectedOrder && sharingOrderId === selectedOrder.order_id ? (sharingProgress[selectedOrder.order_id] || 'Sharing...') : 'Share Bill'}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card className="mb-6 bg-card border border-border shadow-lg">
              <CardContent className="p-6">
                <View className="flex-row items-center justify-between mb-4">
                  <Text className="text-lg font-bold text-foreground">Customer Information</Text>
                  <View className="flex-row">
                    <TouchableOpacity
                      onPress={() => handleCopyCustomerInfo(selectedOrder)}
                      className={`p-2 rounded-lg mr-2 ${isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-100'} border ${isDarkColorScheme ? 'border-gray-700' : 'border-gray-300'}`}
                      activeOpacity={0.7}
                    >
                      <Copy size={18} className={`${isDarkColorScheme ? 'text-gray-400' : 'text-gray-600'}`} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleShareCustomerInfo(selectedOrder)}
                      className={`p-2 rounded-lg ${isDarkColorScheme ? 'bg-blue-800/30' : 'bg-blue-100'} border ${isDarkColorScheme ? 'border-blue-700' : 'border-blue-300'}`}
                      activeOpacity={0.7}
                    >
                      <ShareIcon size={18} className={`${isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'}`} />
                    </TouchableOpacity>
                  </View>
                </View>

                <View className="space-y-4">
                  {/* Order Name */}
                  <View>
                    <Text className="text-sm text-muted-foreground mb-1">Order Number</Text>
                    <Text className="text-base font-semibold text-foreground">
                      #{selectedOrder.order_name}
                    </Text>
                  </View>

                  {/* Customer Name */}
                  <View>
                    <Text className="text-sm text-muted-foreground mb-1">
                      {selectedOrder.order_type === 'delivery' ? 'Receiver Name' : 'Customer Name'}
                    </Text>
                    <Text className="text-base font-semibold text-foreground">
                      {(selectedOrder as any).receiver_name || selectedOrder.customer_name}
                    </Text>
                    {(selectedOrder as any).receiver_name && selectedOrder.customer_name !== (selectedOrder as any).receiver_name && (
                      <Text className="text-sm text-muted-foreground mt-1">
                        Ordered by: {selectedOrder.customer_name}
                      </Text>
                    )}
                  </View>

                  {/* Phone Number */}
                  <View>
                    <Text className="text-sm text-muted-foreground mb-1">
                      {selectedOrder.order_type === 'delivery' ? 'Receiver Phone' : 'Phone Number'}
                    </Text>
                    <Text className="text-base font-semibold text-foreground">
                      {(selectedOrder as any).receiver_number || selectedOrder.customer_number}
                    </Text>
                  </View>

                  {/* Address */}
                  {selectedOrder.customer_address && (
                    <View>
                      <Text className="text-sm text-muted-foreground mb-1">Delivery Address</Text>
                      <Text className="text-base font-semibold text-foreground leading-relaxed">
                        {selectedOrder.customer_address}
                      </Text>
                    </View>
                  )}

                  {/* Order Timing */}
                  <View className={`p-4 mt-4 ${isDarkColorScheme ? 'bg-gray-800/30' : 'bg-gray-50'} rounded-lg border ${isDarkColorScheme ? 'border-gray-700' : 'border-gray-200'}`}>
                    <Text className="text-sm text-muted-foreground mb-2">Order Timing</Text>
                    <View className="flex-row justify-between">
                      <View>
                        <Text className="text-xs text-muted-foreground">Ordered at</Text>
                        <Text className="text-sm font-semibold text-foreground">{orderTime}</Text>
                      </View>
                      <View className="items-end">
                        <Text className="text-xs text-muted-foreground">Time ago</Text>
                        <Text className="text-sm font-semibold text-foreground">{timeAgo}</Text>
                      </View>
                    </View>
                  </View>
                </View>
              </CardContent>
            </Card>

            {/* Delivery Driver Information */}
            {selectedOrder.order_type === 'delivery' && (selectedOrder as any).delivery_driver && (
              <Card className="mb-6 bg-card border border-border shadow-lg">
                <CardContent className="p-6">
                  <Text className="text-lg font-bold text-foreground mb-4">Delivery Driver</Text>

                  <View className="space-y-4">
                    {/* Driver Name */}
                    <View>
                      <Text className="text-sm text-muted-foreground mb-1">Driver Name</Text>
                      <Text className="text-base font-semibold text-foreground">
                        {(selectedOrder as any).delivery_driver.name}
                      </Text>
                    </View>

                    {/* Driver Phone */}
                    <View>
                      <Text className="text-sm text-muted-foreground mb-1">Driver Phone</Text>
                      <Text className="text-base font-semibold text-foreground">
                        {(selectedOrder as any).delivery_driver.number}
                      </Text>
                    </View>

                    {/* Driver ID */}
                    {/* <View>
                      <Text className="text-sm text-muted-foreground mb-1">Driver ID</Text>
                      <Text className="text-sm font-mono text-muted-foreground">
                        {(selectedOrder as any).delivery_driver.id}
                      </Text>
                    </View> */}
                  </View>
                </CardContent>
              </Card>
            )}



            {/* Order Items */}
            <Card className="bg-card border border-border shadow-lg">
              <CardContent className="p-6">
                <View className="flex-row items-center justify-between mb-6">
                  <Text className="text-lg font-bold text-foreground">Order Items</Text>
                  <View className={`px-3 py-1.5 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-100'} rounded-md`}>
                    <Text className="text-sm font-medium text-muted-foreground">
                      {(selectedOrder?.items?.length || selectedOrder?.cart_items?.length || 0)} items
                    </Text>
                  </View>
                </View>

                <View className="space-y-4">
                  {(selectedOrder.items || selectedOrder.cart_items)?.map((item: any, index: number) => (
                    <View key={index} className={`p-4 mb-2 ${isDarkColorScheme ? 'bg-gray-800/30' : 'bg-gray-50'} rounded-lg border ${isDarkColorScheme ? 'border-gray-700' : 'border-gray-200'}`}>
                      <View className="flex-row items-start justify-between mb-3">
                        <View className="flex-1 mr-4">
                          <Text className="text-base font-semibold text-foreground mb-2">
                            {item.name}
                          </Text>
                          <View className="flex-row items-center space-x-4">
                            <View className={`px-2 py-1 ${isDarkColorScheme ? 'bg-blue-900/30' : 'bg-blue-100'} rounded`}>
                              <Text className={`text-xs font-medium ${isDarkColorScheme ? 'text-blue-300' : 'text-blue-700'}`}>
                                Qty: {item.quantity}
                              </Text>
                            </View>
                            <Text className="text-sm text-muted-foreground ml-2">
                              AED {item.price.toFixed(2)} each
                            </Text>
                          </View>
                        </View>
                        <Text className="text-lg font-bold text-foreground">
                          AED {(item.quantity * item.price).toFixed(2)}
                        </Text>
                      </View>

                      {item.notes && (
                        <View className={`p-3 ${isDarkColorScheme ? 'bg-yellow-900/20' : 'bg-yellow-50'} rounded-md border-l-2 ${isDarkColorScheme ? 'border-yellow-600' : 'border-yellow-400'}`}>
                          <Text className="text-xs text-muted-foreground mb-1">Special Instructions</Text>
                          <Text className="text-sm text-foreground">
                            {item.notes}
                          </Text>
                        </View>
                      )}
                    </View>
                  )) || []}
                </View>

                {/* Order Summary */}
                <View className={`mt-6 p-4 ${isDarkColorScheme ? 'bg-green-900/20' : 'bg-green-50'} rounded-lg border ${isDarkColorScheme ? 'border-green-800' : 'border-green-200'}`}>
                  <Text className="text-base font-bold text-foreground mb-3">Order Summary</Text>

                  <View className="space-y-2">
                    <View className="flex-row items-center justify-between">
                      <Text className="text-sm text-muted-foreground">Subtotal</Text>
                      <Text className="text-sm font-semibold text-foreground">AED {subTotal}</Text>
                    </View>

                    {selectedOrder.order_type === 'delivery' && parseFloat(deliveryFee) > 0 && (
                      <View className="flex-row items-center justify-between">
                        <Text className="text-sm text-muted-foreground">Delivery Fee</Text>
                        <Text className="text-sm font-semibold text-foreground">AED {deliveryFee}</Text>
                      </View>
                    )}

                    {(((selectedOrder as any).bill?.discount_amount || selectedOrder.bill_amount?.discount_amount) > 0) && (
                      <View>
                        <View className="flex-row items-center justify-between">
                          <Text className="text-sm text-muted-foreground">Discount</Text>
                          <Text className="text-sm font-semibold text-green-600 dark:text-green-400">
                            -AED {((selectedOrder as any).bill?.discount_amount || selectedOrder.bill_amount?.discount_amount || 0).toFixed(2)}
                          </Text>
                        </View>
                        {((selectedOrder as any).bill?.promo_code || (selectedOrder as any).promo_code || (selectedOrder.bill_amount as any)?.promo_code) && (
                          <View className="flex-row items-center justify-between mt-1">
                            <Text className="text-xs text-muted-foreground">Promocode</Text>
                            <Text className="text-xs font-mono text-muted-foreground">
                              {(selectedOrder as any).bill?.promo_code || (selectedOrder as any).promo_code || (selectedOrder.bill_amount as any)?.promo_code}
                            </Text>
                          </View>
                        )}
                      </View>
                    )}

                    <View className={`border-t ${isDarkColorScheme ? 'border-green-700' : 'border-green-300'} pt-2 mt-3`}>
                      <View className="flex-row items-center justify-between">
                        <Text className="text-lg font-bold text-foreground">Total Amount</Text>
                        <Text className="text-xl font-bold text-foreground">AED {totalBill}</Text>
                      </View>
                    </View>
                  </View>
                </View>
              </CardContent>
            </Card>
          </View>
        </ScrollView>
      </View>
    );
  };

  // Key extractor for FlatList
  const keyExtractor = useCallback((item: Order) => item.order_id, []);

  // Empty component
  const renderEmptyComponent = useCallback(() => (
    <View className="flex-1 items-center justify-center py-16">
      <View className={`w-20 h-20 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
        <Text className="text-3xl">✅</Text>
      </View>
      <Text className="text-foreground text-lg font-medium mb-2">No Past Orders</Text>
      <Text className="text-muted-foreground text-center">Completed orders will appear here</Text>
    </View>
  ), [isDarkColorScheme]);

  // Footer component for spacing
  const renderFooterComponent = useCallback(() => (
    <View className="h-4" />
  ), []);

  const renderOrderList = () => {
    if (isLoadingPast) return renderLoadingSpinner();
    if (pastOrdersError) return renderError(pastOrdersError);

    return (
      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={keyExtractor}
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={renderFooterComponent}
        refreshControl={
          <RefreshControl
            refreshing={isLoadingPast}
            onRefresh={onRefresh}
            tintColor={foodTheme.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        contentContainerStyle={{ paddingTop: 0 }}
        getItemLayout={(data, index) => ({
          length: 220, // Approximate height of each order card
          offset: 220 * index,
          index,
        })}
      />
    );
  };

  // Order Details Modal
  const OrderDetailsModal = () => (
    <Modal
      visible={showDetails}
      transparent={true}
      animationType="slide"
      onRequestClose={handleBackPress}
    >
      <View className="flex-1 bg-black/50 justify-end">
        <View className="bg-card rounded-t-3xl max-h-[90%]">
          {renderOrderDetails(true)}
        </View>
      </View>
    </Modal>
  );



  return (
    <View className="flex-1 bg-background">
      {/* Header */}
      <OrdersHeader
        title="Past Orders"
        showBranchSelector={isOwner}
        branches={branches}
        selectedBranchId={selectedBranchId}
        onSelectBranch={handleBranchSelect}
      />

      {/* Filter Controls */}
      <View className="px-4 py-3 bg-card border-b border-border">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            <TouchableOpacity
              onPress={openFilters}
              className="flex-row items-center bg-muted rounded-lg px-3 py-2 mr-3"
            >
              <Filter size={16} className="text-muted-foreground mr-2" />
              <Text className="text-muted-foreground font-medium">Filters</Text>
              {(appliedSearchText || appliedOrderTypeFilter !== 'all' || appliedStatusFilter !== 'all' || appliedDriverFilter !== 'all') && (
                <View className="w-2 h-2 bg-red-500 rounded-full ml-2" />
              )}
            </TouchableOpacity>

            <Text className="text-sm text-muted-foreground">
              {filteredOrders.length} of {pastOrders.length} orders
            </Text>
          </View>

          {/* Reset Filters Button */}
          {(appliedSearchText || appliedOrderTypeFilter !== 'all' || appliedStatusFilter !== 'all' || appliedDriverFilter !== 'all') && (
            <TouchableOpacity
              onPress={resetFilters}
              className="bg-muted rounded-lg px-3 py-2"
            >
              <Text className="text-muted-foreground text-sm font-medium">Reset</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View className={`flex-1 ${isTablet ? 'flex-row' : ''}`}>
        {/* Order List */}
        <View className={`${isTablet ? 'w-1/2' : 'flex-1'} bg-card ${isTablet ? 'border-r border-border' : ''}`}>
          <View className="flex-1 px-4 pt-4">
            {renderOrderList()}
          </View>
        </View>

        {/* Order Details (Tablet Only) */}
        {isTablet && (
          <View className="flex-1 bg-background">
            {renderOrderDetails()}
          </View>
        )}
      </View>

      {/* Order Details Modal */}
      <OrderDetailsModal />

      {/* Filter Modal */}
      <FilterModal 
        visible={showFilters}
        onClose={handleCloseModal}
        tempSearchText={tempSearchText}
        onSearchTextChange={handleSearchTextChange}
        tempOrderTypeFilter={tempOrderTypeFilter}
        onOrderTypeChange={handleOrderTypeChange}
        tempStatusFilter={tempStatusFilter}
        onStatusChange={handleStatusChange}
        tempDriverFilter={tempDriverFilter}
        onDriverChange={handleDriverChange}
        onClearAllFilters={clearAllFilters}
        onApplyFilters={applyFilters}
        isDarkColorScheme={isDarkColorScheme}
        screenHeight={screenHeight}
        foodTheme={foodTheme}
      />
    </View>
  );
} 