import React from 'react';
import { Tabs } from 'expo-router';
import { ShoppingBag, CheckCircle, Archive, ClipboardList } from 'lucide-react-native';
import { useNewOrders, useAcceptedOrders } from '~/lib/hooks/useOrders';
import { Text } from '~/components/ui/text';
import { View, Platform } from 'react-native';
import { useColorScheme } from '~/lib/useColorScheme';
import { BlurView } from 'expo-blur';
import { useAuth } from '~/lib/hooks/useAuth';
import Login from '~/components/Login';

// Supported merchant brands (affects accent colour)
export type MerchantType = 'sports' | 'food' | 'commerce';

// Badge component for tab bar
function TabBarBadge({ count, type }: { count: number; type: 'new' | 'accepted' }) {
  if (count === 0) return null;
  
  return (
    <View
      style={{
        position: 'absolute',
        top: -4,
        right: -12,
        backgroundColor: type === 'new' ? '#ff2b2b' : '#10b981',
        borderRadius: 8,
        minWidth: 16,
        height: 16,
        paddingHorizontal: 2,
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Text className="text-[10px] font-bold text-white">
        {count > 99 ? '99+' : count}
      </Text>
    </View>
  );
}

export default function TabLayout() {
  // Fetch order counts for badges
  const { data: newOrders = [] } = useNewOrders();
  const { data: acceptedOrders = [] } = useAcceptedOrders();
  const { isDarkColorScheme } = useColorScheme();
  const { isAuthenticated, isLoading } = useAuth();

 
  if (!isAuthenticated) {
     return <Login />;
  }

  // Accent colour for food merchant
  const accent = '#ff2b2b';

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: accent,
        tabBarInactiveTintColor: '#9ca3af',
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
            backgroundColor: 'transparent',
            borderTopWidth: 0,
            elevation: 0,
          },
          default: {
            backgroundColor: isDarkColorScheme ? 'rgba(23,23,23,0.9)' : 'rgba(255,255,255,0.9)',
            borderTopWidth: 1,
            borderTopColor: isDarkColorScheme ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            elevation: 12,
            
          },
        }),
        tabBarBackground: Platform.OS === 'ios' ? () => (
          <BlurView
            intensity={50}
            tint={isDarkColorScheme ? 'dark' : 'light'}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
            }}
          />
        ) : undefined,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          // marginTop: 4,
         },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Orders',
          tabBarIcon: ({ color, size }) => (
            <View style={{ position: 'relative' }}>
              <ClipboardList size={size} color={color} />
              <TabBarBadge count={newOrders.length + acceptedOrders.length} type="new" />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="new-orders"
        options={{
          title: 'New',
          tabBarIcon: ({ color, size }) => (
            <View style={{ position: 'relative' }}>
              <ShoppingBag size={size} color={color} />
              <TabBarBadge count={newOrders.length} type="new" />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="accepted-orders"
        options={{
          title: 'Accepted',
          tabBarIcon: ({ color, size }) => (
            <View style={{ position: 'relative' }}>
              <CheckCircle size={size} color={color} />
              <TabBarBadge count={acceptedOrders.length} type="accepted" />
            </View>
          ),
        }}
      />
      <Tabs.Screen
        name="past-orders"
        options={{
          title: 'Past',
          tabBarIcon: ({ color, size }) => <Archive size={size} color={color} />,
        }}
      />
    </Tabs>
  );
} 