<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-updates-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-json-utils\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-manifests\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-dev-client\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out"><file name="app.config" path="H:\Cravin\cravin-app\node_modules\expo-constants\android\build\intermediates\library_assets\release\packageReleaseAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-modules-core\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-dev-menu\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-svg\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-reanimated\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-onesignal" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-onesignal\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\expo\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-screens\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\release\packageReleaseAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\android\app\src\main\assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\android\app\src\release\assets"/></dataSet><dataSet config="assets-createBundleReleaseJsAndAssets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\android\app\build\generated\assets\createBundleReleaseJsAndAssets"><file name="index.android.bundle" path="H:\Cravin\cravin-app\android\app\build\generated\assets\createBundleReleaseJsAndAssets\index.android.bundle"/></source></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="H:\Cravin\cravin-app\android\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>