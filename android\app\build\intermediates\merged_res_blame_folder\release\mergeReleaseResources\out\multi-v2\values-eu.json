{"logs": [{"outputFile": "com.cravin.merchant.app-mergeReleaseResources-71:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\250db4afb8fa7de0d997879e3d4adf3d\\transformed\\media3-exoplayer-1.4.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,187,253,325,402,476,587,685", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "119,182,248,320,397,471,582,680,748"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10007,10076,10139,10205,10277,10354,10428,10539,10637", "endColumns": "68,62,65,71,76,73,110,97,67", "endOffsets": "10071,10134,10200,10272,10349,10423,10534,10632,10700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e4d7ecf19998ab3515e1b90137c4d08f\\transformed\\biometric-1.1.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,378,514,647,783,909,1066,1165,1309,1448", "endColumns": "110,92,118,135,132,135,125,156,98,143,138,138", "endOffsets": "161,254,373,509,642,778,904,1061,1160,1304,1443,1582"}, "to": {"startLines": "83,85,143,144,145,146,147,148,149,150,151,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7473,7684,12278,12397,12533,12666,12802,12928,13085,13184,13328,13467", "endColumns": "110,92,118,135,132,135,125,156,98,143,138,138", "endOffsets": "7579,7772,12392,12528,12661,12797,12923,13080,13179,13323,13462,13601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd29036936cb81e953b907f3a3c29b2f\\transformed\\material-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1136,1203,1302,1370,1431,1519,1582,1648,1712,1783,1846,1900,2009,2068,2131,2185,2259,2384,2474,2552,2641,2724,2804,2949,3032,3114,3252,3343,3426,3478,3531,3597,3668,3748,3819,3899,3977,4055,4128,4203,4310,4397,4484,4575,4668,4740,4816,4908,4959,5041,5107,5191,5277,5339,5403,5466,5534,5641,5750,5846,5951,6007,6064,6147,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "270,377,482,562,669,769,867,982,1065,1131,1198,1297,1365,1426,1514,1577,1643,1707,1778,1841,1895,2004,2063,2126,2180,2254,2379,2469,2547,2636,2719,2799,2944,3027,3109,3247,3338,3421,3473,3526,3592,3663,3743,3814,3894,3972,4050,4123,4198,4305,4392,4479,4570,4663,4735,4811,4903,4954,5036,5102,5186,5272,5334,5398,5461,5529,5636,5745,5841,5946,6002,6059,6142,6227,6304,6381"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,86,87,139,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,3672,3779,3884,3964,4071,4902,5000,5115,7777,7843,11859,13606,13674,13735,13823,13886,13952,14016,14087,14150,14204,14313,14372,14435,14489,14563,14688,14778,14856,14945,15028,15108,15253,15336,15418,15556,15647,15730,15782,15835,15901,15972,16052,16123,16203,16281,16359,16432,16507,16614,16701,16788,16879,16972,17044,17120,17212,17263,17345,17411,17495,17581,17643,17707,17770,17838,17945,18054,18150,18255,18311,18368,18534,18619,18696", "endLines": "22,50,51,52,53,54,62,63,64,86,87,139,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,215,216,217", "endColumns": "12,106,104,79,106,99,97,114,82,65,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,77,88,82,79,144,82,81,137,90,82,51,52,65,70,79,70,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82,84,76,76", "endOffsets": "922,3774,3879,3959,4066,4166,4995,5110,5193,7838,7905,11953,13669,13730,13818,13881,13947,14011,14082,14145,14199,14308,14367,14430,14484,14558,14683,14773,14851,14940,15023,15103,15248,15331,15413,15551,15642,15725,15777,15830,15896,15967,16047,16118,16198,16276,16354,16427,16502,16609,16696,16783,16874,16967,17039,17115,17207,17258,17340,17406,17490,17576,17638,17702,17765,17833,17940,18049,18145,18250,18306,18363,18446,18614,18691,18768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\02a6c65af6aca4fe0b17434c7fe8f02b\\transformed\\play-services-base-18.0.1\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5198,5302,5474,5598,5707,5859,5984,6108,6359,6537,6645,6808,6936,7090,7250,7316,7381", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "5297,5469,5593,5702,5854,5979,6103,6211,6532,6640,6803,6931,7085,7245,7311,7376,7468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4689ac814784dd79f2206f332baa7585\\transformed\\play-services-basement-18.1.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6216", "endColumns": "142", "endOffsets": "6354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b1efdd330a6997d521c41b8a19879f0\\transformed\\exoplayer-ui-2.18.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,475,657,741,827,902,1001,1092,1187,1255,1351,1447,1514,1586,1651,1722,1849,1971,2094,2163,2252,2324,2419,2519,2621,2687,2754,2807,2865,2914,2975,3037,3109,3173,3240,3305,3369,3436,3502,3569,3623,3690,3771,3852", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "281,470,652,736,822,897,996,1087,1182,1250,1346,1442,1509,1581,1646,1717,1844,1966,2089,2158,2247,2319,2414,2514,2616,2682,2749,2802,2860,2909,2970,3032,3104,3168,3235,3300,3364,3431,3497,3564,3618,3685,3766,3847,3903"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,570,7910,7994,8080,8155,8254,8345,8440,8508,8604,8700,8767,8839,8904,8975,9102,9224,9347,9416,9505,9577,9672,9772,9874,9940,10705,10758,10816,10865,10926,10988,11060,11124,11191,11256,11320,11387,11453,11520,11574,11641,11722,11803", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,83,85,74,98,90,94,67,95,95,66,71,64,70,126,121,122,68,88,71,94,99,101,65,66,52,57,48,60,61,71,63,66,64,63,66,65,66,53,66,80,80,55", "endOffsets": "376,565,747,7989,8075,8150,8249,8340,8435,8503,8599,8695,8762,8834,8899,8970,9097,9219,9342,9411,9500,9572,9667,9767,9869,9935,10002,10753,10811,10860,10921,10983,11055,11119,11186,11251,11315,11382,11448,11515,11569,11636,11717,11798,11854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62900a8129d9ab3abaa7c7365313cbcb\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "55,56,57,58,59,60,61,218", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4171,4269,4372,4472,4575,4680,4783,18773", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "4264,4367,4467,4570,4675,4778,4897,18869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\47c4be2db69016738bbafb806598f314\\transformed\\browser-1.6.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "84,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7584,11958,12060,12173", "endColumns": "99,101,112,104", "endOffsets": "7679,12055,12168,12273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\281993a8081a7f8ed7163ce2e30584f1\\transformed\\appcompat-1.7.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "927,1036,1134,1244,1330,1436,1560,1646,1727,1819,1913,2009,2103,2204,2298,2394,2491,2583,2676,2758,2867,2976,3075,3184,3291,3402,3573,18451", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "1031,1129,1239,1325,1431,1555,1641,1722,1814,1908,2004,2098,2199,2293,2389,2486,2578,2671,2753,2862,2971,3070,3179,3286,3397,3568,3667,18529"}}]}]}