import { Audio, InterruptionModeIOS, InterruptionModeAndroid } from 'expo-av';
import { Platform } from 'react-native';

class SoundService {
  private alertSound: Audio.Sound | null = null;
  private isPlaying = false;

  async initialize() {
    try {
      console.log('🔊 Initializing SoundService...');
      
      // Set audio mode for notifications with background support
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true, // Enable background audio playback
        playsInSilentModeIOS: true, // Play even in silent mode on iOS
        shouldDuckAndroid: true, // Duck other audio on Android
        playThroughEarpieceAndroid: false, // Use speakers, not earpiece
        interruptionModeIOS: InterruptionModeIOS.DoNotMix, // Don't mix with other audio
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix, // Don't mix with other audio
      });

      console.log('✅ SoundService initialized with background audio support');
    } catch (error) {
      console.error('❌ Error initializing SoundService:', error);
    }
  }

  async playOrderAlert() {
    try {
      console.log('🔊 Playing order alert sound...');
      
      // Stop any existing sound first
      try {
        await this.stopAlert();
      } catch (stopError) {
        console.warn('⚠️ Error stopping previous sound:', stopError);
      }

      // Try to create and load the sound with better error handling
      let sound: Audio.Sound;
      
      try {
        // First try with order-notification.wav
        const { sound: newSound } = await Audio.Sound.createAsync(
          require('../../assets/sounds/order-notification.wav'),
          { 
            shouldPlay: true,
            isLooping: true,
            volume: 1.0,
          }
        );
        sound = newSound;
      } catch (primaryError) {
        console.warn('⚠️ Primary audio file failed, trying fallback:', primaryError);
        
        try {
          // Fallback to notification.wav
          const { sound: fallbackSound } = await Audio.Sound.createAsync(
            require('../../assets/sounds/notification.wav'),
            { 
              shouldPlay: true,
              isLooping: true,
              volume: 1.0,
            }
          );
          sound = fallbackSound;
        } catch (fallbackError) {
          console.error('❌ Both audio files failed:', fallbackError);
          
          // Use system sound as final fallback
          console.log('🔊 Using system notification sound as fallback');
          // We could implement a system sound here using native modules
          // For now, just log that we attempted to play sound
          this.isPlaying = false;
          return;
        }
      }

      this.alertSound = sound;
      this.isPlaying = true;

      console.log('✅ Order alert sound started');
    } catch (error) {
      console.error('❌ Error playing order alert:', error);
      // Fallback - just log that we attempted to play sound
      this.isPlaying = false;
    }
  }

  async stopAlert() {
    try {
      if (this.alertSound) {
        console.log('🔇 Stopping alert sound...');
        
        // Store reference to avoid race conditions
        const soundToStop = this.alertSound;
        
        // Clear the reference immediately to prevent duplicate stops
        this.alertSound = null;
        this.isPlaying = false;
        
        // Try to stop the sound
        try {
          await soundToStop.stopAsync();
        } catch (stopError) {
          console.warn('⚠️ Error stopping sound playback:', stopError);
        }
        
        // Try to unload the sound
        try {
          await soundToStop.unloadAsync();
        } catch (unloadError) {
          console.warn('⚠️ Error unloading sound:', unloadError);
        }
        
        console.log('✅ Alert sound stopped');
      }
    } catch (error) {
      console.error('❌ Error stopping alert sound:', error);
      // Reset state even if stop failed
      this.alertSound = null;
      this.isPlaying = false;
    }
  }

  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Test background audio capabilities
   */
  async testBackgroundSound(): Promise<boolean> {
    try {
      console.log('🧪 Testing background audio capabilities...');
      
      // First ensure we have the right audio mode
      await this.initialize();
      
      // Try to play a short test sound
      const { sound } = await Audio.Sound.createAsync(
        require('../../assets/sounds/notification.wav'),
        { 
          shouldPlay: true,
          isLooping: false,
          volume: 0.5, // Lower volume for test
        }
      );

      // Stop after 2 seconds
      setTimeout(async () => {
        try {
          if (sound) {
            try {
              await sound.stopAsync();
            } catch (stopError) {
              console.warn('⚠️ Error stopping test sound playback:', stopError);
            }
            
            try {
              await sound.unloadAsync();
            } catch (unloadError) {
              console.warn('⚠️ Error unloading test sound:', unloadError);
            }
          }
        } catch (error) {
          console.warn('⚠️ Error stopping test sound:', error);
        }
      }, 2000);

      console.log('✅ Background audio test completed');
      return true;
    } catch (error) {
      console.error('❌ Background audio test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const soundService = new SoundService(); 