import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  ScrollView,
  Pressable,
  RefreshControl,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Text } from '~/components/ui/text';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import {
  useNewOrders,
  useAcceptedOrders,
} from '~/lib/hooks/useOrders';
import { useAuth } from '~/lib/hooks/useAuth';
import { Order } from '~/lib/api/orders';
import { useColorScheme } from '~/lib/useColorScheme';
import { OrdersHeader } from '~/components/food/OrdersHeader';
import { useUnifiedNotifications } from '~/lib/providers/UnifiedNotificationProvider';
import { ShoppingBag, CheckCircle, History, Bell } from 'lucide-react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth >= 768;

// Food theme colors with dark mode support - memoized function
const getFoodTheme = (isDark: boolean) => ({
  primary: '#ff2b2b',
  primaryBg: isDark ? 'bg-red-600 dark:bg-red-600' : 'bg-red-500',
  primaryDark: isDark ? 'bg-red-700 dark:bg-red-700' : 'bg-red-600',
  primaryLight: isDark ? 'bg-red-900/20 dark:bg-red-900/20' : 'bg-red-50',
  primaryBorder: isDark ? 'border-red-500 dark:border-red-500' : 'border-red-400',
  primaryText: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-600',
  primaryTextDark: isDark ? 'text-red-300 dark:text-red-300' : 'text-red-700',
  accent: '#ff2b2b',
});

function OrdersScreenContent() {
  // Router
  const router = useRouter();

  // Authentication
  const { session } = useAuth();
  
  const { isDarkColorScheme } = useColorScheme();
  
  // Notifications
  const { testOrderNotification, stopOrderAlert, testBackgroundSound } = useUnifiedNotifications();
  
  // Memoize the food theme to prevent re-computation on every render
  const foodTheme = useMemo(() => getFoodTheme(isDarkColorScheme), [isDarkColorScheme]);

  // Data-fetching hooks
  const {
    data: newOrders = [],
    isLoading: isLoadingNew,
    error: newOrdersError,
    refetch: refetchNewOrders,
  } = useNewOrders();

  const {
    data: acceptedOrders = [],
    isLoading: isLoadingAccepted,
    error: acceptedOrdersError,
    refetch: refetchAcceptedOrders,
  } = useAcceptedOrders();

  // Navigation handlers - memoized to prevent re-creation on every render
  const handleNewOrdersPress = useMemo(() => () => {
     router.push('/(tabs)/new-orders');
  }, []);

  const handleAcceptedOrdersPress = useMemo(() => () => {
     router.push('/(tabs)/accepted-orders');
  }, []);

  const handlePastOrdersPress = useMemo(() => () => {
     router.push('/(tabs)/past-orders');
  }, []);

  const onRefresh = useMemo(() => () => {
    refetchNewOrders();
    refetchAcceptedOrders();
  }, [refetchNewOrders, refetchAcceptedOrders]);

  const isLoading = isLoadingNew || isLoadingAccepted;

  // Memoize background colors to prevent re-computation
  const bgColors = useMemo(() => ({
    newOrders: isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100',
    acceptedOrders: isDarkColorScheme ? 'bg-green-900/30' : 'bg-green-100',
    pastOrders: isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-100',
  }), [isDarkColorScheme]);

  // Memoize the renderOrderCard function to prevent re-creation on every render
  const renderOrderCard = useMemo(() => (
    title: string,
    count: number,
    icon: React.ElementType,
    onPress: () => void,
    color: string,
    bgColor: string,
    hideCount?: boolean
  ) => (
    <Pressable
      onPress={onPress}
      className="mb-4"
      style={{ opacity: isLoading ? 0.6 : 1 }}
    >
      <Card className="shadow-lg border-0 bg-card">
        <CardContent className="p-6">
          <View className="flex-row items-center justify-between">
            <View className="flex-1">
              <View className="flex-row items-center mb-2">
                <View className={`w-12 h-12 ${bgColor} rounded-xl items-center justify-center mr-4`}>
                  {React.createElement(icon, { size: 24, color })}
                </View>
                <View className="flex-1">
                  <Text className="text-lg font-bold text-foreground">{title}</Text>
                  <Text className="text-sm text-muted-foreground">Manage your {title.toLowerCase()}</Text>
                </View>
              </View>
            </View>
            <View className="items-center">
              {!hideCount && (
                <>
                  <Text className="text-3xl font-bold text-foreground">{count}</Text>
                  <Text className="text-sm text-muted-foreground">orders</Text>
                </>
              )}
            </View>
          </View>
        </CardContent>
      </Card>
    </Pressable>
  ), [isLoading]);

  return (
    <View className="flex-1 bg-background">
      <OrdersHeader
        title="Orders Management"
      />

      <ScrollView
        className="flex-1 px-4 pt-4"
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={onRefresh}
            tintColor={foodTheme.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Order Summary Cards */}
        {renderOrderCard(
          'New Orders',
          newOrders.length,
          ShoppingBag,
          handleNewOrdersPress,
          '#ff2b2b',
          bgColors.newOrders
        )}

        {renderOrderCard(
          'Accepted Orders',
          acceptedOrders.length,
          CheckCircle,
          handleAcceptedOrdersPress,
          '#10b981',
          bgColors.acceptedOrders
        )}

        {renderOrderCard(
          'Past Orders',
          0, // You can add a hook for past orders count
          History,
          handlePastOrdersPress,
          '#6b7280',
          bgColors.pastOrders,
          true
        )}

        {/* Test Order Notification Button */}
        {/* <Card className="mb-4 shadow-lg border-0 bg-card">
          <CardContent className="p-4">
            <Text className="text-lg font-bold text-foreground mb-2">🧪 Testing</Text>
            <View className="gap-2">
              <Button
                onPress={() => {
                  Alert.alert(
                    '🧪 Test Order Notification',
                    'This will show a test order notification. Make sure your sound is on!',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Test Now',
                        onPress: () => testOrderNotification(),
                      },
                    ]
                  );
                }}
                className="bg-orange-500 flex-row items-center justify-center"
              >
                <Bell size={20} color="white" style={{ marginRight: 8 }} />
                <Text className="text-white font-semibold">Test Order Notification</Text>
              </Button>
              
              <Button
                onPress={() => {
                  Alert.alert(
                    '🔇 Stop Alert Sound',
                    'This will stop any playing alert sounds.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Stop Now',
                        onPress: () => stopOrderAlert(),
                      },
                    ]
                  );
                }}
                className="bg-red-500 flex-row items-center justify-center"
              >
                <Text className="text-white font-semibold">Stop Alert Sound</Text>
              </Button>

              <Button
                onPress={async () => {
                  Alert.alert(
                    '🎵 Test Background Sound',
                    'This will test if background audio works. Minimize the app after tapping "Test" and listen for the sound.',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Test',
                        onPress: async () => {
                          const success = await testBackgroundSound();
                          Alert.alert(
                            success ? '✅ Success' : '❌ Failed',
                            success 
                              ? 'Background audio test completed! You should have heard a sound.'
                              : 'Background audio test failed. Check permissions and audio settings.'
                          );
                        },
                      },
                    ]
                  );
                }}
                className="bg-blue-500 flex-row items-center justify-center"
              >
                <Text className="text-white font-semibold">Test Background Sound</Text>
              </Button>
            </View>
          </CardContent>
        </Card> */}

        {/* Bottom padding for safe area */}
        <View className="h-20" />
      </ScrollView>
    </View>
  );
}

export default function OrdersScreen() {
  return <OrdersScreenContent />;
} 