import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Menu } from 'lucide-react-native';
import { useColorScheme } from '~/lib/useColorScheme';

interface MenuButtonProps {
    onPress: () => void;
    color?: string;
    size?: number;
    className?: string;
}

export function MenuButton({ onPress, color, size = 24, className = '' }: MenuButtonProps) {
    const { isDarkColorScheme } = useColorScheme();
    
    const defaultColor = color || (isDarkColorScheme ? '#ffffff' : '#000000');

    return (
        <TouchableOpacity
            onPress={onPress}
            className={`p-2 rounded-lg active:bg-black/10 ${className}`}
            activeOpacity={0.7}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
            <Menu size={size} color={defaultColor} />
        </TouchableOpacity>
    );
} 