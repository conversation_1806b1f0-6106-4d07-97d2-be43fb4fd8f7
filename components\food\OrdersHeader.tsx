import React, { useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { ArrowLeft, LogOut, Bell, BellOff } from 'lucide-react-native';
import { Text } from '~/components/ui/text';
import { BranchSelector } from '~/components/food/BranchSelector';
import { useAuth } from '~/lib/hooks/useAuth';
import { useColorScheme } from '~/lib/useColorScheme';
import { useAuthStore } from '~/lib/stores/auth-store';
import { useApiNotification } from '~/lib/providers/ApiNotificationProvider';

type Branch = {
  branch_id: string;
  branch_name: string;
};

interface OrdersHeaderProps {
  title: string;
  showBackButton?: boolean;
  showLogoutButton?: boolean;
  showBranchSelector?: boolean;
  branches?: Branch[];
  selectedBranchId?: string | null;
  onSelectBranch?: (branchId: string | null) => void;
  children?: React.ReactNode;
}

// Food theme colors with dark mode support
const getFoodTheme = (isDark: boolean) => ({
  primary: '#ff2b2b',
  primaryBg: isDark ? 'bg-red-600 dark:bg-red-600' : 'bg-red-500',
  primaryDark: isDark ? 'bg-red-700 dark:bg-red-700' : 'bg-red-600',
  primaryLight: isDark ? 'bg-red-900/20 dark:bg-red-900/20' : 'bg-red-50',
  primaryBorder: isDark ? 'border-red-500 dark:border-red-500' : 'border-red-400',
  primaryText: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-600',
  primaryTextDark: isDark ? 'text-red-300 dark:text-red-300' : 'text-red-700',
  accent: '#ff2b2b',
});

export function OrdersHeader({
  title,
  showBackButton = false,
  showLogoutButton = true,
  showBranchSelector = false,
  branches = [],
  selectedBranchId,
  onSelectBranch,
  children
}: OrdersHeaderProps) {
  const { session } = useAuth();
  const { isDarkColorScheme } = useColorScheme();
  const router = useRouter();
  
  // Memoize the theme to prevent unnecessary re-renders
  const foodTheme = useMemo(() => getFoodTheme(isDarkColorScheme), [isDarkColorScheme]);
  
  const insets = useSafeAreaInsets();

  const logout = useAuthStore((state) => state.logout);
  
  // Notification status
  let notificationsEnabled = false;
  let pendingCount = 0;
  try {
    const notificationCtx = useApiNotification();
    notificationsEnabled = notificationCtx.isEnabled;
    pendingCount = notificationCtx.pendingCount;
  } catch {}

  const handleBackPress = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push('/(tabs)');
    }
  };

  const handleLogoutPress = async () => {
    try {
      await logout();
      router.replace('/');
    } catch (error) {
      console.warn('Error during logout:', error);
    }
  };

  return (
    <SafeAreaView
      edges={['top']}
      style={{ backgroundColor: foodTheme.primary }}
      className={`${foodTheme.primaryBg} shadow-sm`}
    >
      <View className="px-6 pt-6 pb-4">
      <View className="flex-row items-center ">
          {showBackButton && (
            <TouchableOpacity
              onPress={handleBackPress}
              className="w-10 h-10 bg-white/20 rounded-full items-center justify-center mr-4"
            >
              <ArrowLeft size={20} className="text-white" />
            </TouchableOpacity>
          )}
          <Text className="text-3xl font-bold text-white flex-1">
            {title}
          </Text>

          {/* Notification Status Indicator */}
          {/* <View className="w-8 h-8 rounded-full items-center justify-center mr-3">
            {pendingCount > 0 ? (
              <Bell size={20} color="white" />
            ) : notificationsEnabled ? (
              <BellOff size={20} color="rgba(255,255,255,0.7)" />
            ) : null}
            {pendingCount > 0 && (
              <View className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full" />
            )}
          </View> */}

          {showLogoutButton && (
            <TouchableOpacity
              onPress={handleLogoutPress}
              className="w-10 h-10 bg-white/20 rounded-full items-center justify-center ml-1"
            >
              <LogOut size={20} color="white" />
            </TouchableOpacity>
          )}
        </View>

        {/* Custom content (e.g., tabs) */}
        {children}

        {/* Branch Selector */}
        {showBranchSelector && onSelectBranch && (
          <BranchSelector
            branches={branches}
            selectedBranchId={selectedBranchId || null}
            onSelectBranch={onSelectBranch}
          />
        )}
      </View>
    </SafeAreaView>
  );
} 