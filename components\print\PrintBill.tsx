import React from 'react';
import { Order } from '~/lib/api/orders';
import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';

interface PrintBillProps {
  order: Order;
  businessInfo?: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    vatNumber?: string;
  };
}

/**
 * Function to convert the Cravin Food logo to base64 for embedding in PDF
 * This allows the logo to be displayed in the printed receipt
 */
const getLogoBase64 = async (): Promise<string> => {
  try {
    // const asset = Asset.fromModule(require('../../assets/images/CravinFood-logo.webp'));
    // await asset.downloadAsync();
    
    // if (asset.localUri) {
    //   const base64 = await FileSystem.readAsStringAsync(asset.localUri, {
    //     encoding: FileSystem.EncodingType.Base64,
    //   });
    //   return `data:image/webp;base64,${base64}`;
    // }
    return 'https://food.justcravin.com/Cravin-Food/_next/static/media/CravinFood.c6b16a28.png';
  } catch (error) {
    console.error('Error loading Cravin Food logo for PDF:', error);
    // Return empty string to fallback to text logo
    return '';
  }
};

export const generatePrintBillHTML = async (order: Order, businessInfo?: PrintBillProps['businessInfo']): Promise<string> => {
  // Get logo as base64
  const logoBase64 = await getLogoBase64();
  
  const restaurantName = businessInfo?.name || 'Cravin Restaurant';
  const branchName = businessInfo?.address || 'Main Branch'; // address field contains branch_name from API
  const trnNumber = businessInfo?.vatNumber || 'TRN 123456789';
  const branchPhone = businessInfo?.phone || '+971 XX XXX XXXX';

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-AE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatCurrency = (amount: number) => {
    return amount.toFixed(2);
  };

  const billData = order.bill_amount || (order as any).bill;
  const previousBill = billData?.sub_total || 0;
  const deliveryCharge = billData?.delivery_charge || 0;
  const discountAmount = billData?.discount_amount || 0;
  const totalBill = billData?.total_bill || 0;
  
  // VAT calculations (assuming 5% VAT is included in total)
  const netTotalWithoutVAT = totalBill / 1.05;
  const vatAmount = totalBill - netTotalWithoutVAT;

  const customerName = (order as any).receiver_name || order.customer_name || 'N/A';
  const customerPhone = (order as any).receiver_number || order.customer_number || 'N/A';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Order Bill - ${order.order_name}</title>
      <style>

        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          font-size: 12px;
          line-height: 1.2;
          color: #000;
          background: white;
          margin: 0;
          padding: 10px;
          -webkit-print-color-adjust: exact;
          color-adjust: exact;
        }
        
        .bill-container {
          width: 280px;
          margin: 0 auto;
          background: white;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
          padding: 0;
        }
        
        .header {
          text-align: center;
          padding: 10px 5px;
          margin-bottom: 0;
          border-bottom: 1px solid #ccc;
        }
        
        .logo-section {
          margin: 5px auto 8px;
          width: 80px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
        }
        
        .logo-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        
        .restaurant-name {
          font-size: 16px;
          font-weight: bold;
          text-align: center;
          margin: 3px 0;
          text-transform: uppercase;
        }
        
        .branch-info {
          font-size: 11px;
          text-align: center;
          margin: 2px 0;
          line-height: 1.2;
        }
        
        .order-header {
          text-align: center;
          padding: 8px 5px;
          margin-bottom: 0;
          border-bottom: 1px solid #ccc;
        }
        
        .order-id {
          font-size: 18px;
          font-weight: bold;
          text-align: center;
          margin: 2px 0;
        }
        
        .order-type {
          font-size: 14px;
          font-weight: bold;
          text-transform: uppercase;
          text-align: center;
          margin: 2px 0;
        }
        
        .order-date {
          font-size: 10px;
          text-align: center;
          margin: 2px 0;
        }
        
        .customer-section {
          padding: 8px 5px;
          text-align: center;
          font-size: 10px;
          border-bottom: 1px solid #ccc;
        }
        
        .customer-info {
          font-size: 10px;
          text-align: center;
          margin: 2px 0;
          line-height: 1.3;
        }
        
        .address-info {
          font-size: 9px;
          text-align: center;
          margin: 8px 0 2px 0;
          padding: 0 5px;
          line-height: 1.3;
          word-wrap: break-word;
        }
        
        .items-table {
          margin-bottom: 0;
          border-bottom: 1px solid #ccc;
        }
        
        .item-row {
          padding: 3px 5px;
          font-size: 9px;
          margin-bottom: 2px;
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          font-weight: bold;
          margin-bottom: 1px;
          font-size: 10px;
        }
        
        .item-name {
          flex: 1;
          text-align: left;
          word-wrap: break-word;
          padding-right: 5px;
        }
        
        .item-price {
          text-align: right;
          white-space: nowrap;
        }
        
        .item-details {
          display: flex;
          justify-content: space-between;
          color: #555;
          font-size: 8px;
          margin-bottom: 1px;
        }
        
        .item-notes {
          font-size: 8px;
          color: #555;
          margin-top: 2px;
          font-style: italic;
          text-align: left;
        }
        
        .bill-summary {
          padding: 8px 5px;
          font-size: 10px;
          border-bottom: 1px solid #ccc;
        }
        
        .summary-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2px;
          padding: 1px 0;
          font-size: 10px;
        }
        
        .summary-label {
          text-align: left;
          flex: 1;
        }
        
        .summary-value {
          text-align: right;
          white-space: nowrap;
          font-weight: bold;
        }
        
        .summary-row.total {
          font-weight: bold;
          padding-top: 4px;
          margin-top: 4px;
          font-size: 11px;
          border-top: 1px solid #ccc;
        }
        
        .vat-note {
          text-align: center;
          font-size: 8px;
          margin-top: 4px;
          padding: 0 5px;
          line-height: 1.2;
        }
        
        .payment-method {
          text-align: center;
          padding: 10px 5px;
          font-size: 20px;
          font-weight: bold;
          letter-spacing: 2px;
          border-bottom: 1px solid #ccc;
        }
        
        .notes-section {
          padding: 8px 5px;
          font-size: 9px;
          text-align: left;
        }
        
        .notes-label {
          font-weight: bold;
          margin-right: 2px;
        }
        
        .footer {
          text-align: center;
          padding: 8px 5px;
          font-size: 9px;
          font-style: italic;
        }
        
        @media print {
          body {
            padding: 0;
            margin: 0;
            font-size: 10px;
          }
          
          .bill-container {
            border-radius: 0;
            box-shadow: none;
            width: 280px;
            margin: 0 auto;
            page-break-inside: avoid;
          }
          
          .header, .order-header, .customer-section, 
          .items-table, .bill-summary, .payment-method {
            page-break-inside: avoid;
          }
          
          /* Ensure borders print correctly */
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          
          .item-row {
            page-break-inside: avoid;
          }
        }
      </style>
    </head>
    <body>
      <div class="bill-container">
        <!-- Header with Logo and Restaurant Info -->
        <div class="header">
          <div class="logo-section">
            ${logoBase64 ? `<img src="${logoBase64}" alt="Cravin Food" class="logo-image" />` : 'CRAVIN FOOD'}
          </div>
          <div class="restaurant-name">${restaurantName}</div>
          <div class="branch-info">${branchName}</div>
          <div class="branch-info">${trnNumber}</div>
          <div class="branch-info">${branchPhone}</div>
        </div>
                
        <!-- Order Header -->
        <div class="order-header">
          <div class="order-id">#${order.order_name}</div>
          <div class="order-type">${order.order_type}</div>
          <div class="order-date">${formatDate(order.ordered_on)}</div>
        </div>
        
        <!-- Customer Information -->
        <div class="customer-section">
          ${order.order_type === 'pickup' ? `
            <div class="customer-info">
              <span class="notes-label">Name:</span> ${customerName}
            </div>
            <div class="customer-info">
              <span class="notes-label">Mobile No:</span> ${customerPhone}
            </div>
          ` : `
            <div class="customer-info">
              <span class="notes-label">Name:</span> ${customerName}
            </div>
            <div class="customer-info">
              <span class="notes-label">Mobile No:</span> ${customerPhone}
            </div>
            ${order.address ? `
            <div class="address-info">
              ${order.address}
            </div>
            ` : ''}
          `}
        </div>
        
        <!-- Order Items -->
        <div class="items-table">
          ${(order.cart_items || order.items)?.map((item: any) => `
            <div class="item-row">
              <div class="item-header">
                <div class="item-name">${item.name}</div>
                <div class="item-price">${formatCurrency(item.quantity * item.price)}</div>
              </div>
              <div class="item-details">
                <span>Qty: ${item.quantity} × ${formatCurrency(item.price)}</span>
                <span>${item.type === 'veg' ? 'VEG' : 'NON-VEG'}</span>
              </div>
              ${item.variants?.length > 0 ? `
                <div class="item-details">
                  <span colspan="2">Variants: ${item.variants.map((v: any) => v.name).join(', ')}</span>
                </div>
              ` : ''}
              ${item.add_ons && item.add_ons.length > 0 ? `
                <div class="item-details">
                  <span colspan="2">Add-ons: ${item.add_ons.map((a: any)   => a.name).join(', ')}</span>
                </div>
              ` : ''}
              ${item.notes ? `
                <div class="item-notes">
                  Notes: ${item.notes}
                </div>
              ` : ''}
            </div>
          `).join('') || '<div style="text-align: center; color: #666; padding: 10px;">No items found</div>'}
        </div>
        
        <!-- Bill Summary -->
        <div class="bill-summary">
          <div class="summary-row">
            <div class="summary-label">Order Subtotal</div>
            <div class="summary-value">${formatCurrency(previousBill)}</div>
          </div>
          
          ${discountAmount > 0 ? `
          <div class="summary-row">
            <div class="summary-label">Discount</div>
            <div class="summary-value">-${formatCurrency(discountAmount)}</div>
          </div>
          ` : ''}
          
          ${order.order_type === 'delivery' && deliveryCharge > 0 ? `
          <div class="summary-row">
            <div class="summary-label">Delivery Fee</div>
            <div class="summary-value">${formatCurrency(deliveryCharge)}</div>
          </div>
          ` : ''}
          
          <div class="summary-row" style="margin-top: 6px;">
            <div class="summary-label">Net Total Without VAT</div>
            <div class="summary-value">${formatCurrency(netTotalWithoutVAT)}</div>
          </div>
          
          <div class="summary-row">
            <div class="summary-label">VAT 5%</div>
            <div class="summary-value">${formatCurrency(vatAmount)}</div>
          </div>
          
          <div class="summary-row total">
            <div class="summary-label">TOTAL</div>
            <div class="summary-value">${formatCurrency(totalBill)}</div>
          </div>
          
          <div class="vat-note">
            (All items are inclusive of 5% VAT)
          </div>
        </div>
        
        <!-- Payment Method -->
        <div class="payment-method">
          ${order.status === 'rejected' ? 'REJECTED' :
            order.payment_method === 'card' ? 'CARD' :
            order.payment_method === 'cash' ? 'CASH' : 'PAID'}
        </div>
        
        ${order.additional_notes ? `
        <!-- Notes Section -->
        <div class="notes-section">
          <span class="notes-label">Notes:</span> ${order.additional_notes}
        </div>
        ` : ''}
        
        <!-- Footer -->
        <div class="footer">
          Thank you for choosing Cravin!<br/>
          Have a great day!
        </div>
      </div>
    </body>
    </html>
  `;
};

export const PrintBill: React.FC<PrintBillProps> = ({ order, businessInfo }) => {
  return null; // This component is only used for generating HTML
}; 