import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
    View,
    ScrollView,
    TouchableOpacity,
    RefreshControl,
    Alert,
    ActivityIndicator,
    Dimensions,
    FlatList,
    Modal,
} from 'react-native';
import { Text } from '~/components/ui/text';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import {
    useOptimizedNewOrders,
    useAcceptOrder,
    useRejectOrder,
} from '~/lib/hooks/useOrders';
import { useAuth } from '~/lib/hooks/useAuth';
import { Order } from '~/lib/api/orders';
import { useColorScheme } from '~/lib/useColorScheme';
import { OrdersHeader } from '~/components/food/OrdersHeader';
import { X } from 'lucide-react-native';
import { RejectReasonModal } from '~/components/food/RejectReasonModal';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isTablet = screenWidth >= 768;

// Food theme colors with dark mode support
const getFoodTheme = (isDark: boolean) => ({
    primary: '#ff2b2b',
    primaryBg: isDark ? 'bg-red-600 dark:bg-red-600' : 'bg-red-500',
    primaryDark: isDark ? 'bg-red-700 dark:bg-red-700' : 'bg-red-600',
    primaryLight: isDark ? 'bg-red-900/20 dark:bg-red-900/20' : 'bg-red-50',
    primaryBorder: isDark ? 'border-red-500 dark:border-red-500' : 'border-red-400',
    primaryText: isDark ? 'text-red-400 dark:text-red-400' : 'text-red-600',
    primaryTextDark: isDark ? 'text-red-300 dark:text-red-300' : 'text-red-700',
    accent: '#ff2b2b',
});

function NewOrdersScreenContent() {
    // Authentication
    const { session } = useAuth();
    const user = session?.user;

    // State
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
    const [showDetails, setShowDetails] = useState(false);
    const [showRejectModal, setShowRejectModal] = useState(false);
    const [orderToReject, setOrderToReject] = useState<string | null>(null);
    const [processingOrderId, setProcessingOrderId] = useState<string | null>(null);

    const { isDarkColorScheme } = useColorScheme();
    const foodTheme = getFoodTheme(isDarkColorScheme);

    // Data-fetching hooks with optimized 10-second background sync
    const {
        data: newOrders = [],
        isLoading: isLoadingNew,
        error: newOrdersError,
        refetch: refetchNewOrders,
    } = useOptimizedNewOrders();
 
    // Mutation hooks
    const acceptOrderMutation = useAcceptOrder();
    const rejectOrderMutation = useRejectOrder();

    // Clear processing state when mutations complete
    useEffect(() => {
        if (!acceptOrderMutation.isPending && !rejectOrderMutation.isPending) {
            setProcessingOrderId(null);
        }
    }, [acceptOrderMutation.isPending, rejectOrderMutation.isPending]);

    const formatTimeAgo = (dateString: string) => {
        const now = new Date();
        const orderTime = new Date(dateString);
        const diffMs = now.getTime() - orderTime.getTime();
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        const diffHours = Math.floor(diffMins / 60);
        if (diffHours < 24) return `${diffHours}h ago`;
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}d ago`;
    };

    const formatOrderTime = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    const getStatusColor = (status: string) => {
        return {
            bg: isDarkColorScheme ? 'bg-amber-900/30 dark:bg-amber-900/30' : 'bg-amber-100',
            text: isDarkColorScheme ? 'text-amber-400 dark:text-amber-400' : 'text-amber-800',
            dot: isDarkColorScheme ? 'bg-amber-400 dark:bg-amber-400' : 'bg-amber-500'
        };
    };

    // Action Handlers
    const handleAcceptOrder = useCallback((orderId: string) => {
        setProcessingOrderId(orderId);
        acceptOrderMutation.mutate(orderId);
    }, [acceptOrderMutation]);

    const handleRejectOrder = useCallback((orderId: string) => {
        setOrderToReject(orderId);
        setShowRejectModal(true);
    }, []);

    const handleRejectConfirm = useCallback((rejectedReason: string) => {
        if (orderToReject) {
            setProcessingOrderId(orderToReject);
            rejectOrderMutation.mutate({ 
                order_id: orderToReject, 
                rejected_reason: rejectedReason 
            });
            setOrderToReject(null);
        }
        setShowRejectModal(false);
    }, [orderToReject, rejectOrderMutation]);

    const handleRejectCancel = useCallback(() => {
        setShowRejectModal(false);
        setOrderToReject(null);
    }, []);

    const onRefresh = useCallback(() => {
        refetchNewOrders();
    }, [refetchNewOrders]);

    const handleOrderPress = useCallback((order: Order) => {
        setSelectedOrder(order);
        setShowDetails(true);
    }, []);

    const handleBackPress = useCallback(() => {
        setShowDetails(false);
        setSelectedOrder(null);
    }, []);

    // Render Functions
    const renderLoadingSpinner = () => (
        <View className="flex-1 items-center justify-center py-16">
            <ActivityIndicator size="large" color={foodTheme.primary} />
            <Text className="mt-4 text-base text-muted-foreground font-medium">Loading new orders...</Text>
        </View>
    );

    const renderError = (error: Error | null) => {
        if (!error) return null;
        return (
            <View className="flex-1 items-center justify-center py-16 px-6">
                <View className={`w-16 h-16 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-4`}>
                    <Text className="text-2xl">❌</Text>
                </View>
                <Text className={`${foodTheme.primaryText} text-center mb-6 text-base font-medium`}>
                    Failed to load new orders
                </Text>
                <Text className="text-muted-foreground text-center mb-6 text-sm">{error.message}</Text>
                <Button onPress={onRefresh} className={foodTheme.primaryDark}>
                    <Text className="text-white font-semibold">Try Again</Text>
                </Button>
            </View>
        );
    };

    const renderOrderCard = useCallback((order: Order) => {
        const isCurrentOrderProcessing = processingOrderId === order.order_id;
        const isAnyOrderProcessing = processingOrderId !== null;

        const statusColors = getStatusColor(order.status);
        const timeAgo = formatTimeAgo(order.ordered_on);
        const orderTime = formatOrderTime(order.ordered_on);
        const totalBill = ((order as any).bill?.total_bill || order.bill_amount?.total_bill || 0).toFixed(2);

        return (
            <TouchableOpacity 
                className="mb-4"
                onPress={() => handleOrderPress(order)}
                disabled={isAnyOrderProcessing}
                activeOpacity={0.7}
            >
                <Card className="shadow-lg border-0 bg-card">
                    <CardContent className="p-4">
                        {/* Header */}
                        <View className="flex-row items-start justify-between mb-3">
                            <View className="flex-row items-start flex-1 mr-3">
                                <View className={`w-12 h-12 ${order.order_type === 'delivery'
                                        ? (isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100')
                                        : (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                                    } rounded-xl mr-3 items-center justify-center`}>
                                    <Text className="text-xl">
                                        {order.order_type === 'delivery' ? '🚚' : '🍽️'}
                                    </Text>
                                </View>
                                <View className="flex-1">
                                    <Text className="font-bold text-lg text-foreground" numberOfLines={2}>
                                        {(order as any).receiver_name || order.customer_name}
                                    </Text>
                                    <View className="flex-row items-center mt-1 mb-1">
                                        <View className={`w-2 h-2 ${statusColors.dot} rounded-full mr-2`} />
                                        <Text className={`text-sm font-medium ${statusColors.text} uppercase tracking-wide`}>
                                            {order.status}
                                        </Text>
                                    </View>
                                    {(order as any).receiver_name && order.customer_name !== (order as any).receiver_name && (
                                        <Text className="text-xs text-muted-foreground" numberOfLines={1}>
                                            Ordered by: {order.customer_name}
                                        </Text>
                                    )}
                                </View>
                            </View>
                            <View className="items-end">
                                <Text className="font-bold text-xl text-foreground">
                                    AED {totalBill}
                                </Text>
                                {order.order_type === 'delivery' && ((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge) > 0 && (
                                    <Text className="text-xs text-muted-foreground">
                                        +AED {((order as any).bill?.delivery_charge || order.bill_amount?.delivery_charge || 0).toFixed(2)} delivery
                                    </Text>
                                )}
                                <Text className="text-sm text-muted-foreground mt-1">
                                    {orderTime}
                                </Text>
                            </View>
                        </View>

                        {/* Order Info */}
                        <View className="flex-row items-center justify-between mb-3">
                            <View className="flex-row items-center space-x-3">
                                <View className={`px-3 py-1.5 ${order.order_type === 'delivery'
                                        ? (isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100')
                                        : (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                                    } rounded-full`}>
                                    <Text className={`text-xs font-bold uppercase tracking-wide ${order.order_type === 'delivery'
                                            ? (isDarkColorScheme ? 'text-red-300' : 'text-red-700')
                                            : (isDarkColorScheme ? 'text-red-400' : 'text-red-600')
                                        }`}>
                                        {order.order_type}
                                    </Text>
                                </View>
                                
                                <View className="flex-row items-center mr-3 ml-3">
                                    <View className={`w-2 h-2 ${isDarkColorScheme ? 'bg-gray-500' : 'bg-gray-400'} rounded-full mr-2`} />
                                    <Text className="text-sm text-muted-foreground">
                                        {order.cart_items?.length || 0} item{(order.cart_items?.length || 0) !== 1 ? 's' : ''}
                                    </Text>
                                </View>
                                
                                <View className="flex-row items-center">
                                    <View className={`w-2 h-2 ${isDarkColorScheme ? 'bg-blue-500' : 'bg-blue-400'} rounded-full mr-2`} />
                                    <Text className="text-xs text-muted-foreground uppercase tracking-wide">
                                        {timeAgo}
                                    </Text>
                                </View>
                            </View>
                            
                            <Text className={`text-sm font-bold ${foodTheme.primaryText}`}>
                                #{order.order_name}
                            </Text>
                        </View>

                        {/* Action Buttons */}
                        <View onStartShouldSetResponder={() => true} className="flex-row gap-3">
                            <Button
                                variant="outline"
                                size="lg"
                                className={`flex-1 ${isDarkColorScheme
                                        ? 'border-red-600 bg-red-900/20'
                                        : 'border-red-200 bg-red-50'
                                    }`}
                                onPress={() => handleRejectOrder(order.order_id)}
                                disabled={isAnyOrderProcessing}
                            >
                                <Text className={`${foodTheme.primaryText} font-semibold`}>
                                    {isCurrentOrderProcessing && rejectOrderMutation.isPending ? 'Rejecting...' : 'Reject'}
                                </Text>
                            </Button>
                            <Button
                                size="lg"
                                className={`flex-1 ${foodTheme.primaryDark}`}
                                onPress={() => handleAcceptOrder(order.order_id)}
                                disabled={isAnyOrderProcessing}
                            >
                                <Text className="text-white font-semibold">
                                    {isCurrentOrderProcessing && acceptOrderMutation.isPending ? 'Accepting...' : 'Accept'}
                                </Text>
                            </Button>
                        </View>
                    </CardContent>
                </Card>
            </TouchableOpacity>
        );
    }, [processingOrderId, acceptOrderMutation.isPending, rejectOrderMutation.isPending, isDarkColorScheme, foodTheme, handleOrderPress, handleRejectOrder, handleAcceptOrder]);

    const renderFlatListItem = useCallback(({ item }: { item: Order }) => {
        return renderOrderCard(item);
    }, [renderOrderCard]);

    const renderOrderDetails = (inModal = false) => {
        if (!selectedOrder) {
            return (
                <View className="flex-1 items-center justify-center p-8">
                    <View className={`w-20 h-20 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
                        <Text className="text-3xl">🍽️</Text>
                    </View>
                    <Text className="text-foreground text-center text-lg font-medium mb-2">
                        No Order Selected
                    </Text>
                    <Text className="text-muted-foreground text-center">
                        Select an order from the list to view its details
                    </Text>
                </View>
            );
        }

        const statusColors = getStatusColor(selectedOrder.status);
        const timeAgo = formatTimeAgo(selectedOrder.ordered_on);
        const orderTime = formatOrderTime(selectedOrder.ordered_on);
        const totalBill = ((selectedOrder as any).bill?.total_bill || selectedOrder.bill_amount?.total_bill || 0).toFixed(2);
        const deliveryFee = ((selectedOrder as any).bill?.delivery_charge || selectedOrder.bill_amount?.delivery_charge || 0).toFixed(2);
        const subTotal = ((selectedOrder as any).bill?.sub_total || selectedOrder.bill_amount?.sub_total || 0).toFixed(2);
        const isCurrentOrderProcessing = processingOrderId === selectedOrder.order_id;

        return (
            <View style={{ maxHeight: inModal ? screenHeight * 0.85 : undefined }}>
                {inModal && (
                    <View className="p-4 border-b border-border flex-row items-center justify-between bg-card">
                        <Text className="text-xl font-bold text-foreground">Order Details</Text>
                        <TouchableOpacity onPress={handleBackPress} className="p-2 rounded-full bg-muted">
                            <X size={20} className="text-muted-foreground" />
                        </TouchableOpacity>
                    </View>
                )}
                <ScrollView showsVerticalScrollIndicator={false}>
                    {!isTablet && !inModal && (
                        <View className={`flex-row items-center p-4 ${foodTheme.primaryBg} ${isDarkColorScheme ? 'border-red-700' : 'border-red-300'
                            } border-b`}>
                            <TouchableOpacity
                                onPress={handleBackPress}
                                className="w-10 h-10 bg-white/20 rounded-full items-center justify-center mr-4"
                            >
                                <Text className="text-lg text-white">←</Text>
                            </TouchableOpacity>
                            <Text className="text-xl font-bold text-white">Order Details</Text>
                        </View>
                    )}

                    <View className="p-6">
                        {/* Order Header */}
                        <Card className="shadow-xl border-0 bg-card mb-6">
                            <CardContent className="p-6">
                                <View className="flex-row items-center justify-between mb-6">
                                    <View className="flex-1">
                                        <Text className="text-3xl font-bold text-foreground mb-2">
                                            #{selectedOrder.order_name}
                                        </Text>
                                        <View className={`flex-row items-center p-3 ${statusColors.bg} rounded-lg mb-3`}>
                                            <View className={`w-3 h-3 ${statusColors.dot} rounded-full mr-3`} />
                                            <Text className={`text-sm font-bold ${statusColors.text} uppercase tracking-wide`}>
                                                {selectedOrder.status}
                                            </Text>
                                        </View>
                                        <View className="flex-row items-center space-x-4">
                                            <View className={`px-3 py-2 ${selectedOrder.order_type === 'delivery'
                                                    ? (isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100')
                                                    : (isDarkColorScheme ? 'bg-red-900/20' : 'bg-red-50')
                                                } rounded-lg`}>
                                                <Text className={`text-xs font-bold uppercase tracking-wide ${selectedOrder.order_type === 'delivery'
                                                        ? (isDarkColorScheme ? 'text-red-300' : 'text-red-700')
                                                        : (isDarkColorScheme ? 'text-red-400' : 'text-red-600')
                                                    }`}>
                                                    {selectedOrder.order_type}
                                                </Text>
                                            </View>
                                            <Text className="text-sm text-muted-foreground">
                                                {selectedOrder.cart_items?.length || 0} item{(selectedOrder.cart_items?.length || 0) !== 1 ? 's' : ''}
                                            </Text>
                                        </View>
                                    </View>
                                    <View className="items-end">
                                        <Text className="text-4xl font-bold text-foreground mb-1">
                                            AED {totalBill}
                                        </Text>
                                        <Text className="text-sm text-muted-foreground">
                                            {orderTime}
                                        </Text>
                                        <Text className="text-xs text-muted-foreground mt-1">
                                            {timeAgo}
                                        </Text>
                                    </View>
                                </View>

                                {/* Action Buttons */}
                                <View className="border-t border-border pt-4">
                                    <View className="flex-row gap-3">
                                        <Button
                                            variant="outline"
                                            size="lg"
                                            className={`flex-1 ${isDarkColorScheme
                                                    ? 'border-red-600 bg-red-900/20'
                                                    : 'border-red-200 bg-red-50'
                                                }`}
                                            onPress={() => {handleRejectOrder(selectedOrder.order_id)
                                                handleBackPress()
                                            }}
                                            disabled={processingOrderId !== null}
                                        >
                                            <Text className={`${foodTheme.primaryText} font-semibold text-base`}>
                                                {isCurrentOrderProcessing && rejectOrderMutation.isPending ? 'Rejecting...' : 'Reject Order'}
                                            </Text>
                                        </Button>
                                        <Button
                                            size="lg"
                                            className={`flex-1 ${foodTheme.primaryDark}`}
                                            onPress={() => {handleAcceptOrder(selectedOrder.order_id)
                                                handleBackPress()
                                            }}
                                            disabled={processingOrderId !== null}
                                        >
                                            <Text className="text-white font-semibold text-base">
                                                {isCurrentOrderProcessing && acceptOrderMutation.isPending ? 'Accepting...' : '✓ Accept Order'}
                                            </Text>
                                        </Button>
                                    </View>
                                </View>
                            </CardContent>
                        </Card>

                        {/* Customer Information */}
                        <Card className="shadow-xl border-0 bg-card mb-6">
                            <CardContent className="p-6">
                                <View className="flex-row items-center mb-6">
                                    <View className={`w-12 h-12 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-xl items-center justify-center mr-4`}>
                                        <Text className="text-2xl">👤</Text>
                                    </View>
                                    <Text className="text-xl font-bold text-foreground">Customer Information</Text>
                                </View>
                                
                                <View className="space-y-4">
                                    <View className={`p-4 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-xl`}>
                                        <Text className="text-xs text-muted-foreground uppercase tracking-wider mb-1">
                                            {selectedOrder.order_type === 'delivery' ? 'Receiver Name' : 'Customer Name'}
                                        </Text>
                                        <Text className="text-lg font-bold text-foreground">
                                            {(selectedOrder as any).receiver_name || selectedOrder.customer_name}
                                        </Text>
                                        {(selectedOrder as any).receiver_name && selectedOrder.customer_name !== (selectedOrder as any).receiver_name && (
                                            <Text className="text-sm text-muted-foreground mt-1">
                                                Ordered by: {selectedOrder.customer_name}
                                            </Text>
                                        )}
                                    </View>

                                    <View className={`p-4 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-xl`}>
                                        <Text className="text-xs text-muted-foreground uppercase tracking-wider mb-1">
                                            {selectedOrder.order_type === 'delivery' ? 'Receiver Phone' : 'Phone Number'}
                                        </Text>
                                        <Text className="text-lg font-semibold text-foreground">
                                            {(selectedOrder as any).receiver_number || selectedOrder.customer_number}
                                        </Text>
                                    </View>

                                    {selectedOrder.address && (
                                        <View className={`p-4 ${isDarkColorScheme ? 'bg-gray-800/50' : 'bg-gray-50'} rounded-xl`}>
                                            <Text className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Delivery Address</Text>
                                            <Text className="text-base font-semibold text-foreground leading-relaxed">
                                                {selectedOrder.address}
                                            </Text>
                                        </View>
                                    )}

                                    {/* Order Timing Information */}
                                    <View className={`p-4 ${isDarkColorScheme ? 'bg-blue-900/20' : 'bg-blue-50'} rounded-xl border ${isDarkColorScheme ? 'border-blue-800' : 'border-blue-200'}`}>
                                        <Text className="text-xs text-muted-foreground uppercase tracking-wider mb-2">Order Timing</Text>
                                        <View className="flex-row items-center justify-between">
                                            <View>
                                                <Text className="text-sm text-muted-foreground">Ordered at</Text>
                                                <Text className="text-base font-bold text-foreground">{orderTime}</Text>
                                            </View>
                                            <View className="items-end">
                                                <Text className="text-sm text-muted-foreground">Time ago</Text>
                                                <Text className="text-base font-bold text-foreground">{timeAgo}</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </CardContent>
                        </Card>

                        {/* Order Items */}
                        <Card className="shadow-xl border-0 bg-card">
                            <CardContent className="p-6">
                                <View className="flex-row items-center justify-between mb-6">
                                    <View className="flex-row items-center">
                                        <View className={`w-12 h-12 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-xl items-center justify-center mr-4`}>
                                            <Text className="text-2xl">🍽️</Text>
                                        </View>
                                        <Text className="text-xl font-bold text-foreground">
                                            Order Items
                                        </Text>
                                    </View>
                                    <View className={`px-4 py-2 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full`}>
                                        <Text className={`text-sm font-bold ${isDarkColorScheme ? 'text-red-300' : 'text-red-700'}`}>
                                            {selectedOrder?.cart_items?.length || 0} items
                                        </Text>
                                    </View>
                                </View>

                                <View className="space-y-4">
                                    {selectedOrder.cart_items?.map((item, index) => (
                                        <View key={index} className={`p-4 ${isDarkColorScheme ? 'bg-gray-800/30' : 'bg-gray-50'} rounded-xl border-l-4 ${isDarkColorScheme ? 'border-red-500' : 'border-red-400'}`}>
                                            <View className="flex-row items-start justify-between mb-2">
                                                <View className="flex-1 mr-4">
                                                    <Text className="text-lg font-bold text-foreground mb-2">
                                                        {item.name}
                                                    </Text>
                                                    <View className="flex-row items-center space-x-6">
                                                        <View className={`px-3 py-1 ${isDarkColorScheme ? 'bg-blue-900/30' : 'bg-blue-100'} rounded-full`}>
                                                            <Text className={`text-sm font-semibold ${isDarkColorScheme ? 'text-blue-300' : 'text-blue-700'}`}>
                                                                Qty: {item.quantity}
                                                            </Text>
                                                        </View>
                                                        <Text className="text-sm text-muted-foreground">
                                                            AED {item.price.toFixed(2)} each
                                                        </Text>
                                                    </View>
                                                    {item.notes && (
                                                        <View className={`${isDarkColorScheme ? 'bg-yellow-900/20' : 'bg-yellow-50'} rounded-lg p-3 mt-3 border-l-2 ${isDarkColorScheme ? 'border-yellow-600' : 'border-yellow-400'}`}>
                                                            <Text className="text-xs text-muted-foreground uppercase tracking-wider mb-1">Special Instructions</Text>
                                                            <Text className="text-sm font-medium text-foreground">
                                                                {item.notes}
                                                            </Text>
                                                        </View>
                                                    )}
                                                </View>
                                                <View className="items-end">
                                                    <Text className="text-xl font-bold text-foreground">
                                                        AED {(item.quantity * item.price).toFixed(2)}
                                                    </Text>
                                                </View>
                                            </View>
                                        </View>
                                    )) || []}
                                </View>

                                {/* Order Summary */}
                                <View className={`mt-6 p-4 ${isDarkColorScheme ? 'bg-green-900/20' : 'bg-green-50'} rounded-xl border ${isDarkColorScheme ? 'border-green-800' : 'border-green-200'}`}>
                                    <Text className="text-lg font-bold text-foreground mb-3">Order Summary</Text>
                                    
                                    <View className="flex-row items-center justify-between mb-2">
                                        <Text className="text-sm text-muted-foreground">Subtotal</Text>
                                        <Text className="text-sm font-semibold text-foreground">AED {subTotal}</Text>
                                    </View>
                                    
                                    {selectedOrder.order_type === 'delivery' && parseFloat(deliveryFee) > 0 && (
                                        <View className="flex-row items-center justify-between mb-2">
                                            <Text className="text-sm text-muted-foreground">Delivery Fee</Text>
                                            <Text className="text-sm font-semibold text-foreground">AED {deliveryFee}</Text>
                                        </View>
                                    )}
                                    
                                    <View className="border-t border-border pt-2 mt-2">
                                        <View className="flex-row items-center justify-between">
                                            <Text className="text-lg font-bold text-foreground">Total Amount</Text>
                                            <Text className="text-2xl font-bold text-foreground">AED {totalBill}</Text>
                                        </View>
                                    </View>
                                </View>
                            </CardContent>
                        </Card>
                    </View>
                </ScrollView>
            </View>
        );
    };

    const renderEmptyList = useCallback(() => (
        <View className="flex-1 items-center justify-center py-16">
            <View className={`w-20 h-20 ${isDarkColorScheme ? 'bg-red-900/30' : 'bg-red-100'} rounded-full items-center justify-center mb-6`}>
                <Text className="text-3xl">🍽️</Text>
            </View>
            <Text className="text-foreground text-lg font-medium mb-2">No New Orders</Text>
            <Text className="text-muted-foreground text-center">New food orders will appear here</Text>
        </View>
    ), [isDarkColorScheme]);

    const keyExtractor = useCallback((item: Order) => item.order_id, []);

    const getItemLayout = useCallback((data: any, index: number) => ({
        length: 200, // Approximate height of each item
        offset: 200 * index,
        index,
    }), []);

    // Order Details Modal
    const OrderDetailsModal = () => (
        <Modal
            visible={showDetails}
            transparent={true}
            animationType="slide"
            onRequestClose={handleBackPress}
        >
            <View className="flex-1 bg-black/50 justify-end">
                <View className="bg-card rounded-t-3xl max-h-[90%]">
                    {renderOrderDetails(true)}
                </View>
            </View>
        </Modal>
    );

    return (
        <View className="flex-1 bg-background">
            {/* Header */}
            <OrdersHeader
                title="New Orders"
            />
            <View className={`flex-1 ${isTablet ? 'flex-row' : ''}`}>
                {/* Order List */}
                <View className={`${isTablet ? 'w-1/2' : 'flex-1'} bg-card ${isTablet ? 'border-r border-border' : ''}`}>
                    {isLoadingNew ? (
                        renderLoadingSpinner()
                    ) : newOrdersError ? (
                        renderError(newOrdersError)
                    ) : (
                        <FlatList
                            data={newOrders}
                            renderItem={renderFlatListItem}
                            keyExtractor={keyExtractor}
                            refreshControl={
                                <RefreshControl
                                    refreshing={isLoadingNew}
                                    onRefresh={onRefresh}
                                    tintColor={foodTheme.primary}
                                />
                            }
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{ paddingHorizontal: 16, paddingTop: 16, paddingBottom: 16 }}
                            ListEmptyComponent={renderEmptyList}
                            initialNumToRender={10}
                            maxToRenderPerBatch={5}
                            windowSize={10}
                            removeClippedSubviews={true}
                            getItemLayout={getItemLayout}
                        />
                    )}
                </View>

                {/* Order Details (Tablet Only) */}
                {isTablet && (
                    <View className="flex-1 bg-background">
                        {renderOrderDetails()}
                    </View>
                )}
            </View>

            {/* Order Details Modal */}
            <OrderDetailsModal />

            {/* Reject Reason Modal */}
            <RejectReasonModal
                isVisible={showRejectModal}
                onClose={handleRejectCancel}
                onConfirm={handleRejectConfirm}
                isLoading={rejectOrderMutation.isPending}
            />
        </View>
    );
}

export default function NewOrdersScreen() {
    return <NewOrdersScreenContent />;
} 