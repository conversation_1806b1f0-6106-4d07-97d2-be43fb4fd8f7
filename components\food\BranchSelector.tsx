import React, { useState, useMemo, useCallback } from 'react';
import { View, TouchableOpacity, Modal, FlatList, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '~/components/ui/text';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { ChevronDown } from 'lucide-react-native';
import { useColorScheme } from '~/lib/useColorScheme';

type Branch = {
  branch_id: string;
  branch_name: string;
};

type BranchSelectorProps = {
  branches: Branch[];
  selectedBranchId: string | null;
  onSelectBranch: (branchId: string | null) => void;
};

export const BranchSelector = React.memo(function BranchSelector({ branches, selectedBranchId, onSelectBranch }: BranchSelectorProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const { isDarkColorScheme } = useColorScheme();

  const selectedBranch = useMemo(() => 
    branches.find(b => b.branch_id === selectedBranchId), 
    [branches, selectedBranchId]
  );

  const handleSelect = useCallback((branchId: string | null) => {
    onSelectBranch(branchId);
    setModalVisible(false);
  }, [onSelectBranch]);

  const theme = useMemo(() => ({
    primary: '#ff2b2b',
    primaryBg: isDarkColorScheme ? 'bg-red-600' : 'bg-red-500',
    primaryText: isDarkColorScheme ? 'text-red-400' : 'text-red-600',
  }), [isDarkColorScheme]);

  const flatListData = useMemo(() => 
    [{ branch_id: 'all', branch_name: 'All Branches' }, ...branches], 
    [branches]
  );

  const renderItem = useCallback(({ item }: { item: Branch & { branch_id: string } }) => (
    <TouchableOpacity
      onPress={() => handleSelect(item.branch_id === 'all' ? null : item.branch_id)}
      className={`p-4 ${item.branch_id === (selectedBranchId || 'all') ? theme.primaryBg.replace('bg-','') : ''}`}
    >
      <Text className={`text-base text-center ${item.branch_id === (selectedBranchId || 'all') ? 'text-white' : 'text-foreground'}`}>
        {item.branch_name}
      </Text>
    </TouchableOpacity>
  ), [handleSelect, selectedBranchId, theme.primaryBg]);

  const ItemSeparator = useCallback(() => <View className="h-px bg-border" />, []);

  return (
    <View>
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        className="flex-row items-center justify-between w-full p-3 bg-card border border-border rounded-lg"
      >
        <Text className="text-base">
          {selectedBranch ? selectedBranch.branch_name : 'All Branches'}
        </Text>
        <ChevronDown size={20} className="text-muted-foreground" />
      </TouchableOpacity>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <Pressable
          className="flex-1 justify-end bg-black/60"
          onPress={() => setModalVisible(false)}
        >
          <SafeAreaView>
            <Card className="rounded-t-2xl">
              <View className="p-4 border-b border-border">
                <Text className="text-lg font-bold text-center">Select a Branch</Text>
              </View>
              <FlatList
                data={flatListData}
                keyExtractor={(item) => item.branch_id}
                renderItem={renderItem}
                ItemSeparatorComponent={ItemSeparator}
              />
               <Button
                  className="m-4 bg-muted"
                  onPress={() => setModalVisible(false)}
                >
                  <Text>Cancel</Text>
                </Button>
            </Card>
          </SafeAreaView>
        </Pressable>
      </Modal>
    </View>
  );
}); 