import * as React from 'react';
import {
    View,
    Pressable,
    KeyboardAvoidingView,
    ScrollView,
    Platform,
    Keyboard,
    Image,
    StyleSheet,
    Dimensions,
    Alert,
} from 'react-native';
import Animated, {
    useSharedValue,
    useAnimatedStyle,
    withTiming,
    withSpring,
    withDelay,
    interpolate,
    Extrapolate,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { EyeOff, Eye, Lock, User, Check, Building2, UtensilsCrossed, ShoppingBag } from 'lucide-react-native';
import { Button } from '~/components/ui/button';
import { Text } from '~/components/ui/text';
import { Input } from '~/components/ui/input';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuthStore } from '~/lib/stores/auth-store';
import { loginMerchant } from '~/lib/api/auth';
import { authStorage } from '~/lib/utils/auth-storage';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

type MerchantType = 'sports' | 'food' | 'commerce';

export default function Login() {
    const [username, setUsername] = React.useState('');
    const [password, setPassword] = React.useState('');
    const [showPassword, setShowPassword] = React.useState(false);
    const [focusedField, setFocusedField] = React.useState<string | null>(null);
    const [selectedMerchant, setSelectedMerchant] = React.useState<MerchantType>('food');
    const [isLoading, setIsLoading] = React.useState(false);
    const [keyboardVisible, setKeyboardVisible] = React.useState(false);
    const insets = useSafeAreaInsets();

    // Animation values
    const fadeIn = useSharedValue(0);
    const slideUp = useSharedValue(30);
    const logoScale = useSharedValue(0.8);
    const merchantSelectorOpacity = useSharedValue(0);
    const formOpacity = useSharedValue(0);
    const buttonScale = useSharedValue(1);

    const setUser = useAuthStore(state => state.setUser);

    React.useEffect(() => {
        // Staggered animations for a more premium feel
        fadeIn.value = withTiming(1, { duration: 1000 });
        slideUp.value = withSpring(0, { damping: 15, stiffness: 100 });
        logoScale.value = withDelay(200, withSpring(1, { damping: 12, stiffness: 150 }));
        merchantSelectorOpacity.value = withDelay(400, withTiming(1, { duration: 800 }));
        formOpacity.value = withDelay(600, withTiming(1, { duration: 800 }));

        // Keyboard event listeners
        const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
            setKeyboardVisible(true);
        });
        const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
            setKeyboardVisible(false);
        });

        return () => {
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    const containerStyle = useAnimatedStyle(() => ({
        opacity: fadeIn.value,
        transform: [{ translateY: slideUp.value }]
    }));

    const logoAnimatedStyle = useAnimatedStyle(() => ({
        transform: [{ scale: logoScale.value }]
    }));

    const merchantSelectorStyle = useAnimatedStyle(() => ({
        opacity: merchantSelectorOpacity.value,
        transform: [{
            translateY: interpolate(
                merchantSelectorOpacity.value,
                [0, 1],
                [20, 0],
                Extrapolate.CLAMP
            )
        }]
    }));

    const formAnimatedStyle = useAnimatedStyle(() => ({
        opacity: formOpacity.value,
        transform: [{
            translateY: interpolate(
                formOpacity.value,
                [0, 1],
                [20, 0],
                Extrapolate.CLAMP
            )
        }]
    }));

    const buttonAnimatedStyle = useAnimatedStyle(() => ({
        transform: [{ scale: buttonScale.value }]
    }));

    // Enhanced color themes with gradients
    const getTheme = (merchant: MerchantType) => {
        switch (merchant) {
            case 'sports':
                return {
                    gradient: ['#10b981', '#059669'] as const,
                    backgroundGradient: ['#ecfdf5', '#d1fae5'] as const,
                    accent: '#10b981',
                    accentLight: '#a7f3d0',
                    shadow: 'rgba(16, 185, 129, 0.25)',
                    icon: Building2
                };
            case 'food':
                return {
                    gradient: ['#e11d48', '#be123c'] as const,
                    backgroundGradient: ['#fff1f2', '#ffe4e6'] as const,
                    accent: '#e11d48',
                    accentLight: '#fecdd3',
                    shadow: 'rgba(225, 29, 72, 0.25)',
                    icon: UtensilsCrossed
                };
            case 'commerce':
                return {
                    gradient: ['#0ea5e9', '#0284c7'] as const,
                    backgroundGradient: ['#f0f9ff', '#e0f2fe'] as const,
                    accent: '#0ea5e9',
                    accentLight: '#bae6fd',
                    shadow: 'rgba(14, 165, 233, 0.25)',
                    icon: ShoppingBag
                };
        }
    };

    const getLogoSource = (merchant: MerchantType) => {
        switch (merchant) {
            case 'sports':
                return require('../assets/images/cravin-logo.png');
            case 'food':
                return require('../assets/images/CravinFood-logo.webp');
            case 'commerce':
                return require('../assets/images/CravinCommerce-logo.webp');
        }
    };

    const currentTheme = getTheme(selectedMerchant);

    const handleLogin = async () => {
        setIsLoading(true);
        buttonScale.value = withSpring(0.95, { damping: 15 });

        try {
            if (selectedMerchant !== 'food') {
                Alert.alert('Coming Soon', 'Only Food merchant login is supported at the moment.');
                return;
            }

            const data = await loginMerchant({ username, password });

            const userSession = {
                access_token: data.access_token,
                user_id: data.user_id,
                username: data.username,
                restaurant_id: data.restaurant_id,
                branch_ids: data.branches.map((b) => ({ branch_id: b.branch_id, branch_name: b.branch_name })),
                user_type: data.user_type,
            };

            // Store auth data securely
            await authStorage.setAuthToken(data.access_token);
            await authStorage.setUserSession(userSession);

            // Update Zustand store
            setUser(userSession);

            // Navigate to the tabs screen after successful login
            router.replace('/(tabs)');
        } catch (error: any) {
            Alert.alert('Login Failed', error.message || 'Unable to login');
        } finally {
            buttonScale.value = withSpring(1);
            setIsLoading(false);
        }
    };

    const handleMerchantSelect = (merchant: MerchantType) => {
        setSelectedMerchant(merchant);
    };

    return (
        <View style={{ flex: 1 }}>
            <StatusBar style="dark" />

            {/* Animated Background */}
            <LinearGradient
                colors={currentTheme.backgroundGradient}
                style={StyleSheet.absoluteFillObject}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
            />

            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : undefined}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
            >
                <View
                    style={{
                        flex: 1,
                        paddingTop: insets.top + 20,
                        paddingBottom: insets.bottom,
                        paddingLeft: insets.left + 24,
                        paddingRight: insets.right + 24,
                    }}
                >
                    <ScrollView
                        contentContainerStyle={{ 
                            flexGrow: 1,
                            justifyContent: keyboardVisible ? 'flex-start' : 'center',
                            paddingBottom: keyboardVisible ? 20 : 0
                        }}
                        keyboardShouldPersistTaps="handled"
                        showsVerticalScrollIndicator={false}
                        bounces={false}
                        keyboardDismissMode="interactive"
                    >
                        <Pressable onPress={Keyboard.dismiss} style={{ minHeight: '100%' }}>
                            <Animated.View style={[
                                containerStyle, 
                                { 
                                    flex: 1, 
                                    justifyContent: 'center', 
                                    minHeight: keyboardVisible ? 'auto' : screenHeight * 0.8,
                                    paddingTop: keyboardVisible ? 20 : 0
                                }
                            ]}>

                                {/* Header Section */}
                                <View style={{ alignItems: 'center', marginBottom: 12 }}>
                                    <Animated.View style={[logoAnimatedStyle, { marginBottom: 32 }]}>
                                        <View style={styles.logoContainer}>
                                            <Image
                                                source={getLogoSource(selectedMerchant)}
                                                style={styles.logo}
                                                resizeMode="contain"
                                            />
                                        </View>
                                    </Animated.View>

                                </View>

                                {/* Merchant Selector */}
                                <Animated.View style={[merchantSelectorStyle, { marginBottom: 40 }]}>
                                    <Text style={styles.sectionLabel}>Select Merchant</Text>
                                    <View style={styles.merchantContainer}>
                                        {(['food', 'commerce', 'sports'] as MerchantType[]).map((merchant, index) => {
                                            const theme = getTheme(merchant);
                                            const IconComponent = theme.icon;
                                            const isSelected = selectedMerchant === merchant;

                                            return (
                                                <Pressable
                                                    key={merchant}
                                                    onPress={() => handleMerchantSelect(merchant)}
                                                    style={[
                                                        styles.merchantCard,
                                                        isSelected && {
                                                            borderColor: theme.accent,
                                                            // backgroundColor: theme.accentLight + '20',
                                                            shadowColor: theme.shadow,
                                                            shadowOffset: { width: 0, height: 8 },
                                                            shadowOpacity: 0.3,
                                                            shadowRadius: 16,
                                                            elevation: 12,
                                                        }
                                                    ]}
                                                // disabled={merchant !== 'food'}
                                                >
                                                    <View style={[
                                                        styles.merchantIconContainer,
                                                        isSelected && { backgroundColor: theme.accent },
                                                        // merchant !== 'food' && { opacity: 0.5 }
                                                    ]} >
                                                        <IconComponent
                                                            size={24}
                                                            color={isSelected ? '#fff' : theme.accent}
                                                        />
                                                    </View>
                                                    <Text style={[
                                                        styles.merchantLabel,
                                                        isSelected && { color: theme.accent, fontWeight: '700' },
                                                        // merchant !== 'food' && { opacity: 0.5 }
                                                    ]}>
                                                        {merchant.charAt(0).toUpperCase() + merchant.slice(1)}
                                                    </Text>
                                                    {isSelected && (
                                                        <View style={[styles.checkBadge, { backgroundColor: theme.accent }]}>
                                                            <Check size={14} color="#fff" />
                                                        </View>
                                                    )}
                                                </Pressable>
                                            );
                                        })}
                                    </View>
                                </Animated.View>

                                {/* Login Form */}
                                <Animated.View style={formAnimatedStyle}>
                                    <View style={styles.formContainer}>

                                        {/* Username Field */}
                                        <View style={{ marginBottom: 20 }}>
                                            <Text style={styles.fieldLabel}>Username</Text>
                                            <View style={styles.inputContainer}>
                                                <View style={[
                                                    styles.inputIconContainer,
                                                    // focusedField === 'username' && { backgroundColor: currentTheme.accentLight + '30' }
                                                ]}>
                                                    <User size={20} color={focusedField === 'username' ? currentTheme.accent : '#64748b'} />
                                                </View>
                                                <Input
                                                    value={username}
                                                    onChangeText={setUsername}
                                                    onFocus={() => setFocusedField('username')}
                                                    onBlur={() => setFocusedField(null)}
                                                    placeholder="Enter your username"
                                                    style={[
                                                        styles.input,
                                                        focusedField === 'username' && {
                                                            borderColor: currentTheme.accent,
                                                            backgroundColor: '#fff',
                                                            shadowColor: currentTheme.shadow,
                                                            shadowOffset: { width: 0, height: 4 },
                                                            shadowOpacity: 0.1,
                                                            shadowRadius: 8,
                                                            elevation: 4,
                                                        }
                                                    ]}
                                                    placeholderTextColor="#94a3b8"
                                                    autoCapitalize="none"
                                                    autoCorrect={false}
                                                />
                                            </View>
                                        </View>

                                        {/* Password Field */}
                                        <View style={{ marginBottom: 32 }}>
                                            <Text style={styles.fieldLabel}>Password</Text>
                                            <View style={styles.inputContainer}>
                                                <View style={[
                                                    styles.inputIconContainer,
                                                    // focusedField === 'password' && { backgroundColor: currentTheme.accentLight + '30' }
                                                ]}>
                                                    <Lock size={20} color={focusedField === 'password' ? currentTheme.accent : '#64748b'} />
                                                </View>
                                                <Input
                                                    value={password}
                                                    onChangeText={setPassword}
                                                    onFocus={() => setFocusedField('password')}
                                                    onBlur={() => setFocusedField(null)}
                                                    placeholder="Enter your password"
                                                    secureTextEntry={!showPassword}
                                                    style={[
                                                        styles.input,
                                                        { paddingRight: 56 },
                                                        focusedField === 'password' && {
                                                            borderColor: currentTheme.accent,
                                                            backgroundColor: '#fff',
                                                            shadowColor: currentTheme.shadow,
                                                            shadowOffset: { width: 0, height: 4 },
                                                            shadowOpacity: 0.1,
                                                            shadowRadius: 8,
                                                            elevation: 4,
                                                        }
                                                    ]}
                                                    placeholderTextColor="#94a3b8"
                                                    autoCapitalize="none"
                                                    autoCorrect={false}
                                                />
                                                <Pressable
                                                    onPress={() => setShowPassword(!showPassword)}
                                                    style={styles.passwordToggle}
                                                    hitSlop={12}
                                                >
                                                    {showPassword ? (
                                                        <EyeOff size={20} color="#94a3b8" />
                                                    ) : (
                                                        <Eye size={20} color="#94a3b8" />
                                                    )}
                                                </Pressable>
                                            </View>
                                        </View>

                                        {/* Login Button */}
                                        <Animated.View style={buttonAnimatedStyle}>
                                            <Pressable
                                                onPress={handleLogin}
                                                disabled={isLoading}
                                                style={({ pressed }) => [
                                                    styles.loginButton,
                                                    {
                                                        opacity: pressed ? 0.9 : 1,
                                                        shadowColor: currentTheme.shadow,
                                                    }
                                                ]}
                                            >
                                                <LinearGradient
                                                    colors={currentTheme.gradient}
                                                    style={styles.buttonGradient}
                                                    start={{ x: 0, y: 0 }}
                                                    end={{ x: 1, y: 0 }}
                                                >
                                                    <Text style={styles.loginButtonText}>
                                                        {isLoading ? 'Signing In...' : 'Sign In'}
                                                    </Text>
                                                </LinearGradient>
                                            </Pressable>
                                        </Animated.View>
                                    </View>
                                </Animated.View>

                                {/* Footer */}
                                <View style={styles.footer}>
                                    <Text style={styles.footerText}>
                                        Powered by Cravin
                                    </Text>
                                </View>
                            </Animated.View>
                        </Pressable>
                    </ScrollView>
                </View>
            </KeyboardAvoidingView>
        </View>
    );
}

const styles = StyleSheet.create({
    logoContainer: {
        width: 100,
        height: 100,
        borderRadius: 20,
        backgroundColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.1,
        shadowRadius: 16,
        elevation: 8,
    },
    logo: {
        width: 100,
        height: 100,
    },
    welcomeTitle: {
        fontSize: 32,
        fontWeight: '800',
        color: '#1e293b',
        marginBottom: 8,
        letterSpacing: -0.5,
    },
    welcomeSubtitle: {
        fontSize: 16,
        color: '#64748b',
        fontWeight: '500',
        textAlign: 'center',
        lineHeight: 24,
    },
    sectionLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#475569',
        marginBottom: 16,
        letterSpacing: 0.5,
        textTransform: 'uppercase',
    },
    merchantContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    merchantCard: {
        flex: 1,
        backgroundColor: '#fff',
        borderRadius: 16,
        borderWidth: 2,
        borderColor: '#e2e8f0',
        padding: 20,
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 2,
        position: 'relative',
    },
    merchantIconContainer: {
        width: 48,
        height: 48,
        borderRadius: 12,
        backgroundColor: '#f1f5f9',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 12,
    },
    merchantLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#475569',
        textAlign: 'center',
    },
    checkBadge: {
        position: 'absolute',
        top: -6,
        right: -6,
        width: 24,
        height: 24,
        borderRadius: 12,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: '#fff',
    },
    formContainer: {
        backgroundColor: '#fff',
        borderRadius: 24,
        padding: 32,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.08,
        shadowRadius: 24,
        elevation: 8,
    },
    fieldLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#374151',
        marginBottom: 8,
        marginLeft: 4,
    },
    inputContainer: {
        position: 'relative',
    },
    inputIconContainer: {
        position: 'absolute',
        left: 16,
        top: 16,
        zIndex: 1,
        width: 32,
        height: 32,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    input: {
        backgroundColor: '#f8fafc',
        borderWidth: 2,
        borderColor: '#e2e8f0',
        borderRadius: 16,
        paddingLeft: 64,
        paddingRight: 20,
        paddingVertical: 16,
        fontSize: 16,
        fontWeight: '500',
        color: '#1e293b',
        height: 64,
    },
    passwordToggle: {
        position: 'absolute',
        right: 20,
        top: 22,
        padding: 4,
    },
    loginButton: {
        borderRadius: 16,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.2,
        shadowRadius: 16,
        elevation: 8,
    },
    buttonGradient: {
        paddingVertical: 18,
        paddingHorizontal: 32,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 56,
    },
    loginButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '700',
        letterSpacing: 0.5,
    },
    footer: {
        alignItems: 'center',
        marginTop: 40,
    },
    footerText: {
        fontSize: 12,
        color: '#94a3b8',
        fontWeight: '500',
    },
});