import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create an optimized client for real-time order updates
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Optimized for 10-second background sync
      staleTime: 2 * 1000, // 2 seconds - aggressive stale time for real-time updates
      gcTime: 15 * 60 * 1000, // 15 minutes - longer cache time for better performance
      
      // Retry configuration optimized for background sync
      retry: (failureCount, error) => {
        // Don't retry authentication errors
        if (error instanceof Error) {
          if (error.message.includes('401') || error.message.includes('403')) {
            return false;
          }
          if (error.message.includes('4')) { // Other 4xx errors
            return failureCount < 1; // Only one retry for client errors
          }
        }
        // Retry up to 3 times for server errors and network issues
        return failureCount < 3;
      },
      
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 5000),
      
      // Background sync optimizations
      refetchOnWindowFocus: true, // Refetch when user returns to app
      refetchOnReconnect: true, // Refetch on network reconnect
      refetchOnMount: 'always', // Always refetch on component mount for fresh data
      
      // Performance optimizations
      notifyOnChangeProps: ['data', 'error', 'isLoading', 'isSuccess'],
      
      // Network mode for better offline handling
      networkMode: 'online',
    },
    mutations: {
      // Retry failed mutations once
      retry: 1,
      retryDelay: 1000,
      
      // Network mode for mutations
      networkMode: 'online',
    },
  },
});

// Configure global error handling for background sync
queryClient.setQueryDefaults(['orders'], {
  staleTime: 1000, // Very short stale time for order data
  gcTime: 5 * 60 * 1000, // 5 minutes cache for orders
  refetchInterval: 5000, // Frontend refetch every 5 seconds
});

// Configure background task queries
queryClient.setQueryDefaults(['background-task'], {
  staleTime: 30 * 1000, // 30 seconds stale time for background task status
  gcTime: 60 * 1000, // 1 minute cache for background task data
  retry: 2,
});

// Global error handler for background sync issues
queryClient.getQueryCache().subscribe((event) => {
  if (event.type === 'updated' && event.query.state.error) {
    const error = event.query.state.error;
    const queryKey = event.query.queryKey;
    
    // Log background sync errors for debugging
    if (queryKey.includes('orders') || queryKey.includes('background-task')) {
      console.warn('🚨 Background sync error:', {
        queryKey,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }
});

// Monitor cache updates for debugging
if (__DEV__) {
  queryClient.getQueryCache().subscribe((event) => {
    if (event.type === 'updated' && event.query.queryKey.includes('orders')) {
      console.log('📦 Order cache updated:', {
        queryKey: event.query.queryKey,
        dataLength: Array.isArray(event.query.state.data) ? event.query.state.data.length : 'N/A',
        timestamp: new Date().toISOString(),
      });
    }
  });
}

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

export { queryClient }; 