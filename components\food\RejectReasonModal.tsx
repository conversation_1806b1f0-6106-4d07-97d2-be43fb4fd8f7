import React, { useState, useCallback, useMemo } from 'react';
import {
    View,
    TouchableOpacity,
    Modal,
    Text as RNText,
    TextInput,
} from 'react-native';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { Card, CardContent } from '~/components/ui/card';
import { X } from 'lucide-react-native';
import { useColorScheme } from '~/lib/useColorScheme';

interface RejectReasonModalProps {
    isVisible: boolean;
    onClose: () => void;
    onConfirm: (reason: string) => void;
    isLoading?: boolean;
}

// Move constants outside component to prevent recreation
const PREDEFINED_REASONS = [
    "We're Busy",
    "Item out of Stock",
    "Custom Message"
] as const;

const RejectReasonModalComponent: React.FC<RejectReasonModalProps> = ({ 
    isVisible, 
    onClose, 
    onConfirm, 
    isLoading = false 
}) => {
    const [selectedReason, setSelectedReason] = useState<string>('');
    const [customMessage, setCustomMessage] = useState<string>('');
    const { isDarkColorScheme } = useColorScheme();

    // Memoize handlers to prevent unnecessary re-renders
    const handleReasonSelect = useCallback((reason: string) => {
        setSelectedReason(reason);
        if (reason !== 'Custom Message') {
            setCustomMessage('');
        }
    }, []);

    const handleConfirm = useCallback(() => {
        const finalReason = selectedReason === 'Custom Message' ? customMessage : selectedReason;
        if (finalReason.trim()) {
            onConfirm(finalReason.trim());
            // Reset state immediately for better UX
            setSelectedReason('');
            setCustomMessage('');
            onClose();
        }
    }, [selectedReason, customMessage, onConfirm, onClose]);

    const handleClose = useCallback(() => {
        setSelectedReason('');
        setCustomMessage('');
        onClose();
    }, [onClose]);

    // Memoize computed values
    const isConfirmDisabled = useMemo(() => 
        !selectedReason || 
        (selectedReason === 'Custom Message' && !customMessage.trim()) ||
        isLoading,
        [selectedReason, customMessage, isLoading]
    );

    const placeholderColor = useMemo(() => 
        isDarkColorScheme ? '#6b7280' : '#9ca3af',
        [isDarkColorScheme]
    );

    // Memoize reason options to prevent re-rendering
    const reasonOptions = useMemo(() => 
        PREDEFINED_REASONS.map((reason) => (
            <TouchableOpacity
                key={reason}
                onPress={() => handleReasonSelect(reason)}
                className="flex-row items-center py-3"
                disabled={isLoading}
                activeOpacity={0.7}
            >
                <View className={`w-5 h-5 rounded-full border-2 mr-3 items-center justify-center ${
                    selectedReason === reason
                        ? 'border-red-500 bg-red-500'
                        : isDarkColorScheme 
                            ? 'border-gray-500 bg-transparent' 
                            : 'border-gray-400 bg-transparent'
                }`}>
                    {selectedReason === reason && (
                        <View className="w-2 h-2 rounded-full bg-white" />
                    )}
                </View>
                <Text className="text-base text-foreground font-medium">
                    {reason}
                </Text>
            </TouchableOpacity>
        )),
        [selectedReason, handleReasonSelect, isLoading, isDarkColorScheme]
    );

    return (
        <Modal
            visible={isVisible}
            transparent
            animationType="slide"
            onRequestClose={handleClose}
            statusBarTranslucent={false}
        >
            <View className="flex-1 bg-black/60 items-center justify-center p-4">
                <Card className="w-full max-w-sm bg-card shadow-2xl">
                    <CardContent className="p-6">
                        {/* Header */}
                        <View className="flex-row items-center justify-between mb-6">
                            <Text className="text-xl font-bold text-foreground">
                                Select Reason
                            </Text>
                            <TouchableOpacity 
                                onPress={handleClose}
                                className="w-8 h-8 items-center justify-center rounded-full bg-muted active:bg-muted/80"
                                disabled={isLoading}
                                activeOpacity={0.7}
                            >
                                <X size={18} className="text-muted-foreground" />
                            </TouchableOpacity>
                        </View>

                        {/* Reason Options */}
                        <View className="mb-6">
                            {reasonOptions}
                        </View>

                        {/* Custom Message Input */}
                        {selectedReason === 'Custom Message' && (
                            <View className="mb-6">
                                <TextInput
                                    value={customMessage}
                                    onChangeText={setCustomMessage}
                                    placeholder="Enter your reason..."
                                    multiline
                                    numberOfLines={3}
                                    className={`border border-border rounded-lg p-3 text-base text-foreground bg-background min-h-[80px]`}
                                    placeholderTextColor={placeholderColor}
                                    editable={!isLoading}
                                    autoFocus
                                    maxLength={200}
                                />
                            </View>
                        )}

                        {/* Action Button */}
                        <Button
                            onPress={handleConfirm}
                            disabled={isConfirmDisabled}
                            className={`w-full ${
                                isConfirmDisabled
                                    ? 'bg-muted opacity-60'
                                    : 'bg-red-500 active:bg-red-600'
                            }`}
                        >
                            <Text className={`font-semibold ${
                                isConfirmDisabled ? 'text-muted-foreground' : 'text-white'
                            }`}>
                                {isLoading ? 'Rejecting...' : 'Send'}
                            </Text>
                        </Button>
                    </CardContent>
                </Card>
            </View>
        </Modal>
    );
};

RejectReasonModalComponent.displayName = 'RejectReasonModal';

export const RejectReasonModal = React.memo(RejectReasonModalComponent); 