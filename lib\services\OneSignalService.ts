import { OneSignal } from 'react-native-onesignal';
import { Platform, AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Order } from '../api/orders';
import { queryClient } from '../query-client';
import { notificationsApi } from '../api/notifications';
import { router } from 'expo-router';
import { soundService } from './SoundService';

export interface OneSignalNotificationData {
  type: 'new_order' | 'order_update' | 'general';
  order?: Order;
  message?: string;
  action?: string;
  [key: string]: any;
}

interface UserSession {
  access_token: string;
  restaurant_id: string;
  branch_id: string;
  user_id?: string;
}

const CACHE_KEYS = {
  ONESIGNAL_PLAYER_ID: 'onesignal_player_id',
  USER_SESSION: 'onesignal_user_session',
  NOTIFICATION_SETTINGS: 'onesignal_notification_settings',
} as const;

export class OneSignalNotificationService {
  private static instance: OneSignalNotificationService;
  private isInitialized = false;
  private userSession: UserSession | null = null;
  private appState: AppStateStatus = AppState.currentState;
  private notificationCallbacks: Array<(notification: OneSignalNotificationData) => void> = [];

  private constructor() {
    this.setupAppStateListener();
  }

  public static getInstance(): OneSignalNotificationService {
    if (!OneSignalNotificationService.instance) {
      OneSignalNotificationService.instance = new OneSignalNotificationService();
    }
    return OneSignalNotificationService.instance;
  }

  /**
   * Initialize OneSignal with configuration
   */
  public async initialize(appId: string, userSession?: UserSession): Promise<void> {
    try {
      console.log('🚀 Initializing OneSignal service...');

      if (this.isInitialized) {
        console.log('✅ OneSignal already initialized');
        return;
      }

      // Initialize sound service first
      await soundService.initialize();
      
      console.log("appId", appId);
      // Initialize OneSignal
      console.log('📱 Initializing OneSignal SDK...');
      await OneSignal.initialize(appId);

      // Mark as initialized early so other methods can work
      this.isInitialized = true;
      console.log('✅ OneSignal SDK initialized, configuring...');

      // Configure OneSignal settings
      await this.configureOneSignal();

      // Set up notification listeners
      this.setupNotificationListeners();

      // Set up permission handlers
      this.setupPermissionHandlers();

      // Set user session if provided
      if (userSession) {
        await this.setUserSession(userSession);
      }
     
      this.getPlayerId();
      console.log('✅ OneSignal service initialized successfully');

    } catch (error) {
      console.error('❌ Failed to initialize OneSignal service:', error);
      // Reset initialization flag on error
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * Configure OneSignal with app-specific settings
   */
  private async configureOneSignal(): Promise<void> {
    try {
      console.log('⚙️ Configuring OneSignal settings...');

      // Request notification permissions
      console.log('📱 Requesting notification permissions...');
      const permissionResult = await OneSignal.Notifications.requestPermission(true);
      console.log('📱 OneSignal permission result:', permissionResult);

      // Configure notification behavior for foreground
      OneSignal.Notifications.addEventListener('foregroundWillDisplay', (event) => {
        console.log('📱 OneSignal notification will show in foreground:', event.notification);

        // Handle the notification in foreground
        this.handleForegroundNotification(event.notification);

        // Display the notification
        event.notification.display();
      });

      // Configure iOS settings
      if (Platform.OS === 'ios') {
        OneSignal.LiveActivities.setupDefault();
        console.log('🍎 iOS Live Activities configured');
      }

      // If permission was granted, try to ensure subscription is created
      if (permissionResult) {
        console.log('✅ Permission granted, ensuring subscription is created...');
        await this.ensureSubscriptionCreated();

        // Try to get player ID immediately after subscription creation
        console.log('🔍 Checking if player ID is available after subscription creation...');
        const immediatePlayerId = await OneSignal.User.pushSubscription.getIdAsync();
        console.log('📱 Immediate player ID check result:', immediatePlayerId || 'null');
      } else {
        console.warn('⚠️ Permission not granted - player ID will not be available');
      }

      // Give OneSignal a moment to finish configuration
      console.log('⏸️ Waiting for OneSignal to finish configuration...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Final check of OneSignal state
      try {
        const finalPermission = await OneSignal.Notifications.getPermissionAsync();
        const finalPlayerId = await OneSignal.User.pushSubscription.getIdAsync();
        console.log('📊 Final OneSignal state:', {
          permission: finalPermission,
          playerId: finalPlayerId || 'null',
          isInitialized: this.isInitialized
        });
      } catch (stateError) {
        console.warn('⚠️ Could not check final OneSignal state:', stateError);
      }

      console.log('✅ OneSignal configured successfully');
    } catch (error) {
      console.error('❌ Failed to configure OneSignal:', error);
      throw error; // Re-throw to prevent marking as initialized if config fails
    }
  }

  /**
   * Set up notification event listeners
   */
  private setupNotificationListeners(): void {
    // Notification clicked (app opened from notification)
    OneSignal.Notifications.addEventListener('click', (event) => {
      console.log('🔔 OneSignal notification clicked:', event.notification);
      this.handleNotificationClick(event.notification);
    });

    // Notification received in foreground
    OneSignal.Notifications.addEventListener('foregroundWillDisplay', (event) => {
      console.log('🔔 OneSignal notification received in foreground:', event.notification);
      
      // Parse notification data to determine type
      const notificationData = this.parseNotificationData(event.notification);
      
      // Handle the notification
      this.handleForegroundNotification(event.notification);
      
      // Only display system notification for non-new_order types
      // For new_order notifications, we handle everything ourselves (custom sound + navigation)
      if (notificationData.type !== 'new_order') {
        console.log('📱 Displaying system notification for type:', notificationData.type);
        event.notification.display();
      } else {
        console.log('🔕 Suppressing system notification for new_order (using custom handling)');
      }
    });

    console.log('✅ OneSignal notification listeners set up');
  }

  /**
   * Set up permission change handlers
   */
  private setupPermissionHandlers(): void {
    // Listen for subscription changes - this is the most reliable way to get player ID
    OneSignal.User.pushSubscription.addEventListener('change', (subscription: any) => {
      console.log('📱 OneSignal subscription change event:', subscription);

      const playerId = subscription?.id;
      const optedIn = subscription?.optedIn;

      console.log(`🔔 Subscription details - ID: ${playerId || 'null'}, OptedIn: ${optedIn}`);

      if (playerId && optedIn) {
        console.log(`✅ Valid OneSignal subscription received: ${playerId}`);
        this.onSubscriptionReady(playerId);
      } else {
        console.log('⚠️ OneSignal subscription not ready or opted out');
      }
    });
  }

  /**
   * Ensure OneSignal subscription is created (for cases where it doesn't auto-create)
   */
  private async ensureSubscriptionCreated(): Promise<void> {
    try {
      console.log('🔧 Ensuring OneSignal subscription is created...');

      // On some devices/environments, OneSignal needs a little push to create subscription
      // Try to opt the user in explicitly
      try {
        OneSignal.User.pushSubscription.optIn();
        console.log('✅ OneSignal subscription opt-in triggered');
      } catch (optInError) {
        console.warn('⚠️ Could not trigger opt-in:', optInError);
      }

      // Wait a moment for the subscription to be created
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error('❌ Error ensuring subscription creation:', error);
    }
  }

  /**
   * Handle when OneSignal subscription is ready with a valid player ID
   */
  private async onSubscriptionReady(playerId: string): Promise<void> {
    try {
      console.log('🎯 OneSignal subscription ready with player ID:', playerId);

      // Store the player ID
      await AsyncStorage.setItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID, playerId);

      // Ensure player ID is registered with server
      await this.ensurePlayerRegistration();

    } catch (error) {
      console.error('❌ Error handling subscription ready:', error);
    }
  }

  /**
   * Handle when notification permission is granted (legacy method)
   */
  private async onPermissionGranted(): Promise<void> {
    try {
      console.log('🎯 Notification permission granted, checking for subscription...');

      // The subscription change event should handle the actual player ID
      // This method is kept for backward compatibility

    } catch (error) {
      console.error('❌ Error handling permission grant:', error);
    }
  }

  /**
   * Get OneSignal player ID with comprehensive checks
   */
  public async getPlayerId(): Promise<string | null> {
    try {
      console.log('🔍 Getting OneSignal player ID...', { isInitialized: this.isInitialized });

      // Check cached player ID first
      const cachedPlayerId = await AsyncStorage.getItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID);
      if (cachedPlayerId) {
        console.log('💾 Using cached player ID:', cachedPlayerId);
        return cachedPlayerId;
      }

      // Check permission status
      let hasPermission = false;
      try {
        hasPermission = await OneSignal.Notifications.getPermissionAsync();
        console.log('📱 Permission status:', hasPermission);
      } catch (permError) {
        console.warn('⚠️ Could not check permission:', permError);
        // Continue anyway, might still be able to get player ID
      }

      if (!hasPermission) {
        console.warn('⚠️ No notification permission - player ID will likely be null');
        // Continue anyway to try getting player ID
      }

      // Try multiple methods to get the player ID
      let playerId: string | null = null;

      // Method 1: Direct getIdAsync call
      try {
        console.log('📱 Attempting getIdAsync...');
        playerId = await OneSignal.User.pushSubscription.getIdAsync();
        console.log("📱 getIdAsync result:", playerId || 'null');
      } catch (idError) {
        console.warn('⚠️ getIdAsync failed:', idError);
      }

      // Method 2: Retry after delay if first attempt failed
      if (!playerId) {
        try {
          console.log('🔍 Retrying getIdAsync after delay...');
          await new Promise(resolve => setTimeout(resolve, 500));
          playerId = await OneSignal.User.pushSubscription.getIdAsync();
          console.log("📱 getIdAsync retry result:", playerId || 'null');
        } catch (retryError) {
          console.warn('⚠️ getIdAsync retry failed:', retryError);
        }
      }

      // Cache the player ID if we got one
      if (playerId) {
        console.log('✅ Player ID obtained, caching it:', playerId);
        await AsyncStorage.setItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID, playerId);
      } else {
        console.warn('⚠️ Player ID is null - this could mean:');
        console.log('   • OneSignal subscription not created yet');
        console.log('   • Notification permissions denied');
        console.log('   • OneSignal still initializing');
        console.log('   • Device/network issues');
      }

      return playerId || null;
    } catch (error) {
      console.error('❌ Error getting OneSignal player ID:', error);
      return null;
    }
  }

  /**
   * Handle notification received in foreground
   */
  private async handleForegroundNotification(notification: any): Promise<void> {
    try {
      const notificationData = this.parseNotificationData(notification);
      console.log('📱 Processing foreground notification:', notificationData);

      // Handle different notification types
      switch (notificationData.type) {
        case 'new_order':
          await this.handleNewOrderNotification(notificationData);
          // For new order notifications, we handle everything ourselves (navigation + custom sound)
          // Do NOT display the system notification to avoid system sound
          return; // Early return to prevent event.notification.display()
        case 'order_update':
          await this.handleOrderUpdateNotification(notificationData);
          break;
        default:
          await this.handleGeneralNotification(notificationData);
      }

      // Notify callbacks
      this.notificationCallbacks.forEach(callback => callback(notificationData));

    } catch (error) {
      console.error('❌ Error handling foreground notification:', error);
    }
  }

  /**
   * Handle notification click (app opened from notification)
   */
  private async handleNotificationClick(notification: any): Promise<void> {
    try {
      const notificationData = this.parseNotificationData(notification);
      console.log('🔔 Processing notification click:', notificationData);

      // Play sound for new order notifications when app is opened from background
      if (notificationData.type === 'new_order') {
        console.log('🔊 Playing order alert sound for background notification click...');
        try {
          // await soundService.playOrderAlert();
        } catch (soundError) {
          console.warn('⚠️ Could not play sound on notification click:', soundError);
        }
      }

      // Handle navigation based on notification type
      switch (notificationData.type) {
        case 'new_order':
          if (notificationData.order) {
            console.log('📱 Navigating to order notification screen from click...');
            console.log('📱 Order data for navigation:', JSON.stringify(notificationData.order, null, 2));
            
            try {
              router.push({
                pathname: '/order-notification',
                params: {
                  orderData: JSON.stringify(notificationData.order)
                }
              });
              console.log('✅ Navigation initiated successfully');
            } catch (navError) {
              console.error('❌ Navigation error:', navError);
              // Fallback navigation
              console.log('🔄 Attempting fallback navigation...');
              router.push('/order-notification');
            }
          } else {
            console.warn('⚠️ No order data in notification click for new_order type');
            console.log('📋 Available notification data:', notificationData);
          }
          break;
        case 'order_update':
          // Navigate to specific order
          console.log('📦 Handling order update notification click');
          break;
        default:
          console.log('📢 Handling general notification click');
      }

      // Notify callbacks
      this.notificationCallbacks.forEach(callback => callback(notificationData));

    } catch (error) {
      console.error('❌ Error handling notification click:', error);
    }
  }

  /**
   * Handle new order notification
   */
  private async handleNewOrderNotification(notificationData: OneSignalNotificationData): Promise<void> {
    try {
      console.log('🚨 Handling new order notification:', notificationData);

      if (!notificationData.order) {
        console.warn('⚠️ New order notification missing order data');
        console.log('📋 Available notification data:', notificationData);
        return;
      }

      // Play order alert sound
      console.log('🔊 Playing order alert sound...');
      try {
        // await soundService.playOrderAlert();
        console.log('✅ Order alert sound started');
      } catch (soundError) {
        console.warn('⚠️ Could not play order alert sound:', soundError);
      }

      // Navigate to order notification screen with order data
      console.log('📱 Navigating to order notification screen...');
      console.log('📱 Order data for navigation:', JSON.stringify(notificationData.order, null, 2));
      
      try {
        router.push({
          pathname: '/order-notification',
          params: {
            orderData: JSON.stringify(notificationData.order)
          }
        });
        console.log('✅ Navigation to order notification screen initiated');
      } catch (navError) {
        console.error('❌ Error navigating to order notification screen:', navError);
        // Fallback - try simple navigation
        console.log('🔄 Attempting fallback navigation...');
        try {
          router.push('/order-notification');
          console.log('✅ Fallback navigation initiated');
        } catch (fallbackError) {
          console.error('❌ Fallback navigation also failed:', fallbackError);
        }
      }

      // Invalidate React Query cache to refresh order lists
      queryClient.invalidateQueries({
        queryKey: ['orders'],
        exact: false
      });

      console.log('✅ New order notification processed successfully');

    } catch (error) {
      console.error('❌ Error handling new order notification:', error);
    }
  }

  /**
   * Handle order update notification
   */
  private async handleOrderUpdateNotification(notificationData: OneSignalNotificationData): Promise<void> {
    try {
      console.log('📦 Handling order update notification:', notificationData);

      // OneSignal handles notification sound automatically

      // Invalidate cache for order updates
      queryClient.invalidateQueries({
        queryKey: ['orders'],
        exact: false
      });

    } catch (error) {
      console.error('❌ Error handling order update notification:', error);
    }
  }

  /**
   * Handle general notification
   */
  private async handleGeneralNotification(notificationData: OneSignalNotificationData): Promise<void> {
    try {
      console.log('📢 Handling general notification:', notificationData);

      // OneSignal handles notification sound automatically

    } catch (error) {
      console.error('❌ Error handling general notification:', error);
    }
  }

  /**
   * Parse notification data from OneSignal notification
   */
  private parseNotificationData(notification: any): OneSignalNotificationData {
    try {
      // OneSignal can provide custom data in either additionalData or data field
      const additionalData = notification.additionalData || {};
      const data = notification.data || {};
      
      // Merge both data sources, with additionalData taking precedence
      const customData = { ...data, ...additionalData };
      
      console.log('🔍 Parsing notification data:', {
        additionalData,
        data,
        merged: customData,
        notificationBody: notification.body,
        notificationMessage: notification.message
      });
console.log(customData,"customData")
      // Handle order data - it might be a string that needs parsing or already an object
      let order = undefined;
      if (customData.order) {
        if (typeof customData.order === 'string') {
          try {
            order = JSON.parse(customData.order);
          } catch (parseError) {
            console.warn('⚠️ Failed to parse order string:', parseError);
            order = undefined;
          }
        } else if (typeof customData.order === 'object') {
          order = customData.order;
        }
      }

      const parsedData = {
        type: customData.type || customData.orderType || 'general', // Check both type and orderType
        order,
        message: notification.body || notification.message || customData.message,
        action: customData.action,
        orderId: customData.orderId, // Include orderId from data
        messageType: customData.messageType, // Include messageType from data
        ...customData,
      };
      
      console.log('✅ Parsed notification data:', parsedData);
      return parsedData;
    } catch (error) {
      console.error('❌ Error parsing notification data:', error);
      return {
        type: 'general',
        message: notification.body || notification.message || 'Unknown notification',
      };
    }
  }

  /**
   * Set user session for targeting
   */
  public async setUserSession(userSession: UserSession): Promise<void> {
    try {
      this.userSession = userSession;

      // Set external user ID
      OneSignal.User.addAlias('external_id', userSession.user_id || userSession.restaurant_id);

      // Set tags for targeting
      OneSignal.User.addTags({
        restaurant_id: userSession.restaurant_id,
        branch_id: userSession.branch_id,
        user_type: 'merchant',
        app_version: '1.0.0',
      });

      // Store session for background use
      await AsyncStorage.setItem(CACHE_KEYS.USER_SESSION, JSON.stringify(userSession));

      // Register player ID with server now that we have user session
      await this.ensurePlayerRegistration();

      console.log('✅ OneSignal user session set successfully');
    } catch (error) {
      console.error('❌ Error setting OneSignal user session:', error);
    }
  }

  /**
   * Register player ID with your server
   */
  private async registerPlayerWithServer(playerId: string): Promise<void> {
    try {
      // Use the enhanced method with retry logic
      await this.registerPlayerWithServerEnhanced(playerId);
    } catch (error) {
      // Error already logged in enhanced method
      // Don't throw error - registration failure shouldn't break the app
    }
  }

  /**
   * Wait for OneSignal to be ready and have a player ID
   */
  private async waitForOneSignalReady(maxWaitTime: number = 10000): Promise<string | null> {
    const startTime = Date.now();
    let attempts = 0;

    console.log(`⏰ Waiting for OneSignal player ID (max ${maxWaitTime / 1000}s)...`);

    while (Date.now() - startTime < maxWaitTime) {
      attempts++;
      const elapsed = Math.round((Date.now() - startTime) / 1000);
      console.log(`🔍 Attempt ${attempts} (${elapsed}s elapsed) - checking for player ID...`);

      // Try to get player ID directly without initialization checks
      let playerId: string | null = null;

      try {
        // Check cached first
        playerId = await AsyncStorage.getItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID);
        if (playerId) {
          console.log(`💾 Found cached player ID: ${playerId}`);
          return playerId;
        }

        // Try direct OneSignal call
        playerId = await OneSignal.User.pushSubscription.getIdAsync();
        console.log(`📱 OneSignal getIdAsync returned: ${playerId || 'null'}`);

        if (playerId) {
          console.log(`✅ OneSignal ready with player ID: ${playerId} (after ${attempts} attempts, ${elapsed}s)`);
          // Cache it for future use
          await AsyncStorage.setItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID, playerId);
          return playerId;
        }

      } catch (error) {
        console.warn(`⚠️ Error checking player ID on attempt ${attempts}:`, error);
      }

      // Wait 2 seconds before next attempt (but shorter wait for first few attempts)
      const waitTime = attempts <= 3 ? 1000 : 2000;
      console.log(`⏸️ Waiting ${waitTime / 1000}s before next attempt...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    const totalTime = Math.round((Date.now() - startTime) / 1000);
    console.warn(`⚠️ Timed out waiting for OneSignal player ID after ${attempts} attempts (${totalTime}s)`);
    return null;
  }

  /**
   * Ensure player ID is registered with server (called when we have both session and player ID)
   */
  public async ensurePlayerRegistration(): Promise<void> {
    try {
      if (!this.userSession) {
        console.warn('⚠️ Cannot register player - no user session available');
        return;
      }

      console.log('🔍 Checking for player ID...');
      let playerId = await this.getPlayerId();

      if (!playerId) {
        console.log('⏰ Player ID not ready, waiting for OneSignal initialization...');
        playerId = await this.waitForOneSignalReady(10000); // Wait up to 10 seconds
      }

      if (!playerId) {
        console.warn('⚠️ Cannot register player - no player ID available after waiting');
        console.log('💡 This usually means permissions were denied or OneSignal failed to initialize');
        return;
      }

      console.log('🔄 Player ID available, registering with server...');
      await this.registerPlayerWithServer(playerId);

    } catch (error) {
      console.error('❌ Error ensuring player registration:', error);
    }
  }

  /**
   * Manually trigger subscription creation (for testing/debugging)
   */
  public async manuallyCreateSubscription(): Promise<{ success: boolean; playerId?: string; error?: string }> {
    try {
      console.log('🔧 Manually triggering OneSignal subscription creation...');

      // Force opt-in
      await this.ensureSubscriptionCreated();

      // Wait a bit longer for subscription to be created
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Try to get the player ID
      const playerId = await this.getPlayerId();

      if (playerId) {
        console.log('✅ Manual subscription creation successful, player ID:', playerId);
        return { success: true, playerId };
      } else {
        const error = 'Subscription creation triggered but no player ID available yet';
        console.warn('⚠️', error);
        return { success: false, error };
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Manual subscription creation failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Manually trigger player registration (for testing/debugging)
   */
  public async manuallyRegisterPlayer(): Promise<{ success: boolean; playerId?: string; error?: string }> {
    try {
      console.log('🔧 Manually triggering player registration...');

      if (!this.userSession) {
        const error = 'No user session available - please log in first';
        console.error('❌', error);
        return { success: false, error };
      }

      const playerId = await this.getPlayerId();
      if (!playerId) {
        // Try to create subscription first
        console.log('🔄 No player ID found, attempting to create subscription...');
        const subscriptionResult = await this.manuallyCreateSubscription();

        if (!subscriptionResult.success || !subscriptionResult.playerId) {
          const error = 'No OneSignal player ID available after subscription creation attempt';
          console.error('❌', error);
          return { success: false, error };
        }

        // Use the newly created player ID
        await this.registerPlayerWithServer(subscriptionResult.playerId);
        console.log('✅ Manual player registration completed successfully with new subscription');
        return { success: true, playerId: subscriptionResult.playerId };
      }

      await this.registerPlayerWithServer(playerId);
      console.log('✅ Manual player registration completed successfully');
      return { success: true, playerId };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Manual player registration failed:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Set up app state listener
   */
  private setupAppStateListener(): void {
    AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      this.appState = nextAppState;
      console.log(`🔄 App state changed to: ${nextAppState}`);
    });
  }

  /**
   * Add notification callback
   */
  public addNotificationCallback(callback: (notification: OneSignalNotificationData) => void): void {
    this.notificationCallbacks.push(callback);
  }

  /**
   * Remove notification callback
   */
  public removeNotificationCallback(callback: (notification: OneSignalNotificationData) => void): void {
    const index = this.notificationCallbacks.indexOf(callback);
    if (index > -1) {
      this.notificationCallbacks.splice(index, 1);
    }
  }

  /**
   * Send test notification (for debugging)
   */
  public async sendTestNotification(): Promise<void> {
    try {
      const playerId = await this.getPlayerId();
      if (!playerId) {
        console.warn('⚠️ No player ID available for test notification');
        return;
      }

      // Here you would call your server to send a test notification
      console.log('🧪 Test notification would be sent to player:', playerId);
    } catch (error) {
      console.error('❌ Error sending test notification:', error);
    }
  }

  /**
   * Test order notification locally (for development)
   */
  public async testOrderNotification(): Promise<void> {
    try {
      console.log('🧪 Testing order notification locally...');
      
      // Create mock order data
      const mockOrder: Order = {
        order_id: 'TEST_ORDER_123',
        order_name: 'TEST123',
        customer_name: 'John Doe',
        customer_number: '+971501234567',
        order_type: 'delivery',
        payment_method: 'cash',
        bill_amount: {
          total_bill: 45.50,
          sub_total: 40.00,
          previous_bill: 0,
          delivery_charge: 2.00,
          discount_amount: 0
        },
        cart_items: [
          {
            id: 'item_1',
            name: 'Chicken Burger',
            type: 'non-veg',
            quantity: 2,
            price: 15.00,
            variants: [{ id: 'var_1', name: 'Large', price: 0 }],
            notes: 'Extra sauce',
            combos: [],
            add_ons: null,
            image_link: ''
          },
          {
            id: 'item_2',
            name: 'French Fries',
            type: 'veg',
            quantity: 1,
            price: 10.00,
            variants: [],
            notes: '',
            combos: [],
            add_ons: null,
            image_link: ''
          }
        ],
        address: '123 Test Street, Dubai, UAE',
        receiver_name: 'John Doe',
        receiver_number: '+971501234567',
        latitude: '25.2048',
        longitude: '55.2708',
        status: 'initiated',
        additional_notes: '',
        promo_code: null,
        reason: null,
        modified_at: null,
        pre_order: false,
        created_at: new Date().toISOString(),
        ordered_on: new Date().toISOString()
      };

      // Create mock notification data
      const mockNotificationData: OneSignalNotificationData = {
        type: 'new_order',
        order: mockOrder,
        message: 'New order received!',
        action: 'view_order'
      };

      // Simulate receiving the notification
      await this.handleNewOrderNotification(mockNotificationData);
      
      console.log('✅ Test order notification completed');
    } catch (error) {
      console.error('❌ Error testing order notification:', error);
    }
  }

  /**
   * Clear OneSignal data (for logout)
   */
  public async clearUserData(): Promise<void> {
    try {
      // Remove notification ID from server before clearing local data
      if (this.userSession) {
        const playerId = await this.getPlayerId();
        if (playerId) {
          console.log('📡 Removing notification ID from server...');
          await this.removePlayerFromServerEnhanced(playerId);
        }
      }

      // Clear OneSignal data
      OneSignal.User.removeAlias('external_id');
      OneSignal.User.removeTags(['restaurant_id', 'branch_id', 'user_type']);

      // Clear local storage
      await AsyncStorage.multiRemove([
        CACHE_KEYS.ONESIGNAL_PLAYER_ID,
        CACHE_KEYS.USER_SESSION,
        CACHE_KEYS.NOTIFICATION_SETTINGS,
      ]);

      this.userSession = null;
      console.log('✅ OneSignal user data cleared');
    } catch (error) {
      console.error('❌ Error clearing OneSignal user data:', error);
    }
  }

  /**
   * Get notification settings
   */
  public async getNotificationSettings(): Promise<any> {
    try {
      const permission = await OneSignal.Notifications.getPermissionAsync();
      const playerId = await this.getPlayerId();

      return {
        hasPermission: permission,
        playerId,
        isInitialized: this.isInitialized,
      };
    } catch (error) {
      console.error('❌ Error getting notification settings:', error);
      return null;
    }
  }

  /**
   * Get comprehensive OneSignal debug information
   */
  public async getDebugInfo(): Promise<any> {
    try {
      console.log('🔍 Gathering OneSignal debug information...');

      const cachedPlayerId = await AsyncStorage.getItem(CACHE_KEYS.ONESIGNAL_PLAYER_ID);
      const cachedUserSession = await AsyncStorage.getItem(CACHE_KEYS.USER_SESSION);

      let permission = false;
      let directPlayerId = null;

      try {
        permission = await OneSignal.Notifications.getPermissionAsync();
        directPlayerId = await OneSignal.User.pushSubscription.getIdAsync();
      } catch (onesignalError) {
        console.warn('⚠️ Error accessing OneSignal:', onesignalError);
      }

      const debugInfo = {
        timestamp: new Date().toISOString(),
        serviceState: {
          isInitialized: this.isInitialized,
          hasUserSession: !!this.userSession,
          userSessionDetails: this.userSession ? {
            userId: this.userSession.user_id,
            restaurantId: this.userSession.restaurant_id,
            hasAccessToken: !!this.userSession.access_token
          } : null
        },
        oneSignalState: {
          permission,
          directPlayerId,
          cachedPlayerId,
          hasCachedUserSession: !!cachedUserSession
        },
        platform: Platform.OS,
        canAttemptRegistration: !!(this.userSession && (directPlayerId || cachedPlayerId))
      };

      console.log('📊 OneSignal Debug Info:', debugInfo);
      return debugInfo;

    } catch (error) {
      console.error('❌ Error getting debug info:', error);
      return { error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Test with the exact OneSignal notification data provided by user
   */
  public async testWithProvidedNotificationData(): Promise<void> {
    try {
      console.log('🧪 Testing with provided OneSignal notification data...');
      
      // Simulate the exact notification structure that OneSignal would provide
      const mockNotification = {
        body: 'message',
        title: 'title', 
        additionalData: {
          messageType: 'messageType',
          orderType: 'new_order',
          orderId: 'orderId',
          type: 'new_order',
          order: {
            order_id: 'TEST_ORDER_123',
            order_name: 'TEST123',
            customer_name: 'John Doe',
            customer_number: '+971501234567',
            order_type: 'delivery',
            payment_method: 'cash',
            bill_amount: {
              total_bill: 45.5,
              sub_total: 40.0,
              previous_bill: 0,
              delivery_charge: 2.0,
              discount_amount: 0
            },
            cart_items: [
              {
                id: 'item_1',
                name: 'Chicken Burger',
                type: 'non-veg',
                quantity: 2,
                price: 15.0,
                variants: [{ id: 'var_1', name: 'Large', price: 0 }],
                notes: 'Extra sauce',
                combos: [],
                add_ons: null,
                image_link: ''
              },
              {
                id: 'item_2',
                name: 'French Fries',
                type: 'veg',
                quantity: 1,
                price: 10.0,
                variants: [],
                notes: '',
                combos: [],
                add_ons: null,
                image_link: ''
              }
            ],
            address: '123 Test Street, Dubai, UAE',
            receiver_name: 'John Doe',
            receiver_number: '+971501234567',
            latitude: '25.2048',
            longitude: '55.2708',
            status: 'initiated',
            additional_notes: '',
            promo_code: null,
            reason: null,
            modified_at: null,
            pre_order: false,
            created_at: '2025-07-01T00:00:00.000Z',
            ordered_on: '2025-07-01T00:00:00.000Z'
          },
          message: 'testomg',
          action: 'testing'
        },
        // Also test with data field (alternative location)
        data: {
          messageType: 'messageType',
          orderType: 'new_order',
          orderId: 'orderId',
          type: 'new_order',
          message: 'testomg',
          action: 'testing'
        }
      };

      console.log('📱 Testing notification parsing...');
      const parsedData = this.parseNotificationData(mockNotification);
      console.log('✅ Parsed data:', parsedData);

      console.log('📱 Testing notification click handling...');
      await this.handleNotificationClick(mockNotification);

      console.log('📱 Testing foreground notification handling...');
      await this.handleForegroundNotification(mockNotification);
      
      console.log('✅ Test with provided notification data completed');
    } catch (error) {
      console.error('❌ Error testing with provided notification data:', error);
    }
  }

  /**
   * Clear user data and switch to a new user
   */
  public async switchUser(newUserSession: UserSession): Promise<void> {
    try {
      console.log('🔄 Switching OneSignal user...');
      console.log('👤 From:', this.userSession?.user_id || 'none');
      console.log('👤 To:', newUserSession.user_id);

      // Clear previous user data first
      await this.clearUserData();

      // Set new user session
      await this.setUserSession(newUserSession);

      console.log('✅ OneSignal user switched successfully');
    } catch (error) {
      console.error('❌ Error switching OneSignal user:', error);
      throw error;
    }
  }

  /**
   * Get current user session
   */
  public getUserSession(): UserSession | null {
    return this.userSession;
  }

  /**
   * Verify server integration by checking if OneSignal ID is properly stored/removed
   */
  public async verifyServerIntegration(): Promise<{
    success: boolean;
    playerId?: string;
    userSession?: any;
    serverStoreSuccess?: boolean;
    serverRemoveSuccess?: boolean;
    error?: string;
  }> {
    try {
      console.log('🧪 Verifying OneSignal server integration...');

      if (!this.userSession) {
        return {
          success: false,
          error: 'No user session available - please log in first'
        };
      }

      const playerId = await this.getPlayerId();
      if (!playerId) {
        return {
          success: false,
          error: 'No OneSignal player ID available'
        };
      }

      console.log('📡 Testing server store operation...');
      let serverStoreSuccess = false;
      try {
        await this.registerPlayerWithServer(playerId);
        serverStoreSuccess = true;
        console.log('✅ Server store operation successful');
      } catch (error) {
        console.error('❌ Server store operation failed:', error);
      }

      return {
        success: serverStoreSuccess,
        playerId,
        userSession: {
          userId: this.userSession.user_id,
          restaurantId: this.userSession.restaurant_id,
          branchId: this.userSession.branch_id
        },
        serverStoreSuccess,
        serverRemoveSuccess: undefined // Not testing remove in verification
      };

    } catch (error) {
      console.error('❌ Error verifying server integration:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Enhanced method to register player with server with retry logic
   */
  private async registerPlayerWithServerEnhanced(playerId: string, retryCount: number = 3): Promise<void> {
    if (!this.userSession) {
      console.warn('⚠️ No user session available for server registration');
      return;
    }

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        console.log(`📡 Registering OneSignal Player ID with server (attempt ${attempt}/${retryCount})...`);
        console.log(`👤 User ID: ${this.userSession.user_id || this.userSession.restaurant_id}`);
        console.log(`📱 Player ID: ${playerId}`);

        await notificationsApi.storeNotificationId(
          {
            userId: this.userSession.user_id || this.userSession.restaurant_id,
            deviceId: playerId,
            isApp: true,
          },
          this.userSession.access_token
        );

        console.log('✅ Player ID registered with server successfully');
        return; // Success - exit the retry loop

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`❌ Error registering player ID with server (attempt ${attempt}/${retryCount}):`, error);
        
        if (attempt < retryCount) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
          console.log(`⏸️ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All attempts failed
    console.error(`❌ Failed to register player ID with server after ${retryCount} attempts`);
    console.warn('⚠️ Continuing without server registration...');
    throw lastError || new Error('Registration failed after all retry attempts');
  }

  /**
   * Enhanced method to remove player from server with retry logic
   */
  private async removePlayerFromServerEnhanced(playerId: string, retryCount: number = 2): Promise<void> {
    if (!this.userSession) {
      console.warn('⚠️ No user session available for server removal');
      return;
    }

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        console.log(`📡 Removing OneSignal Player ID from server (attempt ${attempt}/${retryCount})...`);
        console.log(`👤 User ID: ${this.userSession.user_id || this.userSession.restaurant_id}`);
        console.log(`📱 Player ID: ${playerId}`);

        await notificationsApi.removeNotificationId(
          this.userSession.user_id || this.userSession.restaurant_id,
          playerId,
          this.userSession.access_token
        );

        console.log('✅ Player ID removed from server successfully');
        return; // Success - exit the retry loop

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.error(`❌ Error removing player ID from server (attempt ${attempt}/${retryCount}):`, error);
        
        if (attempt < retryCount) {
          const delay = Math.min(1000 * attempt, 3000); // Linear backoff, max 3s
          console.log(`⏸️ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All attempts failed - but don't throw error for removal as it's not critical
    console.error(`❌ Failed to remove player ID from server after ${retryCount} attempts`);
    console.warn('⚠️ Continuing with logout process...');
  }
}

// Export singleton instance
export const oneSignalService = OneSignalNotificationService.getInstance(); 