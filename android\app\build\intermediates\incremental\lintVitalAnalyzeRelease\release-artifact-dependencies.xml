<dependencies>
  <compile
      roots=":@@:react-native-screens::release,com.facebook.react:react-android:0.79.5:release@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,:@@:expo::release,:@@:expo-modules-core::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,androidx.appcompat:appcompat-resources:1.7.0@aar,:@@:react-native-onesignal::release,com.onesignal:OneSignal:5.1.34@aar,com.onesignal:core:5.1.34@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,host.exp.exponent:expo.modules.device:7.1.4@aar,host.exp.exponent:expo.modules.filesystem:18.1.11@aar,host.exp.exponent:expo.modules.sharing:13.1.5@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,host.exp.exponent:expo.modules.securestore:14.2.3@aar,androidx.biometric:biometric:1.1.0@aar,com.onesignal:notifications:5.1.34@aar,com.google.firebase:firebase-messaging:23.4.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-cloud-messaging:17.1.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,androidx.activity:activity:1.8.0@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,com.onesignal:in-app-messages:5.1.34@aar,androidx.browser:browser:1.6.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,host.exp.exponent:expo.modules.av:15.1.7@aar,com.google.android.exoplayer:exoplayer:2.18.1@aar,com.google.android.exoplayer:exoplayer-ui:2.18.1@aar,androidx.media:media:1.4.3@aar,androidx.media:media:1.4.3@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.loader:loader:1.0.0@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.work:work-runtime:2.8.1@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.core:core-ktx:1.13.1@aar,androidx.savedstate:savedstate:1.2.1@aar,com.facebook.fresco:fbcore:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.android.exoplayer:extension-okhttp:2.18.1@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,:@@:expo-constants::release,host.exp.exponent:expo.modules.haptics:14.1.4@aar,:@@:expo-dev-client::release,androidx.databinding:viewbinding:8.8.2@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.cardview:cardview:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,com.google.firebase:firebase-datatransport:18.1.7@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.firebase:firebase-components:17.1.5@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.0@aar,com.facebook.fresco:ui-core:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,:@@:react-native-async-storage_async-storage::release,:@@:react-native-gesture-handler::release,:@@:react-native-reanimated::release,:@@:react-native-safe-area-context::release,:@@:react-native-svg::release,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.react:hermes-android:0.79.5:release@aar,org.jetbrains:annotations:23.0.0@jar,com.google.android.exoplayer:exoplayer-core:2.18.1@aar,com.google.android.exoplayer:exoplayer-common:2.18.1@aar,com.google.guava:guava:33.0.0-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.soloader:soloader:0.12.1@aar,com.facebook.soloader:nativeloader:0.12.1@jar,com.facebook.soloader:annotation:0.12.1@jar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,commons-io:commons-io:2.6@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,expo.modules.asset:expo.modules.asset:11.1.7@aar,expo.modules.audio:expo.modules.audio:0.4.8@aar,com.google.guava:failureaccess:1.0.2@jar,com.google.android.exoplayer:exoplayer-database:2.18.1@aar,com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar,com.google.android.exoplayer:exoplayer-dash:2.18.1@aar,com.google.android.exoplayer:exoplayer-hls:2.18.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar,host.exp.exponent:expo.modules.blur:14.1.5@aar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,host.exp.exponent:expo.modules.font:13.3.2@aar,host.exp.exponent:expo.modules.keepawake:14.1.4@aar,host.exp.exponent:expo.modules.lineargradient:14.1.5@aar,host.exp.exponent:expo.modules.linking:7.1.7@aar,host.exp.exponent:expo.modules.navigationbar:4.2.7@aar,host.exp.exponent:expo.modules.print:14.1.4@aar,host.exp.exponent:expo.modules.splashscreen:0.30.10@aar,host.exp.exponent:expo.modules.systemui:5.0.10@aar,com.onesignal:location:5.1.34@aar,:@@:expo-dev-menu-interface::release,:@@:expo-json-utils::release,:@@:expo-manifests::release,:@@:expo-updates-interface::release">
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="Cravin Merchant:react-native-screens"/>
    <dependency
        name="com.facebook.react:react-android:0.79.5:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name=":@@:react-native-onesignal::release"
        simpleName="Cravin Merchant:react-native-onesignal"/>
    <dependency
        name="com.onesignal:OneSignal:5.1.34@aar"
        simpleName="com.onesignal:OneSignal"/>
    <dependency
        name="com.onesignal:core:5.1.34@aar"
        simpleName="com.onesignal:core"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="host.exp.exponent:expo.modules.device:7.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.device"/>
    <dependency
        name="host.exp.exponent:expo.modules.filesystem:18.1.11@aar"
        simpleName="host.exp.exponent:expo.modules.filesystem"/>
    <dependency
        name="host.exp.exponent:expo.modules.sharing:13.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.sharing"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="host.exp.exponent:expo.modules.securestore:14.2.3@aar"
        simpleName="host.exp.exponent:expo.modules.securestore"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="com.onesignal:notifications:5.1.34@aar"
        simpleName="com.onesignal:notifications"/>
    <dependency
        name="com.google.firebase:firebase-messaging:23.4.0@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="com.onesignal:in-app-messages:5.1.34@aar"
        simpleName="com.onesignal:in-app-messages"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="host.exp.exponent:expo.modules.av:15.1.7@aar"
        simpleName="host.exp.exponent:expo.modules.av"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.media:media:1.4.3@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.work:work-runtime:2.8.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name="host.exp.exponent:expo.modules.haptics:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.haptics"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.1.7@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="Cravin Merchant:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="Cravin Merchant:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="Cravin Merchant:react-native-reanimated"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="Cravin Merchant:react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="Cravin Merchant:react-native-svg"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.5:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="com.google.guava:guava:33.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:11.1.7@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="expo.modules.audio:expo.modules.audio:0.4.8@aar"
        simpleName="expo.modules.audio:expo.modules.audio"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="host.exp.exponent:expo.modules.blur:14.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.blur"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:13.3.2@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.lineargradient:14.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.lineargradient"/>
    <dependency
        name="host.exp.exponent:expo.modules.linking:7.1.7@aar"
        simpleName="host.exp.exponent:expo.modules.linking"/>
    <dependency
        name="host.exp.exponent:expo.modules.navigationbar:4.2.7@aar"
        simpleName="host.exp.exponent:expo.modules.navigationbar"/>
    <dependency
        name="host.exp.exponent:expo.modules.print:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.print"/>
    <dependency
        name="host.exp.exponent:expo.modules.splashscreen:0.30.10@aar"
        simpleName="host.exp.exponent:expo.modules.splashscreen"/>
    <dependency
        name="host.exp.exponent:expo.modules.systemui:5.0.10@aar"
        simpleName="host.exp.exponent:expo.modules.systemui"/>
    <dependency
        name="com.onesignal:location:5.1.34@aar"
        simpleName="com.onesignal:location"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
  </compile>
  <package
      roots=":@@:react-native-gesture-handler::release,:@@:react-native-safe-area-context::release,:@@:react-native-screens::release,:@@:react-native-async-storage_async-storage::release,:@@:expo::release,:@@:react-native-onesignal::release,:@@:react-native-reanimated::release,:@@:react-native-svg::release,:@@:expo-dev-launcher::release,:@@:expo-dev-menu::release,:@@:expo-dev-menu-interface::release,:@@:expo-modules-core::release,host.exp.exponent:expo.modules.av:15.1.7@aar,host.exp.exponent:expo.modules.font:13.3.2@aar,host.exp.exponent:expo.modules.splashscreen:0.30.10@aar,host.exp.exponent:expo.modules.systemui:5.0.10@aar,com.facebook.react:react-android:0.79.5:release@aar,:@@:expo-constants::release,:@@:expo-dev-client::release,:@@:expo-manifests::release,:@@:expo-json-utils::release,:@@:expo-updates-interface::release,expo.modules.asset:expo.modules.asset:11.1.7@aar,expo.modules.audio:expo.modules.audio:0.4.8@aar,host.exp.exponent:expo.modules.blur:14.1.5@aar,host.exp.exponent:expo.modules.device:7.1.4@aar,host.exp.exponent:expo.modules.filesystem:18.1.11@aar,host.exp.exponent:expo.modules.haptics:14.1.4@aar,host.exp.exponent:expo.modules.keepawake:14.1.4@aar,host.exp.exponent:expo.modules.lineargradient:14.1.5@aar,host.exp.exponent:expo.modules.linking:7.1.7@aar,host.exp.exponent:expo.modules.navigationbar:4.2.7@aar,host.exp.exponent:expo.modules.print:14.1.4@aar,host.exp.exponent:expo.modules.securestore:14.2.3@aar,host.exp.exponent:expo.modules.sharing:13.1.5@aar,com.onesignal:OneSignal:5.1.34@aar,com.onesignal:in-app-messages:5.1.34@aar,com.onesignal:notifications:5.1.34@aar,com.onesignal:location:5.1.34@aar,com.onesignal:core:5.1.34@aar,com.google.android.material:material:1.12.0@aar,com.google.firebase:firebase-messaging:23.4.0@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,androidx.work:work-runtime-ktx:2.8.1@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,androidx.core:core-splashscreen:1.2.0-alpha02@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,androidx.biometric:biometric:1.1.0@aar,androidx.constraintlayout:constraintlayout:2.0.1@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.fragment:fragment-ktx:1.6.1@aar,androidx.legacy:legacy-support-v4:1.0.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-cloud-messaging:17.1.0@aar,com.google.android.gms:play-services-stats:17.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.activity:activity:1.8.0@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.legacy:legacy-support-core-ui:1.0.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.autofill:autofill:1.1.0@aar,com.facebook.fresco:animated-gif:3.6.0@aar,com.facebook.fresco:webpsupport:3.6.0@aar,com.facebook.fresco:fresco:3.6.0@aar,com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar,com.facebook.fresco:animated-base:3.6.0@aar,com.facebook.fresco:animated-drawable:3.6.0@aar,com.facebook.fresco:vito-options:3.6.0@aar,com.facebook.fresco:drawee:3.6.0@aar,com.facebook.fresco:nativeimagefilters:3.6.0@aar,com.facebook.fresco:memory-type-native:3.6.0@aar,com.facebook.fresco:memory-type-java:3.6.0@aar,com.facebook.fresco:imagepipeline-native:3.6.0@aar,com.facebook.fresco:memory-type-ashmem:3.6.0@aar,com.facebook.fresco:imagepipeline:3.6.0@aar,com.facebook.fresco:nativeimagetranscoder:3.6.0@aar,com.facebook.fresco:imagepipeline-base:3.6.0@aar,com.facebook.fresco:urimod:3.6.0@aar,com.facebook.fresco:vito-source:3.6.0@aar,com.facebook.fresco:middleware:3.6.0@aar,com.facebook.fresco:ui-common:3.6.0@aar,com.facebook.fresco:soloader:3.6.0@aar,com.facebook.fresco:fbcore:3.6.0@aar,androidx.browser:browser:1.6.0@aar,androidx.core:core-ktx:1.13.1@aar,androidx.media3:media3-datasource-okhttp:1.4.0@aar,androidx.media3:media3-extractor:1.4.0@aar,androidx.media3:media3-container:1.4.0@aar,androidx.media3:media3-datasource:1.4.0@aar,androidx.media3:media3-decoder:1.4.0@aar,androidx.media3:media3-database:1.4.0@aar,androidx.media3:media3-common:1.4.0@aar,androidx.media3:media3-exoplayer-dash:1.4.0@aar,androidx.media3:media3-exoplayer-hls:1.4.0@aar,androidx.media3:media3-exoplayer-smoothstreaming:1.4.0@aar,androidx.media3:media3-exoplayer:1.4.0@aar,androidx.transition:transition:1.5.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.emoji2:emoji2-views-helper:1.3.0@aar,androidx.emoji2:emoji2:1.3.0@aar,com.google.android.exoplayer:exoplayer:2.18.1@aar,com.google.android.exoplayer:exoplayer-dash:2.18.1@aar,com.google.android.exoplayer:exoplayer-hls:2.18.1@aar,com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar,com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar,com.google.android.exoplayer:exoplayer-core:2.18.1@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.work:work-runtime:2.8.1@aar,androidx.loader:loader:1.0.0@aar,androidx.viewpager:viewpager:1.0.0@aar,com.google.android.exoplayer:exoplayer-ui:2.18.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.recyclerview:recyclerview:1.2.1@aar,androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.media:media:1.4.3@aar,androidx.media:media:1.4.3@aar,androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar,com.google.android.exoplayer:extension-okhttp:2.18.1@aar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,androidx.room:room-runtime:2.5.0@aar,androidx.room:room-common:2.5.0@jar,io.insert-koin:koin-core-jvm:3.5.6@jar,co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar,co.touchlab:stately-concurrency-jvm:2.0.6@jar,co.touchlab:stately-strict-jvm:2.0.6@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar,org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.tracing:tracing-ktx:1.2.0@aar,androidx.collection:collection-ktx:1.1.0@jar,com.facebook.fresco:ui-core:3.6.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,com.facebook.fresco:vito-renderer:3.6.0@aar,com.facebook.react:hermes-android:0.79.5:release@aar,androidx.databinding:viewbinding:8.8.2@aar,com.github.Dimezis:BlurView:version-2.0.6@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar,com.google.android.exoplayer:exoplayer-database:2.18.1@aar,com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar,com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar,com.google.android.exoplayer:exoplayer-common:2.18.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,com.google.firebase:firebase-datatransport:18.1.7@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.sqlite:sqlite-framework:2.3.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,com.google.firebase:firebase-components:17.1.5@aar,androidx.sqlite:sqlite:2.3.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.facebook.yoga:proguard-annotations:1.19.0@jar,com.facebook.fbjni:fbjni:0.7.0@aar,com.facebook.infer.annotation:infer-annotation:0.18.0@jar,com.facebook.soloader:soloader:0.12.1@aar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.parse.bolts:bolts-tasks:1.4.0@jar,com.facebook.soloader:nativeloader:0.12.1@jar,commons-io:commons-io:2.6@jar,com.google.code.gson:gson:2.8.6@jar,com.facebook.device.yearclass:yearclass:2.1.0@jar,commons-codec:commons-codec:1.10@jar,com.google.errorprone:error_prone_annotations:2.15.0@jar,org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar,com.facebook.soloader:annotation:0.12.1@jar,com.google.guava:guava:33.0.0-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.constraintlayout:constraintlayout-solver:2.0.1@jar,com.google.guava:failureaccess:1.0.2@jar">
    <dependency
        name=":@@:react-native-gesture-handler::release"
        simpleName="Cravin Merchant:react-native-gesture-handler"/>
    <dependency
        name=":@@:react-native-safe-area-context::release"
        simpleName="Cravin Merchant:react-native-safe-area-context"/>
    <dependency
        name=":@@:react-native-screens::release"
        simpleName="Cravin Merchant:react-native-screens"/>
    <dependency
        name=":@@:react-native-async-storage_async-storage::release"
        simpleName="Cravin Merchant:react-native-async-storage_async-storage"/>
    <dependency
        name=":@@:expo::release"
        simpleName="host.exp.exponent:expo"/>
    <dependency
        name=":@@:react-native-onesignal::release"
        simpleName="Cravin Merchant:react-native-onesignal"/>
    <dependency
        name=":@@:react-native-reanimated::release"
        simpleName="Cravin Merchant:react-native-reanimated"/>
    <dependency
        name=":@@:react-native-svg::release"
        simpleName="Cravin Merchant:react-native-svg"/>
    <dependency
        name=":@@:expo-dev-launcher::release"
        simpleName="host.exp.exponent:expo-dev-launcher"/>
    <dependency
        name=":@@:expo-dev-menu::release"
        simpleName="host.exp.exponent:expo-dev-menu"/>
    <dependency
        name=":@@:expo-dev-menu-interface::release"
        simpleName="host.exp.exponent:expo-dev-menu-interface"/>
    <dependency
        name=":@@:expo-modules-core::release"
        simpleName="host.exp.exponent:expo-modules-core"/>
    <dependency
        name="host.exp.exponent:expo.modules.av:15.1.7@aar"
        simpleName="host.exp.exponent:expo.modules.av"/>
    <dependency
        name="host.exp.exponent:expo.modules.font:13.3.2@aar"
        simpleName="host.exp.exponent:expo.modules.font"/>
    <dependency
        name="host.exp.exponent:expo.modules.splashscreen:0.30.10@aar"
        simpleName="host.exp.exponent:expo.modules.splashscreen"/>
    <dependency
        name="host.exp.exponent:expo.modules.systemui:5.0.10@aar"
        simpleName="host.exp.exponent:expo.modules.systemui"/>
    <dependency
        name="com.facebook.react:react-android:0.79.5:release@aar"
        simpleName="com.facebook.react:react-android"/>
    <dependency
        name=":@@:expo-constants::release"
        simpleName="host.exp.exponent:expo-constants"/>
    <dependency
        name=":@@:expo-dev-client::release"
        simpleName="host.exp.exponent:expo-dev-client"/>
    <dependency
        name=":@@:expo-manifests::release"
        simpleName="host.exp.exponent:expo-manifests"/>
    <dependency
        name=":@@:expo-json-utils::release"
        simpleName="host.exp.exponent:expo-json-utils"/>
    <dependency
        name=":@@:expo-updates-interface::release"
        simpleName="host.exp.exponent:expo-updates-interface"/>
    <dependency
        name="expo.modules.asset:expo.modules.asset:11.1.7@aar"
        simpleName="expo.modules.asset:expo.modules.asset"/>
    <dependency
        name="expo.modules.audio:expo.modules.audio:0.4.8@aar"
        simpleName="expo.modules.audio:expo.modules.audio"/>
    <dependency
        name="host.exp.exponent:expo.modules.blur:14.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.blur"/>
    <dependency
        name="host.exp.exponent:expo.modules.device:7.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.device"/>
    <dependency
        name="host.exp.exponent:expo.modules.filesystem:18.1.11@aar"
        simpleName="host.exp.exponent:expo.modules.filesystem"/>
    <dependency
        name="host.exp.exponent:expo.modules.haptics:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.haptics"/>
    <dependency
        name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.keepawake"/>
    <dependency
        name="host.exp.exponent:expo.modules.lineargradient:14.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.lineargradient"/>
    <dependency
        name="host.exp.exponent:expo.modules.linking:7.1.7@aar"
        simpleName="host.exp.exponent:expo.modules.linking"/>
    <dependency
        name="host.exp.exponent:expo.modules.navigationbar:4.2.7@aar"
        simpleName="host.exp.exponent:expo.modules.navigationbar"/>
    <dependency
        name="host.exp.exponent:expo.modules.print:14.1.4@aar"
        simpleName="host.exp.exponent:expo.modules.print"/>
    <dependency
        name="host.exp.exponent:expo.modules.securestore:14.2.3@aar"
        simpleName="host.exp.exponent:expo.modules.securestore"/>
    <dependency
        name="host.exp.exponent:expo.modules.sharing:13.1.5@aar"
        simpleName="host.exp.exponent:expo.modules.sharing"/>
    <dependency
        name="com.onesignal:OneSignal:5.1.34@aar"
        simpleName="com.onesignal:OneSignal"/>
    <dependency
        name="com.onesignal:in-app-messages:5.1.34@aar"
        simpleName="com.onesignal:in-app-messages"/>
    <dependency
        name="com.onesignal:notifications:5.1.34@aar"
        simpleName="com.onesignal:notifications"/>
    <dependency
        name="com.onesignal:location:5.1.34@aar"
        simpleName="com.onesignal:location"/>
    <dependency
        name="com.onesignal:core:5.1.34@aar"
        simpleName="com.onesignal:core"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.google.firebase:firebase-messaging:23.4.0@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.8.1@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
        simpleName="androidx.core:core-splashscreen"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.biometric:biometric:1.1.0@aar"
        simpleName="androidx.biometric:biometric"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-v4:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-v4"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-ui"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.autofill:autofill:1.1.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="com.facebook.fresco:animated-gif:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-gif"/>
    <dependency
        name="com.facebook.fresco:webpsupport:3.6.0@aar"
        simpleName="com.facebook.fresco:webpsupport"/>
    <dependency
        name="com.facebook.fresco:fresco:3.6.0@aar"
        simpleName="com.facebook.fresco:fresco"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-okhttp3"/>
    <dependency
        name="com.facebook.fresco:animated-base:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-base"/>
    <dependency
        name="com.facebook.fresco:animated-drawable:3.6.0@aar"
        simpleName="com.facebook.fresco:animated-drawable"/>
    <dependency
        name="com.facebook.fresco:vito-options:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-options"/>
    <dependency
        name="com.facebook.fresco:drawee:3.6.0@aar"
        simpleName="com.facebook.fresco:drawee"/>
    <dependency
        name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagefilters"/>
    <dependency
        name="com.facebook.fresco:memory-type-native:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-java:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-java"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-native"/>
    <dependency
        name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
        simpleName="com.facebook.fresco:memory-type-ashmem"/>
    <dependency
        name="com.facebook.fresco:imagepipeline:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline"/>
    <dependency
        name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
        simpleName="com.facebook.fresco:nativeimagetranscoder"/>
    <dependency
        name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
        simpleName="com.facebook.fresco:imagepipeline-base"/>
    <dependency
        name="com.facebook.fresco:urimod:3.6.0@aar"
        simpleName="com.facebook.fresco:urimod"/>
    <dependency
        name="com.facebook.fresco:vito-source:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-source"/>
    <dependency
        name="com.facebook.fresco:middleware:3.6.0@aar"
        simpleName="com.facebook.fresco:middleware"/>
    <dependency
        name="com.facebook.fresco:ui-common:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-common"/>
    <dependency
        name="com.facebook.fresco:soloader:3.6.0@aar"
        simpleName="com.facebook.fresco:soloader"/>
    <dependency
        name="com.facebook.fresco:fbcore:3.6.0@aar"
        simpleName="com.facebook.fresco:fbcore"/>
    <dependency
        name="androidx.browser:browser:1.6.0@aar"
        simpleName="androidx.browser:browser"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.media3:media3-datasource-okhttp:1.4.0@aar"
        simpleName="androidx.media3:media3-datasource-okhttp"/>
    <dependency
        name="androidx.media3:media3-extractor:1.4.0@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.4.0@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.4.0@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.4.0@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.4.0@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.4.0@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.4.0@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-exoplayer-hls:1.4.0@aar"
        simpleName="androidx.media3:media3-exoplayer-hls"/>
    <dependency
        name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.0@aar"
        simpleName="androidx.media3:media3-exoplayer-smoothstreaming"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.4.0@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-dash"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-hls"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-rtsp"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-smoothstreaming"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-core"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.work:work-runtime:2.8.1@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-ui"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.2.1@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
        simpleName="androidx.slidingpanelayout:slidingpanelayout"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.media:media:1.4.3@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
        simpleName="androidx.asynclayoutinflater:asynclayoutinflater"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
        simpleName="com.google.android.exoplayer:extension-okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="io.insert-koin:koin-core-jvm:3.5.6@jar"
        simpleName="io.insert-koin:koin-core-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrent-collections-jvm"/>
    <dependency
        name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-concurrency-jvm"/>
    <dependency
        name="co.touchlab:stately-strict-jvm:2.0.6@jar"
        simpleName="co.touchlab:stately-strict-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-reflect"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="com.facebook.fresco:ui-core:3.6.0@aar"
        simpleName="com.facebook.fresco:ui-core"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.facebook.fresco:vito-renderer:3.6.0@aar"
        simpleName="com.facebook.fresco:vito-renderer"/>
    <dependency
        name="com.facebook.react:hermes-android:0.79.5:release@aar"
        simpleName="com.facebook.react:hermes-android"/>
    <dependency
        name="androidx.databinding:viewbinding:8.8.2@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="com.github.Dimezis:BlurView:version-2.0.6@aar"
        simpleName="com.github.Dimezis:BlurView"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-datasource"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-database"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-extractor"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-decoder"/>
    <dependency
        name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
        simpleName="com.google.android.exoplayer:exoplayer-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.1.7@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
        simpleName="com.facebook.yoga:proguard-annotations"/>
    <dependency
        name="com.facebook.fbjni:fbjni:0.7.0@aar"
        simpleName="com.facebook.fbjni:fbjni"/>
    <dependency
        name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
        simpleName="com.facebook.infer.annotation:infer-annotation"/>
    <dependency
        name="com.facebook.soloader:soloader:0.12.1@aar"
        simpleName="com.facebook.soloader:soloader"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.parse.bolts:bolts-tasks:1.4.0@jar"
        simpleName="com.parse.bolts:bolts-tasks"/>
    <dependency
        name="com.facebook.soloader:nativeloader:0.12.1@jar"
        simpleName="com.facebook.soloader:nativeloader"/>
    <dependency
        name="commons-io:commons-io:2.6@jar"
        simpleName="commons-io:commons-io"/>
    <dependency
        name="com.google.code.gson:gson:2.8.6@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
        simpleName="com.facebook.device.yearclass:yearclass"/>
    <dependency
        name="commons-codec:commons-codec:1.10@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
        simpleName="org.jetbrains.kotlin:kotlin-annotations-jvm"/>
    <dependency
        name="com.facebook.soloader:annotation:0.12.1@jar"
        simpleName="com.facebook.soloader:annotation"/>
    <dependency
        name="com.google.guava:guava:33.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
        simpleName="androidx.constraintlayout:constraintlayout-solver"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.2@jar"
        simpleName="com.google.guava:failureaccess"/>
  </package>
</dependencies>
