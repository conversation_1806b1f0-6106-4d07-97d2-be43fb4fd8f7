import { ENV } from '../config/env';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface Branch {
  branch_id: string;
  branch_name: string;
}

export interface LoginSuccessData {
  access_token: string;
  user_id: string;
  user_type: string;
  username: string;
  restaurant_id: string;
  branches: Branch[];
}

export interface ApiSuccessResponse<T> {
  statusCode: number;
  data: T;
  message?: string;
}

export interface ApiErrorResponse {
  statusCode: number;
  message: string;
}

/**
 * Perform merchant login request.
 */
export async function loginMerchant({ username, password }: LoginRequest): Promise<LoginSuccessData> {
  const response = await fetch(`${ENV.API_BASE_URL}/auth/merchant/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: '*/*',
    },
    body: JSON.stringify({ username, password }),
  });

  const json = await response.json();

  if (!response.ok) {
    const err = (json as ApiErrorResponse).message || 'Login failed';
    throw new Error(err);
  }

  const success = json as ApiSuccessResponse<LoginSuccessData>;
  return success.data;
} 