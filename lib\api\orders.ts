import { ENV, shouldUseMockData } from '../config/env';

// API base URL - uses environment configuration
const API_BASE_URL = ENV.API_BASE_URL;

// Updated types to match actual API response
export interface CartItemAddon {
  id: string;
  name: string;
  price: number;
}

export interface CartItemVariant {
  id: string;
  name: string;
  price: number;
}

export interface CartItemCombo {
  id: string;
  name: string;
  items: string[];
}

export interface OrderItem {
  id: string;
  name: string;
  type: 'veg' | 'non-veg';
  notes: string;
  price: number;
  combos: CartItemCombo[];
  add_ons: CartItemAddon[] | null;
  quantity: number;
  variants: CartItemVariant[];
  image_link: string;
}

export interface BillAmount {
  sub_total: number;
  total_bill: number;
  previous_bill: number;
  delivery_charge: number;
  discount_amount: number;
}

export interface Order {
  created_at: string;
  order_id: string;
  order_name: string;
  ordered_on: string;
  customer_name: string;
  customer_number: string;
  address: string;
  receiver_name: string;
  receiver_number: string;
  latitude: string;
  longitude: string;
  cart_items?: OrderItem[];
  status: 'initiated' | 'accepted' | 'preparing' | 'ready' | 'ready_for_pickup' | 'out_for_delivery' | 'picked_up' | 'completed' | 'cancelled' | 'rejected';
  payment_method: 'cash' | 'card' | 'online';
  additional_notes: string;
  bill_amount: BillAmount;
  promo_code: string | null;
  reason: string | null;
  modified_at: string | null;
  pre_order: boolean;
  order_type: 'delivery' | 'pickup';
  items?: any;
  customer_address?: string;
}

export interface ApiResponse<T> {
  statusCode: number;
  data: T;
  message?: string;
  error?: string;
}


// API Service Functions
export const ordersApi = {
  // Get new orders
  getNewOrders: async (restaurant_id: string, branch_id: string, access_token: string): Promise<Order[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${restaurant_id}/${branch_id}/active?order_type=new`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Order[]> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch new orders');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching new orders:', error);
      
   
      throw error;
    }
  },

  // Get accepted orders
  getAcceptedOrders: async (restaurant_id: string, branch_id: string, access_token: string): Promise<Order[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${restaurant_id}/${branch_id}/active?order_type=accepted`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Order[]> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch accepted orders');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching accepted orders:', error);
    
      throw error;
    }
  },

  // Get past orders
  getPastOrders: async (restaurant_id: string, branch_id: string, access_token: string): Promise<Order[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/restaurants/${restaurant_id}/orders/past?branchId=${branch_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Order[]> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch past orders');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching past orders:', error);
      
  
      throw error;
    }
  },

  // Accept order
  acceptOrder: async (order_id: string, access_token: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${order_id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
        body: JSON.stringify({ status: 'accepted' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      console.log("🚀 ~ result:", result)

      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to accept order');
      }

      // Check if the operation was successful even with 200 status code
      if (result.data && result.data.success === false) {
        // Throw error so React Query handles it properly
        throw new Error(result.data.message || 'Failed to accept order');
      }
    } catch (error) {
      console.error('Error accepting order:', error);
      if (shouldUseMockData()) {
        // For development, we'll just simulate success
         return;
      }
      throw error;
    }
  },

  // Reject order
  rejectOrder: async (order_id: string, access_token: string, rejected_reason: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${order_id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
        body: JSON.stringify({
          status: 'rejected',
          rejected_reason
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      console.log("🚀 ~ result:", result)

      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to reject order');
      }

      // Check if the operation was successful even with 200 status code
      if (result.data && result.data.success === false) {
        // Throw error so React Query handles it properly
        throw new Error(result.data.message || 'Failed to reject order');
      }
    } catch (error) {
      console.error('Error rejecting order:', error);

      throw error;
    }
  },

  // Mark order as ready
  markOrderReady: async (order_id: string, access_token: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/orders/${order_id}/ready`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to mark order as ready');
      }
    } catch (error) {
      console.error('Error marking order as ready:', error);
      
      throw error;
    }
  },

  // Complete order
  completeOrder: async (order_id: string, access_token: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/orders/${order_id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to complete order');
      }
    } catch (error) {
      console.error('Error completing order:', error);
      
      throw error;
    }
  },

  // Mark order as out for delivery
  markOrderOutForDelivery: async (order_id: string, access_token: string, driverInfo?: { name: string; number: string; id: string }): Promise<void> => {
    try {
      const body = driverInfo 
        ? { status: 'out_for_delivery', name: driverInfo.name, number: driverInfo.number, id: driverInfo.id }
        : { status: 'out_for_delivery' };

      const response = await fetch(`${API_BASE_URL}/branches/orders/${order_id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        console.log(result.error, "result.error")
        throw new Error(result.error || 'Failed to mark order as out for delivery');
      }
    } catch (error) {
      console.error('Error marking order as out for delivery:', error);
      
      throw error;
    }
  },

  // Mark order as ready for pickup
  markOrderReadyForPickup: async (order_id: string, access_token: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${order_id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
        body: JSON.stringify({ status: 'ready_for_pickup' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to mark order as ready for pickup');
      }
    } catch (error) {
      console.error('Error marking order as ready for pickup:', error);
     
      throw error;
    }
  },

  // Mark order as picked up
  markOrderPickedUp: async (order_id: string, access_token: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/orders/${order_id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
        body: JSON.stringify({ status: 'picked_up' }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to mark order as picked up');
      }
    } catch (error) {
      console.error('Error marking order as picked up:', error);
      if (shouldUseMockData()) {
        // For development, we'll just simulate success
        console.log(`Mock: Order ${order_id} marked as picked up`);
        return;
      }
      throw error;
    }
  },

  // Get restaurant branches
  getBranches: async (restaurant_id: string, access_token: string): Promise<any[]> => {
    try {
      const response = await fetch(`${API_BASE_URL}/restaurants/${restaurant_id}/branches`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch branches');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching branches:', error);
      
      if (shouldUseMockData()) {
        // Return mock data for development
        return [
          { branch_id: 'branch_1', branch_name: 'Main Branch' },
          { branch_id: 'branch_2', branch_name: 'Downtown Branch' },
        ];
      }
      throw error;
    }
  },

  // Get delivery drivers for a branch
  getDeliveryDrivers: async (restaurant_id: string, branch_id: string, user_type: string, access_token: string): Promise<any[]> => {
    try {
      const queryParam = user_type === "owner" ? `restaurantId=${restaurant_id}` : `restaurantId=${restaurant_id}&branchId=${branch_id}`;
      const response = await fetch(`${API_BASE_URL}/branches/delivery-drivers?${queryParam}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any[]> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch delivery drivers');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching delivery drivers:', error);
      
      if (shouldUseMockData()) {
        // Return mock data for development
        return [
          { id: 'driver_1', name: 'John Doe', number: '+971501234567' },
          { id: 'driver_2', name: 'Ahmed Ali', number: '+971507654321' },
        ];
      }
      throw error;
    }
  },

  // Get branch delivery status (includes branch_delivery_access)
  getBranchDeliveryStatus: async (restaurant_id: string, branch_id: string, user_type: string, access_token: string): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/branches/get-all-flags/${restaurant_id}/status?branchId=${branch_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch branch delivery status');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching branch delivery status:', error);
      
      if (shouldUseMockData()) {
        // Return mock data for development - matching the actual response format
        return [{ 
          branch_id: branch_id,
          restaurant_id: restaurant_id,
          inbox_access_enabled: true, 
          branch_inbox_access: true,
          branch_delivery_access: true 
        }];
      }
      throw error;
    }
  },

  // Get order details for printing
  getOrderDetails: async (order_id: string, access_token: string): Promise<any> => {
    try {
      const response = await fetch(`${API_BASE_URL}/restaurants/orders/${order_id}/details`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<any> = await response.json();
      
      if (result.statusCode !== 200) {
        throw new Error(result.error || 'Failed to fetch order details');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching order details:', error);
      
      if (shouldUseMockData()) {
        // Return mock data for development
        return {
          restaurant_name: 'Cravin Restaurant',
          branch_name: 'Main Branch',
          trn_number: 'TRN 123456789',
          branch_phone: '+971 XX XXX XXXX',
          order_id: order_id,
          customer_name: 'Mock Customer',
          customer_phone: '+971 XX XXX XXXX',
          order_type: 'delivery',
          bill: {
            total_bill: 50.00,
            sub_total: 45.00,
            delivery_charge: 5.00,
            discount_amount: 0
          },
          items: [],
          status: 'accepted',
          payment_method: 'cash',
          created_at: new Date().toISOString(),
          additional_notes: 'Mock order notes'
        };
      }
      throw error;
    }
  },
}; 