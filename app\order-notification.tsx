import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Vibration,
  BackHandler,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as Haptics from 'expo-haptics';
import { useAcceptOrder, useRejectOrder } from '../lib/hooks/useOrders';
import { RejectReasonModal } from '../components/food/RejectReasonModal';
import { Order } from '../lib/api/orders';
import { OneSignalNotificationData } from '../lib/services/OneSignalService';

// Sound service with error handling
let soundService: any = null;
try {
  const { soundService: importedSoundService } = require('../lib/services/SoundService');
  soundService = importedSoundService;
} catch (error) {
  console.warn('Sound service not available:', error);
  // Create a mock sound service
  soundService = {
    playOrderAlert: async () => console.log('Mock: playOrderAlert'),
    stopAlert: async () => console.log('Mock: stopAlert'),
    initialize: async () => console.log('Mock: initialize'),
  };
}

interface OrderNotificationProps {
  // Modal mode props - can accept either Order or OneSignalNotificationData
  isVisible?: boolean;
  orderData?: Order | OneSignalNotificationData | null;
  onAccept?: (orderId: string) => void;
  onReject?: (orderId: string, reason: string) => void;
  onClose?: () => void;
  // Screen mode - uses route params
  isModal?: boolean;
  /** Whether this component should manage playing the alert sound. Default true. */
  playSound?: boolean;
}

// Unified Order Notification Component
// This single component handles both modal and screen presentation modes
function UnifiedOrderNotification(props?: OrderNotificationProps) {
  const router = useRouter();
  const params = useLocalSearchParams();
  
  // Determine if this is modal mode or screen mode
  const isModalMode = props?.isModal || props?.isVisible !== undefined;
  
  // Parse order data from props (modal mode) or route params (screen mode)
  const orderData: Order | null = React.useMemo(() => {
    if (isModalMode && props?.orderData) {
      // Check if orderData is OneSignalNotificationData type (has order property) or Order type
      if (props.orderData && typeof props.orderData === 'object') {
        // If it has an 'order' property, it's OneSignalNotificationData
        if ('order' in props.orderData && props.orderData.order) {
          return props.orderData.order;
        }
        // If it has order_id property, it's an Order
        if ('order_id' in props.orderData) {
          return props.orderData as Order;
        }
      }
      return null;
    }
    
    try {
      const parsed = params.orderData ? JSON.parse(params.orderData as string) : null;
      console.log('Order notification screen - Parsed order data:', parsed);
      console.log('Order notification screen - Bill amount:', parsed?.bill_amount);
      return parsed;
    } catch (error) {
      console.error('Error parsing order data:', error);
      return null;
    }
  }, [isModalMode, props?.orderData, params.orderData]);

  const [isProcessing, setIsProcessing] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const alertIntervalRef = useRef<any>(null);

  const acceptOrderMutation = useAcceptOrder();
  const rejectOrderMutation = useRejectOrder();

  // Initialize sound service
  useEffect(() => {
    if (soundService && soundService.initialize) {
      soundService.initialize().catch(console.error);
    }
  }, []);

  // Update current time every second
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, []);

  // Handle hardware back button (only in screen mode)
  useEffect(() => {
    if (isModalMode) return;
    
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleBackPress();
      return true; // Prevent default behavior
    });

    return () => backHandler.remove();
  }, [isModalMode]);

  // Countdown timer for auto-rejection - REMOVED (no auto-reject needed)

  // Sound effects and haptics on mount/visibility change
  useEffect(() => {
    const shouldPlaySoundBase = isModalMode ? props?.isVisible : true;
    const shouldPlaySound = shouldPlaySoundBase && props?.playSound !== false;
    if (shouldPlaySound) {
      // Ensure any previous alert is stopped before starting a new one
      soundService.stopAlert();
      // Force stop any existing sounds first to prevent multiple sounds
      const startAlert = async () => {
        await soundService.playOrderAlert(); // Start our alert
      };
      
      startAlert().catch(console.error);
      
      // Strong vibration pattern
      if (Platform.OS === 'android') {
        Vibration.vibrate([1000, 500, 1000, 500, 1000], true); // Repeat vibration
      } else {
        Vibration.vibrate();
      }
      
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      // Set up repeating alert every 10 seconds
      alertIntervalRef.current = setInterval(() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        if (Platform.OS === 'android') {
          Vibration.vibrate(500);
        }
      }, 10000);

      return () => {
        soundService.stopAlert();
        Vibration.cancel();
        if (alertIntervalRef.current) {
          clearInterval(alertIntervalRef.current);
          alertIntervalRef.current = null;
        }
      };
    } else {
      // If we are not supposed to play, make sure any ongoing alert is stopped
      soundService.stopAlert();
      Vibration.cancel();
      if (alertIntervalRef.current) {
        clearInterval(alertIntervalRef.current);
        alertIntervalRef.current = null;
      }
    }
  }, [isModalMode, props?.isVisible, props?.playSound]);

  const handleBackPress = () => {
    if (isModalMode) {
      // In modal mode, just close the modal
      props?.onClose?.();
      return;
    }
    
    Alert.alert(
      'Pending Order Response',
      'You have a pending order that needs your response. Are you sure you want to go back?',
      [
        {
          text: 'Stay',
          style: 'cancel',
        },
        {
          text: 'Reject Order',
          style: 'destructive',
          onPress: () => handleReject('User cancelled'),
        },
      ]
    );
  };

  const handleAccept = async () => {
    if (!orderData?.order_id || isProcessing) return;

    setIsProcessing(true);
    try {
      // Stop all alerts immediately
      await soundService.stopAlert();
      Vibration.cancel();
      if (alertIntervalRef.current) {
        clearInterval(alertIntervalRef.current);
        alertIntervalRef.current = null;
      }
      
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      
      if (isModalMode && props?.onAccept) {
        // Modal mode - call prop callback
        props.onAccept(orderData.order_id);
      } else {
        // Screen mode - use mutation and navigate
        await acceptOrderMutation.mutateAsync(orderData.order_id);
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('Error accepting order:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to accept order. Please try again.');
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
      }, 1000);
    }
  };

  const handleReject = async (reason: string = 'Restaurant is busy') => {
    if (!orderData?.order_id || isProcessing) return;

    setIsProcessing(true);
    try {
      // Stop all alerts immediately
      await soundService.stopAlert();
      Vibration.cancel();
      if (alertIntervalRef.current) {
        clearInterval(alertIntervalRef.current);
        alertIntervalRef.current = null;
      }
      
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      
      if (isModalMode && props?.onReject) {
        // Modal mode - call prop callback
        props.onReject(orderData.order_id, reason);
      } else {
        // Screen mode - use mutation and navigate
        await rejectOrderMutation.mutateAsync({
          order_id: orderData.order_id,
          rejected_reason: reason,
        });
        router.replace('/(tabs)');
      }
    } catch (error) {
      console.error('Error rejecting order:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to reject order. Please try again.');
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
      }, 1000);
    }
  };

  const formatCurrency = (amount: number) => {
    return `AED ${amount.toFixed(2)}`;
  };

  if (!orderData) {
    if (isModalMode) {
      // In modal mode, just return null if no data
      return null;
    }
    
    // If no order data in screen mode, navigate back
    React.useEffect(() => {
      console.log('No order data found, navigating back');
      router.replace('/(tabs)');
    }, []);
    return null;
  }

  // Additional safety check for bill_amount
  if (!orderData.bill_amount) {
    console.warn('Order data missing bill_amount:', orderData);
  }

  const contentView = (
    <View className="flex-1 bg-red-500">
      <StatusBar style="light" backgroundColor="#ff2b2b" />
      
      {/* Emergency Header */}
      <View className="pt-12  pb-6 px-6 bg-red-600">
        <View className="flex-row justify-between items-center">
          <View className="flex-1">
            <Text className="text-white text-4xl font-black pt-4">
              🚨 NEW ORDER!
            </Text>
            <Text className="text-red-100 text-lg font-bold mt-1">
              Order #{orderData.order_name || 'N/A'}
            </Text>
            <Text className="text-red-100 text-sm mt-1">
              {currentTime.toLocaleTimeString()}
            </Text>
          </View>
          
          {/* Order Status Badge */}
          <View className="px-6 py-3 rounded-2xl bg-red-700">
            <Text className="text-white font-black text-lg">
              🔴 LIVE
            </Text>
            <Text className="text-red-200 text-xs text-center">
              new order
            </Text>
          </View>
        </View>
        
        {/* Close button for modal mode */}
        {/* {isModalMode && (
          <TouchableOpacity
            onPress={handleBackPress}
            className="absolute top-12 right-6 bg-red-700 p-2 rounded-full"
          >
            <Text className="text-white text-xl font-bold">✕</Text>
          </TouchableOpacity>
        )} */}
      </View>

      {/* Main Content */}
      <View className="flex-1 bg-white">
        <ScrollView className="flex-1 p-6">
          {/* Customer Information */}
          <View className="bg-red-50 p-4 rounded-2xl mb-6 border-2 border-red-200">
            <Text className="text-red-800 font-bold text-xl mb-3">
              Customer Details
            </Text>
            
            <View className="space-y-3">
              <View className="flex-row items-center">
                <View className="w-12 h-12 bg-red-200 rounded-full items-center justify-center mr-4">
                  <Text className="text-2xl">👤</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-gray-600 text-sm font-medium">Customer Name</Text>
                  <Text className="text-gray-900 text-lg font-bold">
                    {orderData.customer_name || 'Unknown Customer'}
                  </Text>
                </View>
              </View>

              <View className="flex-row items-center">
                <View className="w-12 h-12 bg-red-200 rounded-full items-center justify-center mr-4">
                  <Text className="text-2xl">📞</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-gray-600 text-sm font-medium">Phone Number</Text>
                  <Text className="text-gray-900 text-lg font-bold">
                    {orderData.customer_number || 'No phone number'}
                  </Text>
                </View>
              </View>

              {orderData.address && (
                <View className="flex-row items-center">
                  <View className="w-12 h-12 bg-red-200 rounded-full items-center justify-center mr-4">
                    <Text className="text-2xl">📍</Text>
                  </View>
                  <View className="flex-1">
                    <Text className="text-gray-600 text-sm font-medium">Delivery Address</Text>
                    <Text className="text-gray-900 text-base font-semibold">
                      {orderData.address}
                    </Text>
                  </View>
                </View>
              )}

              <View className="flex-row items-center">
                <View className="w-12 h-12 bg-red-200 rounded-full items-center justify-center mr-4">
                  <Text className="text-2xl">💰</Text>
                </View>
                <View className="flex-1">
                  <Text className="text-gray-600 text-sm font-medium">Total Amount</Text>
                  <Text className="text-green-600 text-2xl font-black">
                    {formatCurrency(orderData.bill_amount?.total_bill || 0)}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Order Summary */}
          {orderData.cart_items && orderData.cart_items.length > 0 && (
            <View className="bg-gray-50 p-4 rounded-2xl mb-6">
              <Text className="text-gray-800 font-bold text-lg mb-3">
                Order Items ({orderData.cart_items?.length || 0})
              </Text>
              
              {orderData.cart_items?.slice(0, 3).map((item, index) => (
                <View key={index} className="flex-row justify-between items-center py-2">
                  <View className="flex-1">
                    <Text className="text-gray-800 font-semibold">
                      {item.quantity}x {item.name}
                    </Text>
                    {item.variants && item.variants.length > 0 && (
                      <Text className="text-gray-600 text-sm">
                        {item.variants.map(v => v.name).join(', ')}
                      </Text>
                    )}
                    {item.notes && (
                      <Text className="text-gray-500 text-sm">
                        Note: {item.notes}
                      </Text>
                    )}
                  </View>
                  <Text className="text-gray-800 font-bold">
                    {formatCurrency(item.price * item.quantity)}
                  </Text>
                </View>
              ))}
              
              {orderData.cart_items && orderData.cart_items.length > 3 && (
                <Text className="text-gray-600 text-center mt-2">
                  +{orderData.cart_items.length - 3} more items
                </Text>
              )}
            </View>
          )}

          {/* Order Info */}
          <View className="bg-gray-50 p-4 rounded-2xl mb-6">
            <Text className="text-gray-800 font-bold text-lg mb-3">
              Order Information
            </Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600 font-medium">Order Type</Text>
                <Text className="font-semibold capitalize text-gray-800">
                  {orderData.order_type || 'N/A'}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600 font-medium">Payment Method</Text>
                <Text className="font-semibold capitalize text-gray-800">
                  {orderData.payment_method || 'N/A'}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Fixed Action Buttons */}
        <View className="p-6 bg-white border-t border-gray-200">
          <View className="flex-row space-x-4">
            <TouchableOpacity
              onPress={() => setShowRejectModal(true)}
              disabled={isProcessing}
              className={`flex-1 py-4 rounded-2xl border-2 ${
                isProcessing 
                  ? 'bg-gray-200 border-gray-300' 
                  : 'bg-red-100 border-red-300'
              }`}
              activeOpacity={0.7}
            >
              <Text className={`text-center font-black text-lg ${
                isProcessing ? 'text-gray-500' : 'text-red-700'
              }`}>
                ❌ REJECT
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleAccept}
              disabled={isProcessing}
              className={`flex-1 py-4 rounded-2xl ${
                isProcessing 
                  ? 'bg-gray-400' 
                  : 'bg-green-500'
              }`}
              activeOpacity={0.7}
            >
              <Text className="text-white text-center font-black text-lg">
                {isProcessing ? 'PROCESSING...' : '✅ ACCEPT'}
              </Text>
            </TouchableOpacity>
          </View>
          
          <Text className="text-center text-gray-600 text-sm mt-3">
            ⚠️ You must respond to continue using the app
          </Text>
        </View>
      </View>

      {/* Reject Reason Modal */}
      <RejectReasonModal
        isVisible={showRejectModal}
        onClose={() => setShowRejectModal(false)}
        onConfirm={handleReject}
        isLoading={isProcessing}
      />
    </View>
  );

  // Return modal wrapper if in modal mode, otherwise return content directly
  if (isModalMode) {
    return (
      <Modal
        visible={props?.isVisible || false}
        transparent={false}
        animationType="fade"
        presentationStyle="fullScreen"
        onRequestClose={() => {
          // Block modal dismissal
          Alert.alert(
            '⚠️ New Order Alert',
            'You must accept or reject this order before continuing.',
            [
              { text: 'OK' }
            ]
          );
        }}
        statusBarTranslucent={true}
      >
        {contentView}
      </Modal>
    );
  }

  return contentView;
}

// Named export for Modal usage (used by providers)
export const OrderNotificationModal: React.FC<{
  isVisible: boolean;
  orderData: Order | OneSignalNotificationData | null;
  onAccept: (orderId: string) => void;
  onReject: (orderId: string, reason: string) => void;
  onClose: () => void;
  playSound?: boolean;
}> = ({ isVisible, orderData, onAccept, onReject, onClose, playSound }) => {
  return (
    <UnifiedOrderNotification
      isModal={true}
      isVisible={isVisible}
      orderData={orderData}
      onAccept={onAccept}
      onReject={onReject}
      onClose={onClose}
      playSound={playSound}
    />
  );
};

// Default export for Screen usage (used by Expo Router)
function OrderNotificationScreen() {
  return <UnifiedOrderNotification />;
}

export default OrderNotificationScreen;