import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export interface StorageAdapter {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
}

/**
 * Secure storage for sensitive data like tokens
 * Uses SecureStore on native platforms and localStorage on web
 */
export const secureStorage: StorageAdapter = {
  async getItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        return localStorage.getItem(key);
      }
      // Use SecureStore on native platforms
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      console.error('SecureStorage getItem error:', error);
      return null;
    }
  },

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        localStorage.setItem(key, value);
        return;
      }
      // Use SecureStore on native platforms
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      console.error('SecureStorage setItem error:', error);
      throw error;
    }
  },

  async removeItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        // Use localStorage on web
        localStorage.removeItem(key);
        return;
      }
      // Use SecureStore on native platforms
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error('SecureStorage removeItem error:', error);
      throw error;
    }
  },
};

/**
 * Regular storage for non-sensitive data
 * Uses AsyncStorage on native platforms and localStorage on web
 */
export const regularStorage: StorageAdapter = {
  async getItem(key: string): Promise<string | null> {
    try {
      if (Platform.OS === 'web') {
        return localStorage.getItem(key);
      }
      return await AsyncStorage.getItem(key);
    } catch (error) {
      console.error('RegularStorage getItem error:', error);
      return null;
    }
  },

  async setItem(key: string, value: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem(key, value);
        return;
      }
      await AsyncStorage.setItem(key, value);
    } catch (error) {
      console.error('RegularStorage setItem error:', error);
      throw error;
    }
  },

  async removeItem(key: string): Promise<void> {
    try {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key);
        return;
      }
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('RegularStorage removeItem error:', error);
      throw error;
    }
  },
};

/**
 * Storage adapter for Zustand with automatic fallback
 */
export const createZustandStorage = (useSecure: boolean = false): StorageAdapter => {
  const storage = useSecure ? secureStorage : regularStorage;
  
  return {
    getItem: async (key: string) => {
      try {
        return await storage.getItem(key);
      } catch (error) {
        console.error(`Storage getItem error for key "${key}":`, error);
        return null;
      }
    },
    setItem: async (key: string, value: string) => {
      try {
        await storage.setItem(key, value);
      } catch (error) {
        console.error(`Storage setItem error for key "${key}":`, error);
        // Don't throw to prevent app crashes
      }
    },
    removeItem: async (key: string) => {
      try {
        await storage.removeItem(key);
      } catch (error) {
        console.error(`Storage removeItem error for key "${key}":`, error);
        // Don't throw to prevent app crashes
      }
    },
  };
}; 