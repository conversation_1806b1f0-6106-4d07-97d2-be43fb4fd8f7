if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "C:/Users/<USER>/.gradle/caches/8.13/transforms/df75d6af1b7fa262029bb3cf0826a17c/transformed/hermes-android-0.79.5-release/prefab/modules/libhermes/libs/android.x86_64/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "C:/Users/<USER>/.gradle/caches/8.13/transforms/df75d6af1b7fa262029bb3cf0826a17c/transformed/hermes-android-0.79.5-release/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

