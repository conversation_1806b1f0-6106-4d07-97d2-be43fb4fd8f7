import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useOptimizedNewOrders, useAcceptOrder, useRejectOrder } from '../hooks/useOrders';
import { Order } from '../api/orders';
import { OrderNotificationModal } from '../../app/order-notification';
import { soundService } from '../services/SoundService';

interface ApiNotificationContextType {
  /** True while the provider is actively polling the API */
  isPolling: boolean;
  /** Number of orders currently waiting for user action */
  pendingCount: number;
  /** Enable / disable API-based notifications (future use) */
  isEnabled: boolean;
  /** Whether at least one order notification modal is currently visible */
  isModalVisible: boolean;
}

const ApiNotificationContext = createContext<ApiNotificationContextType | undefined>(undefined);

export const ApiNotificationProvider: React.FC<{ children: React.ReactNode; enabled?: boolean }> = ({ children, enabled = true }) => {
  /** React-Query hook that fetches new orders every 5 seconds */
  const {
    data: newOrders = [],
    isLoading,
  } = useOptimizedNewOrders();

  // Mutations for accept / reject
  const acceptMutation = useAcceptOrder();
  const rejectMutation = useRejectOrder();

  /** A queue containing orders that still require user interaction */
  const [orderQueue, setOrderQueue] = useState<Order[]>([]);

  /** Set of order ids we have already showed to the user.  This prevents duplicate modals */
  const seenIdsRef = useRef<Set<string>>(new Set());

  // Initialize sound service once
  useEffect(() => {
    soundService.initialize();
  }, []);

  // Control alert sound based on queue length
  useEffect(() => {
    if (orderQueue.length > 0) {
      if (!soundService.getIsPlaying()) {
        soundService.playOrderAlert();
      }
    } else {
      soundService.stopAlert();
    }
  }, [orderQueue.length]);

  /** Detect brand-new orders returned from the API */
  useEffect(() => {
    if (!enabled || isLoading) return;

    const incomingIds = new Set<string>(newOrders.map((o) => o.order_id));
    const unseenOrders = newOrders.filter((o) => !seenIdsRef.current.has(o.order_id));

    if (unseenOrders.length > 0) {
      // Append unseen orders to local queue
      setOrderQueue((prev) => [...prev, ...unseenOrders]);

      // Mark them as seen so we do not show duplicates on next poll
      unseenOrders.forEach((o) => seenIdsRef.current.add(o.order_id));
    }

    // Clean up seenIds for orders that disappeared from API (accepted/rejected elsewhere)
    // This keeps the set from growing indefinitely.
    seenIdsRef.current.forEach((id) => {
      if (!incomingIds.has(id)) {
        seenIdsRef.current.delete(id);
      }
    });
  }, [newOrders, isLoading, enabled]);

  /** Handlers invoked from the modal */
  const handleAccept = (orderId: string) => {
    acceptMutation.mutate(orderId, {
      onSuccess: () => {
        setOrderQueue((q) => q.filter((o) => o.order_id !== orderId));
      },
      onError: () => {
        // keep in queue so user can retry in next poll
      },
    });
  };

  const handleReject = (orderId: string, reason: string) => {
    rejectMutation.mutate(
      { order_id: orderId, rejected_reason: reason },
      {
        onSuccess: () => {
          setOrderQueue((q) => q.filter((o) => o.order_id !== orderId));
        },
        onError: () => {
          // keep order in queue for retry
        },
      }
    );
  };

  const contextValue: ApiNotificationContextType = useMemo(
    () => ({
      isPolling: enabled,
      pendingCount: orderQueue.length,
      isEnabled: enabled,
      isModalVisible: orderQueue.length > 0,
    }),
    [orderQueue.length, enabled]
  );

  const currentOrder = orderQueue[0] ?? null;

  return (
    <ApiNotificationContext.Provider value={contextValue}>
      {children}

      {/* Render the modal on top of everything else */}
      {currentOrder && (
        <OrderNotificationModal
          key={currentOrder.order_id}
          isVisible={true}
          orderData={currentOrder}
          onAccept={handleAccept}
          onReject={handleReject}
          playSound={false}
          onClose={() => {
            /* users cannot close without action, but keep handler for completeness */
          }}
        />
      )}
    </ApiNotificationContext.Provider>
  );
};

export const useApiNotification = (): ApiNotificationContextType => {
  const ctx = useContext(ApiNotificationContext);
  if (!ctx) {
    throw new Error('useApiNotification must be used within ApiNotificationProvider');
  }
  return ctx;
}; 