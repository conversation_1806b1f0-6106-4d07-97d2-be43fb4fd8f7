<libraries>
  <library
      name=":@@:react-native-screens::release"
      project=":react-native-screens"/>
  <library
      name="com.facebook.react:react-android:0.79.5:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9616061a70c48f7f6f08941ffa751889\transformed\react-android-0.79.5-release\jars\classes.jar"
      resolved="com.facebook.react:react-android:0.79.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9616061a70c48f7f6f08941ffa751889\transformed\react-android-0.79.5-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c674cd16f9f5e5f23c9c3b7e3a3f7588\transformed\fresco-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c674cd16f9f5e5f23c9c3b7e3a3f7588\transformed\fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8028197b572d896dd9f8b8804aa0fd09\transformed\imagepipeline-okhttp3-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8028197b572d896dd9f8b8804aa0fd09\transformed\imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b10410589cd543d28a5071420020c9e\transformed\middleware-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4b10410589cd543d28a5071420020c9e\transformed\middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c93d95e280558f6ca9e7696f6cbd2cdc\transformed\ui-common-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c93d95e280558f6ca9e7696f6cbd2cdc\transformed\ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo::release"
      project=":expo"/>
  <library
      name=":@@:expo-modules-core::release"
      project=":expo-modules-core"/>
  <library
      name=":@@:expo-dev-launcher::release"
      project=":expo-dev-launcher"/>
  <library
      name=":@@:expo-dev-menu::release"
      project=":expo-dev-menu"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\daf877faecb4db893e000faf757056c7\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\daf877faecb4db893e000faf757056c7\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:react-native-onesignal::release"
      project=":react-native-onesignal"/>
  <library
      name="com.onesignal:OneSignal:5.1.34@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bb258541fbedb11686b9bf73f0f40985\transformed\OneSignal-5.1.34\jars\classes.jar"
      resolved="com.onesignal:OneSignal:5.1.34"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bb258541fbedb11686b9bf73f0f40985\transformed\OneSignal-5.1.34"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.onesignal:core:5.1.34@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34\jars\classes.jar"
      resolved="com.onesignal:core:5.1.34"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\28e2e4022d5642a2bd04b039ca3e3720\transformed\core-5.1.34"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\281993a8081a7f8ed7163ce2e30584f1\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\281993a8081a7f8ed7163ce2e30584f1\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c82e9805d9c0ecc3ad31126f1e52a80a\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.device:7.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\252080c1424b57af4d7507d029d8591a\transformed\expo.modules.device-7.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.device:7.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\252080c1424b57af4d7507d029d8591a\transformed\expo.modules.device-7.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.filesystem:18.1.11@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.filesystem:18.1.11"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\425ec90b65e73b017ca6cf65de3aab66\transformed\expo.modules.filesystem-18.1.11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.sharing:13.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.sharing:13.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\64b200bace57dff47ed5613c2dfc0d04\transformed\expo.modules.sharing-13.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\232d1aafd621ebff9ee1ba890f8f51a7\transformed\legacy-support-v4-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\232d1aafd621ebff9ee1ba890f8f51a7\transformed\legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.securestore:14.2.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\697d0f2cfe938fbbebe2f2bc2e226e02\transformed\expo.modules.securestore-14.2.3\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.securestore:14.2.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\697d0f2cfe938fbbebe2f2bc2e226e02\transformed\expo.modules.securestore-14.2.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4d7ecf19998ab3515e1b90137c4d08f\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.onesignal:notifications:5.1.34@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34\jars\classes.jar"
      resolved="com.onesignal:notifications:5.1.34"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\adc098907c8c12277553eb0c43882764\transformed\notifications-5.1.34"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-messaging:23.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-messaging:23.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2637c9752b5f0a14974ab86e1a0fe302\transformed\firebase-messaging-23.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-iid-interop:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9493e592fdc0c5b702540e3bb73f9835\transformed\firebase-iid-interop-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\02a6c65af6aca4fe0b17434c7fe8f02b\transformed\play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-cloud-messaging:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\89e885885a77e10da35362a70041edb7\transformed\play-services-cloud-messaging-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations:17.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations:17.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\efb232a5a4e68efb6f7e9b81f4645a5e\transformed\firebase-installations-17.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ef79d754d050ead123a8dc574816b32\transformed\firebase-installations-interop-17.1.1\jars\classes.jar"
      resolved="com.google.firebase:firebase-installations-interop:17.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ef79d754d050ead123a8dc574816b32\transformed\firebase-installations-interop-17.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common-ktx:20.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1cdaf93c1c774d7cdee1ebd41704afe8\transformed\firebase-common-ktx-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-common:20.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2\jars\classes.jar"
      resolved="com.google.firebase:firebase-common:20.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e1fef0fa644660ebe92e5ecd8911a1e7\transformed\firebase-common-20.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\89ea14f423ad5c8e358b53e4a9b81859\transformed\activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\89ea14f423ad5c8e358b53e4a9b81859\transformed\activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\13722947dc5f00d0d1a1c9b2d82d5407\transformed\legacy-support-core-ui-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\13722947dc5f00d0d1a1c9b2d82d5407\transformed\legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\356fc999dba310762fcb51e029991c71\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\356fc999dba310762fcb51e029991c71\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.onesignal:in-app-messages:5.1.34@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\94b47daf1560bb780136208263a881b4\transformed\in-app-messages-5.1.34\jars\classes.jar"
      resolved="com.onesignal:in-app-messages:5.1.34"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\94b47daf1560bb780136208263a881b4\transformed\in-app-messages-5.1.34"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.browser:browser:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\47c4be2db69016738bbafb806598f314\transformed\browser-1.6.0\jars\classes.jar"
      resolved="androidx.browser:browser:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\47c4be2db69016738bbafb806598f314\transformed\browser-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\83d51b6e88007b5429952582adf6ad94\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\83d51b6e88007b5429952582adf6ad94\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\942d929df50e13a06eea64c3f56ce2e6\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\942d929df50e13a06eea64c3f56ce2e6\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc83fc3662b1f056d8c5fcf6cc0b2ce8\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc83fc3662b1f056d8c5fcf6cc0b2ce8\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.av:15.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\df7d784b1be5e2369f95f298bb989f58\transformed\expo.modules.av-15.1.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.av:15.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\df7d784b1be5e2369f95f298bb989f58\transformed\expo.modules.av-15.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\32a69fcdfcfaf951b7d4c1b7aa539671\transformed\exoplayer-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\32a69fcdfcfaf951b7d4c1b7aa539671\transformed\exoplayer-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7b1efdd330a6997d521c41b8a19879f0\transformed\exoplayer-ui-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7b1efdd330a6997d521c41b8a19879f0\transformed\exoplayer-ui-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.4.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\88f934b05d112786789c29637c6dc478\transformed\media-1.4.3\jars\classes.jar"
      resolved="androidx.media:media:1.4.3"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\88f934b05d112786789c29637c6dc478\transformed\media-1.4.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-stats:17.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-stats:17.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\14699fb8d7f0051cbab7d2b5cadb26cf\transformed\play-services-stats-17.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\10f2e59836afcfaeb7024ffa9f730642\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\10f2e59836afcfaeb7024ffa9f730642\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f83150c8cd0912cbab709d7028c29e4\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8f83150c8cd0912cbab709d7028c29e4\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5799eb2ead5366c18900bdc6eb607ded\transformed\coordinatorlayout-1.2.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5799eb2ead5366c18900bdc6eb607ded\transformed\coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ad04b768963391ba6665aa73cb02336d\transformed\slidingpanelayout-1.0.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ad04b768963391ba6665aa73cb02336d\transformed\slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb3414a5b9006ede02e6334a5c1c6e5\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1bb3414a5b9006ede02e6334a5c1c6e5\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a337a35254edb8a41af5a93dcd364b0a\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a337a35254edb8a41af5a93dcd364b0a\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\60de6752652a833c9a313b58ddaff005\transformed\asynclayoutinflater-1.0.0\jars\classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\60de6752652a833c9a313b58ddaff005\transformed\asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\62900a8129d9ab3abaa7c7365313cbcb\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\946d327f7d72e200149bb16e5512a317\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\946d327f7d72e200149bb16e5512a317\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc9f8e9a1a6d58dfeddb1833d8dc0f9\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fc9f8e9a1a6d58dfeddb1833d8dc0f9\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\385207845dcfb3956e3a4c461df893df\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\385207845dcfb3956e3a4c461df893df\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ce4db16154b1473cd097236076b5c4a\transformed\lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8ce4db16154b1473cd097236076b5c4a\transformed\lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c8201ea65a60642aef04e8f655662b3e\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\426cb95f49bf9e6d587bd5521f699ec0\transformed\work-runtime-2.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\68ac8370b68d7cf5c72cf3ece9b72580\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\68ac8370b68d7cf5c72cf3ece9b72580\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3236732610ecd0bdf86bfde650060d85\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3236732610ecd0bdf86bfde650060d85\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-play-services\1.7.3\7087d47913cfb0062c9909dacbfc78fe44c5ecff\kotlinx-coroutines-play-services-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd4c1e6f7b43fc2e3da945397e86e157\transformed\play-services-tasks-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-measurement-connector:19.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7be5211f7cb8a65783e9723251c88d3a\transformed\firebase-measurement-connector-19.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4689ac814784dd79f2206f332baa7585\transformed\play-services-basement-18.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\39189d1f65b3b9d5d51083ae6ee8e753\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\39189d1f65b3b9d5d51083ae6ee8e753\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\49e80fdcd82127bd0c9c0544df1e2cfa\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\49e80fdcd82127bd0c9c0544df1e2cfa\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc8aa523a6c477e084b3ea9cd8fb499\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc8aa523a6c477e084b3ea9cd8fb499\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa74fe9b56d39051c08965a002f0b9a\transformed\fbcore-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6fa74fe9b56d39051c08965a002f0b9a\transformed\fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\34d85fffb9c43b690472da434082af14\transformed\drawee-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\34d85fffb9c43b690472da434082af14\transformed\drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-urlconnection\4.9.2\3b9e64d3d56370bc7488ed8b336d17a8013cb336\okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.google.android.exoplayer:extension-okhttp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1735a5bdc5556b5fcd9734fc7bce3c\transformed\extension-okhttp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:extension-okhttp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1f1735a5bdc5556b5fcd9734fc7bce3c\transformed\extension-okhttp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="io.insert-koin:koin-core-jvm:3.5.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.insert-koin\koin-core-jvm\3.5.6\71c101744c62708690796cdb48ed8522a74687c7\koin-core-jvm-3.5.6.jar"
      resolved="io.insert-koin:koin-core-jvm:3.5.6"/>
  <library
      name="co.touchlab:stately-concurrent-collections-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrent-collections-jvm\2.0.6\fb80df9c69dd0e154c346ee5510601e2d148e23d\stately-concurrent-collections-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrent-collections-jvm:2.0.6"/>
  <library
      name="co.touchlab:stately-concurrency-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-concurrency-jvm\2.0.6\14dcbce3fc3d80a5a07f9df33dd2dc54e437e8d0\stately-concurrency-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-concurrency-jvm:2.0.6"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name=":@@:expo-constants::release"
      project=":expo-constants"/>
  <library
      name="host.exp.exponent:expo.modules.haptics:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.haptics:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5235b1141c9a83f252c5a59e0062b9d7\transformed\expo.modules.haptics-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-client::release"
      project=":expo-dev-client"/>
  <library
      name="androidx.databinding:viewbinding:8.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7b214cd1187ca8ca8f548d8878a9b523\transformed\viewbinding-8.8.2\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7b214cd1187ca8ca8f548d8878a9b523\transformed\viewbinding-8.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5e440d6d7f8b81f1ee5821c2f7734b6c\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5e440d6d7f8b81f1ee5821c2f7734b6c\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c942b0acc1f7702ab490f1c891f0cfb5\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c942b0acc1f7702ab490f1c891f0cfb5\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cefce8cad880e9116d74623f30bb2411\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cefce8cad880e9116d74623f30bb2411\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\02014e6ae21bddbc8831a1642129fc1d\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\02014e6ae21bddbc8831a1642129fc1d\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\024a6d12fcdce3f1b72cd8a34e5f1401\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.2.0\34dbc21d203cc4d4d623ac572a21acd4ccd716af\collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\982380dbe80441682c717ab01f65a7f9\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\982380dbe80441682c717ab01f65a7f9\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\76679a59b9f9ae735a48e7d215f630b1\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\76679a59b9f9ae735a48e7d215f630b1\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\353c17a77463ee884dbc33275c068645\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\353c17a77463ee884dbc33275c068645\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-datatransport:18.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7\jars\classes.jar"
      resolved="com.google.firebase:firebase-datatransport:18.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d2b1e77572509a6ac4acb600cffe9be\transformed\firebase-datatransport-18.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6c033124e74b990323ef4e97fe3f50d7\transformed\transport-backend-cct-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:3.1.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e27f382b5dc2e6a5d2766c67c8850f8f\transformed\transport-runtime-3.1.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders-proto\16.0.0\a42d5fd83b96ae7b73a8617d29c94703e18c9992\firebase-encoders-proto-16.0.0.jar"
      resolved="com.google.firebase:firebase-encoders-proto:16.0.0"/>
  <library
      name="com.google.android.datatransport:transport-api:3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7db2b7a71f5dbcd5883ac91edcd6931d\transformed\transport-api-3.1.0\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7db2b7a71f5dbcd5883ac91edcd6931d\transformed\transport-api-3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6937096d739cfeb784d5c810ef736db\transformed\firebase-encoders-json-18.0.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:18.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6937096d739cfeb784d5c810ef736db\transformed\firebase-encoders-json-18.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:17.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\17.0.0\26f52dc549c42575b155f8c720e84059ee600a85\firebase-encoders-17.0.0.jar"
      resolved="com.google.firebase:firebase-encoders:17.0.0"/>
  <library
      name="com.google.firebase:firebase-components:17.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\07a750665b475a53f1748cb783f578fd\transformed\firebase-components-17.1.5\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:17.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\07a750665b475a53f1748cb783f578fd\transformed\firebase-components-17.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.8.1\b8a16fe526014b7941c1debaccaf9c5153692dbb\annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc14df0b6916a817c4ff4d00c27f2b4\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7bc14df0b6916a817c4ff4d00c27f2b4\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0caca4f943fdf24a419b6298848cfc96\transformed\ui-core-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0caca4f943fdf24a419b6298848cfc96\transformed\ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\993e9886b0746368e368e024aca617d1\transformed\imagepipeline-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\993e9886b0746368e368e024aca617d1\transformed\imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\973fffd5662755d4e69b886e239d1df3\transformed\imagepipeline-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\973fffd5662755d4e69b886e239d1df3\transformed\imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\2.0.21\222b2be42672d47c002c1b22ac9f030d781fc5db\kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      project=":react-native-async-storage_async-storage"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      project=":react-native-gesture-handler"/>
  <library
      name=":@@:react-native-reanimated::release"
      project=":react-native-reanimated"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      project=":react-native-safe-area-context"/>
  <library
      name=":@@:react-native-svg::release"
      project=":react-native-svg"/>
  <library
      name="com.facebook.fresco:animated-gif:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e536ed9c62de1fa526e47192528d03\transformed\animated-gif-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e536ed9c62de1fa526e47192528d03\transformed\animated-gif-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\595ddea61318adcd7bdc9d5a52426fe2\transformed\webpsupport-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\595ddea61318adcd7bdc9d5a52426fe2\transformed\webpsupport-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.79.5:release@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\df75d6af1b7fa262029bb3cf0826a17c\transformed\hermes-android-0.79.5-release\jars\classes.jar"
      resolved="com.facebook.react:hermes-android:0.79.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\df75d6af1b7fa262029bb3cf0826a17c\transformed\hermes-android-0.79.5-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\257ac79b8ae7366202e9abfc2076c6f2\transformed\exoplayer-core-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\86a4969581c6bafb61efe441a5ab92b7\transformed\exoplayer-common-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:33.0.0-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.0.0-android\cfbbdc54f232feedb85746aeeea0722f5244bb9a\guava-33.0.0-android.jar"
      resolved="com.google.guava:guava:33.0.0-android"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\96639698415dd61bd95573038ad7be02\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2727b732b2ccccec1ebeef9a3638abbf\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2727b732b2ccccec1ebeef9a3638abbf\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\68b695252e1a975cc49d4b6bc33f0c98\transformed\autofill-1.1.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\68b695252e1a975cc49d4b6bc33f0c98\transformed\autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7f5330b574122aa672d96a0f5922e634\transformed\fbjni-0.7.0\jars\classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7f5330b574122aa672d96a0f5922e634\transformed\fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1\jars\classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a58b0f0b17062398abaef72c7c038ec3\transformed\soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\nativeloader\0.12.1\492cc5082540e19b29328f2f56c53255cb6e7cc6\nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.soloader\annotation\0.12.1\945ada76f62253ba8e72cbf755d0e85ea7362cfe\annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.infer.annotation\infer-annotation\0.18.0\27539793fe93ed7d92b6376281c16cda8278ab2f\infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-annotations-jvm\1.3.72\7dba6c57de526588d8080317bda0c14cd88c8055\kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb15aeac3381993b7b6be5a355bfea61\transformed\imagepipeline-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb15aeac3381993b7b6be5a355bfea61\transformed\imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\200e032c88fe4eeaab6cfc9d3de54cb0\transformed\memory-type-ashmem-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\200e032c88fe4eeaab6cfc9d3de54cb0\transformed\memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc548028e1fe578ec6cdb8cf56f9a06c\transformed\memory-type-native-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fc548028e1fe578ec6cdb8cf56f9a06c\transformed\memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab7e0c4acc242ba6899988208e639af\transformed\memory-type-java-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab7e0c4acc242ba6899988208e639af\transformed\memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\280f1670cd45c825a6613a5172c11f7a\transformed\nativeimagefilters-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\280f1670cd45c825a6613a5172c11f7a\transformed\nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\41e7aa391a69310f9c865af9efcc14ac\transformed\nativeimagetranscoder-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\41e7aa391a69310f9c865af9efcc14ac\transformed\nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.yoga\proguard-annotations\1.19.0\fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63\proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="com.google.firebase:firebase-annotations:16.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.2.0\ba0806703ca285d03fa9c888b5868f101134a501\firebase-annotations-16.2.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.2.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.6\815893df5f31da2ece4040fe0a12fd44b577afaf\commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.15.0\38c8485a652f808c8c149150da4e5c2b0bd17f9a\error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="expo.modules.asset:expo.modules.asset:11.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd9d7ba25d1438208f96157ddcd6e358\transformed\expo.modules.asset-11.1.7\jars\classes.jar"
      resolved="expo.modules.asset:expo.modules.asset:11.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dd9d7ba25d1438208f96157ddcd6e358\transformed\expo.modules.asset-11.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="expo.modules.audio:expo.modules.audio:0.4.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8\jars\classes.jar"
      resolved="expo.modules.audio:expo.modules.audio:0.4.8"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\43e85091a278c47cabe7e7a972848fbb\transformed\expo.modules.audio-0.4.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.2\c4a06a64e650562f30b7bf9aaec1bfed43aca12b\failureaccess-1.0.2.jar"
      resolved="com.google.guava:failureaccess:1.0.2"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\efe3a061c6deb0a5c90e5593eb0157f8\transformed\exoplayer-database-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\efe3a061c6deb0a5c90e5593eb0157f8\transformed\exoplayer-database-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd49a2bc21d7481306f5fab22b13b93\transformed\exoplayer-datasource-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fd49a2bc21d7481306f5fab22b13b93\transformed\exoplayer-datasource-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\98254bf55b4a99883211a00c74ee064c\transformed\exoplayer-decoder-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\98254bf55b4a99883211a00c74ee064c\transformed\exoplayer-decoder-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\819b0f19fd7a4e0628a596acf3c0edf7\transformed\exoplayer-extractor-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\819b0f19fd7a4e0628a596acf3c0edf7\transformed\exoplayer-extractor-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\93599d426d717b29c71811f738de6550\transformed\exoplayer-dash-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\93599d426d717b29c71811f738de6550\transformed\exoplayer-dash-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbb53b0031d6b8cf2af31614c779d6c\transformed\exoplayer-hls-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2fbb53b0031d6b8cf2af31614c779d6c\transformed\exoplayer-hls-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae76c217763a0357fb15e4bf3b4067b3\transformed\exoplayer-rtsp-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae76c217763a0357fb15e4bf3b4067b3\transformed\exoplayer-rtsp-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\30816eecce2e37d429d2df995908dc08\transformed\exoplayer-smoothstreaming-2.18.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\30816eecce2e37d429d2df995908dc08\transformed\exoplayer-smoothstreaming-2.18.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.blur:14.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\79abbfa205322d40c6b84839f63416b0\transformed\expo.modules.blur-14.1.5\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.blur:14.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\79abbfa205322d40c6b84839f63416b0\transformed\expo.modules.blur-14.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.device.yearclass:yearclass:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.facebook.device.yearclass\yearclass\2.1.0\ef7d013a0140137b4a948dd65b46a08205d21020\yearclass-2.1.0.jar"
      resolved="com.facebook.device.yearclass:yearclass:2.1.0"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.10\4b95f4897fa13f2cd904aee711aeafc0c5295cd8\commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="host.exp.exponent:expo.modules.font:13.3.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\18d4c57a39b6281cb2c7f9a0213928ae\transformed\expo.modules.font-13.3.2\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.font:13.3.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\18d4c57a39b6281cb2c7f9a0213928ae\transformed\expo.modules.font-13.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a45eaa01c94036bfce1be4073840d76\transformed\expo.modules.keepawake-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.keepawake:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0a45eaa01c94036bfce1be4073840d76\transformed\expo.modules.keepawake-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.lineargradient:14.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\469c4df5952573b4ed36c6c2062c1133\transformed\expo.modules.lineargradient-14.1.5\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.lineargradient:14.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\469c4df5952573b4ed36c6c2062c1133\transformed\expo.modules.lineargradient-14.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.linking:7.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0ce2567ccc087fb22aa7b99b1e0462\transformed\expo.modules.linking-7.1.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.linking:7.1.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0ce2567ccc087fb22aa7b99b1e0462\transformed\expo.modules.linking-7.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.navigationbar:4.2.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b01e470a716b449ca6703c2e0cec7372\transformed\expo.modules.navigationbar-4.2.7\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.navigationbar:4.2.7"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b01e470a716b449ca6703c2e0cec7372\transformed\expo.modules.navigationbar-4.2.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.print:14.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2862bc9df2a2ab0fbb5fd0f17196117\transformed\expo.modules.print-14.1.4\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.print:14.1.4"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f2862bc9df2a2ab0fbb5fd0f17196117\transformed\expo.modules.print-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.splashscreen:0.30.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b3c8037b98d96420bf52805fc30b2c\transformed\expo.modules.splashscreen-0.30.10\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.splashscreen:0.30.10"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a3b3c8037b98d96420bf52805fc30b2c\transformed\expo.modules.splashscreen-0.30.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.systemui:5.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1fcf9a7e2fdee1cbfa45ddc2de3118\transformed\expo.modules.systemui-5.0.10\jars\classes.jar"
      resolved="host.exp.exponent:expo.modules.systemui:5.0.10"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9d1fcf9a7e2fdee1cbfa45ddc2de3118\transformed\expo.modules.systemui-5.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.onesignal:location:5.1.34@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f42d1a13e414193dbd93f5ef64c4067f\transformed\location-5.1.34\jars\classes.jar"
      resolved="com.onesignal:location:5.1.34"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f42d1a13e414193dbd93f5ef64c4067f\transformed\location-5.1.34"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-dev-menu-interface::release"
      project=":expo-dev-menu-interface"/>
  <library
      name=":@@:expo-json-utils::release"
      project=":expo-json-utils"/>
  <library
      name=":@@:expo-manifests::release"
      project=":expo-manifests"/>
  <library
      name=":@@:expo-updates-interface::release"
      project=":expo-updates-interface"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fd29036936cb81e953b907f3a3c29b2f\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.8.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\73a7775f35e417780184b8f501ba2117\transformed\work-runtime-ktx-2.8.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.8.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\73a7775f35e417780184b8f501ba2117\transformed\work-runtime-ktx-2.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-splashscreen:1.2.0-alpha02@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc2afe36c727cd4c319cac1b77229cf\transformed\core-splashscreen-1.2.0-alpha02\jars\classes.jar"
      resolved="androidx.core:core-splashscreen:1.2.0-alpha02"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fcc2afe36c727cd4c319cac1b77229cf\transformed\core-splashscreen-1.2.0-alpha02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f310e5539327a120ba8c9ec23e648205\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a24e10b8c009f789fedc6ea50f1b85c\transformed\fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4a24e10b8c009f789fedc6ea50f1b85c\transformed\fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7351d3833f13a2cc542fe59def656210\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7351d3833f13a2cc542fe59def656210\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\53a88349e37837a6cee380b95f0f29f7\transformed\activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\53a88349e37837a6cee380b95f0f29f7\transformed\activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\db0bff994c226a93b8e8ded58ee81cbc\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\db0bff994c226a93b8e8ded58ee81cbc\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\432c227539738b0a6fa92207b5f4f4e6\transformed\animated-base-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-base:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\432c227539738b0a6fa92207b5f4f4e6\transformed\animated-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2555262b72297a642e7a95d8688571b9\transformed\animated-drawable-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2555262b72297a642e7a95d8688571b9\transformed\animated-drawable-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\66a30dd3c9c373083e00c82f964d10f3\transformed\vito-options-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-options:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\66a30dd3c9c373083e00c82f964d10f3\transformed\vito-options-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\92383f2905d19505013069f0bdc93d86\transformed\urimod-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\92383f2905d19505013069f0bdc93d86\transformed\urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d8a2a37b30a9c89433a23bfbe2c62e\transformed\vito-source-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d0d8a2a37b30a9c89433a23bfbe2c62e\transformed\vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\84c2e1fdf67a20a30b80c97a36fac620\transformed\soloader-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\84c2e1fdf67a20a30b80c97a36fac620\transformed\soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource-okhttp:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\06e5a4bc13c42f302c7d1242ac46a49c\transformed\media3-datasource-okhttp-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-datasource-okhttp:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\06e5a4bc13c42f302c7d1242ac46a49c\transformed\media3-datasource-okhttp-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\211be35423f7bfaec0e55aefa1405712\transformed\media3-extractor-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\211be35423f7bfaec0e55aefa1405712\transformed\media3-extractor-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-container:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0392dcde0f5057ee09db92292159e852\transformed\media3-container-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0392dcde0f5057ee09db92292159e852\transformed\media3-container-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a220c0850ea7a4312f5767b5b3a47390\transformed\media3-datasource-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a220c0850ea7a4312f5767b5b3a47390\transformed\media3-datasource-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\103d981b5962056e853bb637be791128\transformed\media3-decoder-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\103d981b5962056e853bb637be791128\transformed\media3-decoder-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\529f1223e88b2da943ded65d9d311cec\transformed\media3-database-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\529f1223e88b2da943ded65d9d311cec\transformed\media3-database-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aade20f00a213d4b47ce0a9eb366adcd\transformed\media3-common-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-dash:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\af51e38e80ddf7c842cfbea983da50d3\transformed\media3-exoplayer-dash-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-dash:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\af51e38e80ddf7c842cfbea983da50d3\transformed\media3-exoplayer-dash-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-hls:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a3097707dc566d1228ea0db1842b762\transformed\media3-exoplayer-hls-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-hls:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8a3097707dc566d1228ea0db1842b762\transformed\media3-exoplayer-hls-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer-smoothstreaming:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae019187e5c516a5cb0b7ac11247c8c7\transformed\media3-exoplayer-smoothstreaming-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer-smoothstreaming:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae019187e5c516a5cb0b7ac11247c8c7\transformed\media3-exoplayer-smoothstreaming-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\250db4afb8fa7de0d997879e3d4adf3d\transformed\media3-exoplayer-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\448ca3090c033310e421ff7270af74f2\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\448ca3090c033310e421ff7270af74f2\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\675f4f7d07ab458ad78ae7586d1e5993\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\675f4f7d07ab458ad78ae7586d1e5993\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\504eeb7c7bcb8f63fc45f7fe459b21ca\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\504eeb7c7bcb8f63fc45f7fe459b21ca\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2ec9fa55b924e2bc0edbb680d7cb4cb9\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52de6cc1bf4e0cbce2ccf873236402e3\transformed\recyclerview-1.2.1\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52de6cc1bf4e0cbce2ccf873236402e3\transformed\recyclerview-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\900e1bfa4bca01d85722b25f0cd283a5\transformed\lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\900e1bfa4bca01d85722b25f0cd283a5\transformed\lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\417f62f3d94f17f3e5d09a4e5b5cb22f\transformed\lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\417f62f3d94f17f3e5d09a4e5b5cb22f\transformed\lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\484febe24aacd0df853b318a482220cb\transformed\lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\484febe24aacd0df853b318a482220cb\transformed\lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\52ba16b6904dc864b115fe678a12efe3\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="co.touchlab:stately-strict-jvm:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\co.touchlab\stately-strict-jvm\2.0.6\fdbcb1fc1c9219aa5a5f2b1c9084a4ed8b2a8f8b\stately-strict-jvm-2.0.6.jar"
      resolved="co.touchlab:stately-strict-jvm:2.0.6"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-reflect\2.0.21\669e1d35e4ca1797f9ddb2830dd6c36c0ca531e4\kotlin-reflect-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.0.21"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2d5f1c8d3de942a5eade573b54824329\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2ad778840a272b59e429623bbe3eda\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\cf2ad778840a272b59e429623bbe3eda\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6550963f3403c0d1d6c453e7b7e3228\transformed\vito-renderer-3.6.0\jars\classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.6.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f6550963f3403c0d1d6c453e7b7e3228\transformed\vito-renderer-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.Dimezis:BlurView:version-2.0.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6\jars\classes.jar"
      resolved="com.github.Dimezis:BlurView:version-2.0.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\91bd54ded411652c7267ded7f00ae22a\transformed\BlurView-version-2.0.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\af53d4f4b13e08684217b30f24915ec1\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\af53d4f4b13e08684217b30f24915ec1\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\072824e130dfb32121ac6c53e61c0c7e\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\072824e130dfb32121ac6c53e61c0c7e\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\207646751c7680dd4c2eebb78ef5b048\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\207646751c7680dd4c2eebb78ef5b048\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.parse.bolts\bolts-tasks\1.4.0\d85884acf6810a3bbbecb587f239005cbc846dc4\bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.code.gson:gson:2.8.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.6\9180733b7df8542621dc12e21e87557e8c99b8cb\gson-2.8.6.jar"
      resolved="com.google.code.gson:gson:2.8.6"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
</libraries>
